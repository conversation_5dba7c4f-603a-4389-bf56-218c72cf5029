<?php

namespace Shaqi\Tabby\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class TabbyApiService
{
    protected Client $client;
    protected ?string $baseUrl;
    protected ?string $secretKey;
    protected ?string $publicKey;
    protected ?string $merchantCode;
    protected bool $isTestMode;

    public function __construct()
    {
        $this->isTestMode = get_payment_setting('environment', TABBY_PAYMENT_METHOD_NAME, 'sandbox') === 'sandbox';
        $this->baseUrl = $this->isTestMode ? TABBY_API_BASE_URL_SANDBOX : TABBY_API_BASE_URL_LIVE;
        $this->secretKey = get_payment_setting('secret_key', TABBY_PAYMENT_METHOD_NAME) ?? '';
        $this->publicKey = get_payment_setting('public_key', TABBY_PAYMENT_METHOD_NAME) ?? '';
        $this->merchantCode = get_payment_setting('merchant_code', TABBY_PAYMENT_METHOD_NAME) ?? '';

        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 30,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
        ]);
    }

    /**
     * Create a checkout session
     */
    public function createCheckoutSession(array $paymentData): array
    {
        try {
            do_action('payment_before_making_api_request', TABBY_PAYMENT_METHOD_NAME, $paymentData);

            $response = $this->client->post('/api/v2/checkout', [
                'json' => $paymentData,
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                ],
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            do_action('payment_after_api_response', TABBY_PAYMENT_METHOD_NAME, $paymentData, $responseData);

            return $responseData;
        } catch (GuzzleException $exception) {

            throw $exception;
        }
    }

    /**
     * Retrieve payment status
     */
    public function retrievePayment(string $paymentId): array
    {
        try {
            $response = $this->client->get("/api/v2/payments/{$paymentId}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                ],
            ]);
            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $exception) {

            throw $exception;
        }
    }

    /**
     * Capture a payment
     */
    public function capturePayment(string $paymentId, array $captureData): array
    {
        try {
            do_action('payment_before_making_api_request', TABBY_PAYMENT_METHOD_NAME, $captureData);

            $response = $this->client->post("/api/v2/payments/{$paymentId}/captures", [
                'json' => $captureData,
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                ],
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            do_action('payment_after_api_response', TABBY_PAYMENT_METHOD_NAME, $captureData, $responseData);

            return $responseData;
        } catch (GuzzleException $exception) {

            throw $exception;
        }
    }

    /**
     * Refund a payment
     */
    public function refundPayment(string $paymentId, array $refundData): array
    {
        try {
            do_action('payment_before_making_api_request', TABBY_PAYMENT_METHOD_NAME, $refundData);

            $response = $this->client->post("/api/v2/payments/{$paymentId}/refunds", [
                'json' => $refundData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            do_action('payment_after_api_response', TABBY_PAYMENT_METHOD_NAME, $refundData, $responseData);

            return $responseData;
        } catch (GuzzleException $exception) {

            throw $exception;
        }
    }

    /**
     * Register webhook
     */
    public function registerWebhook(string $webhookUrl, ?array $authHeader = null): array
    {
        try {
            $data = [
                'url' => $webhookUrl,
                'is_test' => $this->isTestMode,
            ];

            if ($authHeader) {
                $data['header'] = $authHeader;
            }

            $response = $this->client->post('/api/v1/webhooks', [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                    'X-Merchant-Code' => $this->merchantCode,
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $exception) {
            throw $exception;
        }
    }

    /**
     * Retrieve all webhooks
     */
    public function getAllWebhooks(): array
    {
        try {
            $response = $this->client->get('/api/v1/webhooks', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                    'X-Merchant-Code' => $this->merchantCode,
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $exception) {
            throw $exception;
        }
    }

    /**
     * Retrieve a specific webhook
     */
    public function getWebhook(string $webhookId): array
    {
        try {
            $response = $this->client->get("/api/v1/webhooks/{$webhookId}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                    'X-Merchant-Code' => $this->merchantCode,
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $exception) {
            throw $exception;
        }
    }

    /**
     * Update webhook
     */
    public function updateWebhook(string $webhookId, string $webhookUrl, ?array $authHeader = null): array
    {
        try {
            $data = [
                'url' => $webhookUrl,
                'is_test' => $this->isTestMode,
            ];

            if ($authHeader) {
                $data['header'] = $authHeader;
            }

            $response = $this->client->put("/api/v1/webhooks/{$webhookId}", [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                    'X-Merchant-Code' => $this->merchantCode,
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $exception) {
            throw $exception;
        }
    }

    /**
     * Remove webhook
     */
    public function removeWebhook(string $webhookId): array
    {
        try {
            $response = $this->client->delete("/api/v1/webhooks/{$webhookId}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->secretKey,
                    'X-Merchant-Code' => $this->merchantCode,
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $exception) {
            throw $exception;
        }
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        // Based on Tabby documentation, these are the main supported currencies
        return ['AED', 'SAR', 'KWD', 'BHD', 'QAR', 'EGP'];
    }

    /**
     * Get merchant code
     */
    public function getMerchantCode(): string
    {
        return $this->merchantCode;
    }

    /**
     * Check if API is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->secretKey) && !empty($this->publicKey) && !empty($this->merchantCode);
    }

    /**
     * Check if in test mode
     */
    public function isTestMode(): bool
    {
        return $this->isTestMode;
    }
}
