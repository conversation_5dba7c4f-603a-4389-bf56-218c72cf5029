{"__meta": {"id": "01K3V7JZJ3FXYQA64N5BZDKN1W", "datetime": "2025-08-29 15:36:54", "utime": **********.084166, "method": "PUT", "uri": "/admin/ecommerce/settings/branch-management", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.026613, "end": **********.084185, "duration": 2.0575718879699707, "duration_str": "2.06s", "measures": [{"label": "Booting", "start": **********.026613, "relative_start": 0, "end": **********.794515, "relative_end": **********.794515, "duration": 0.****************, "duration_str": "768ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.794524, "relative_start": 0.****************, "end": **********.084187, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.812384, "relative_start": 0.****************, "end": **********.822491, "relative_end": **********.822491, "duration": 0.010107040405273438, "duration_str": "10.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.079561, "relative_start": 2.***************, "end": **********.081313, "relative_end": **********.081313, "duration": 0.0017518997192382812, "duration_str": "1.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 260, "nb_statements": 260, "nb_visible_statements": 260, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.0111100000000004, "accumulated_duration_str": "1.01s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 160 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.837915, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 0.048}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.842452, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 0.048, "width_percent": 0.035}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.848197, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.083, "width_percent": 0.064}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.868284, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:45", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=45", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "45"}, "connection": "martfury", "explain": null, "start_percent": 0.147, "width_percent": 0.133}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'api_enabled'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:52", "api_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.871454, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 0.28, "width_percent": 0.406}, {"sql": "update `settings` set `value` = '[\\\"language\\\",\\\"language-advanced\\\",\\\"ads\\\",\\\"analytics\\\",\\\"audit-log\\\",\\\"backup\\\",\\\"blog\\\",\\\"captcha\\\",\\\"contact\\\",\\\"cookie-consent\\\",\\\"ecommerce\\\",\\\"faq\\\",\\\"location\\\",\\\"marketplace\\\",\\\"mollie\\\",\\\"newsletter\\\",\\\"payment\\\",\\\"paypal\\\",\\\"paypal-payout\\\",\\\"paystack\\\",\\\"razorpay\\\",\\\"shippo\\\",\\\"simple-slider\\\",\\\"social-login\\\",\\\"sslcommerz\\\",\\\"stripe\\\",\\\"stripe-connect\\\",\\\"translation\\\",\\\"cmi\\\",\\\"fob-email-log\\\",\\\"google-merchant-feed\\\",\\\"paymob\\\",\\\"quote\\\",\\\"tabby\\\",\\\"hyperpay\\\",\\\"aramex\\\",\\\"branch-management\\\"]', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'activated_plugins'", "type": "query", "params": [], "bindings": ["[\"language\",\"language-advanced\",\"ads\",\"analytics\",\"audit-log\",\"backup\",\"blog\",\"captcha\",\"contact\",\"cookie-consent\",\"ecommerce\",\"faq\",\"location\",\"marketplace\",\"mollie\",\"newsletter\",\"payment\",\"paypal\",\"paypal-payout\",\"paystack\",\"razorpay\",\"shippo\",\"simple-slider\",\"social-login\",\"sslcommerz\",\"stripe\",\"stripe-connect\",\"translation\",\"cmi\",\"fob-email-log\",\"google-merchant-feed\",\"paymob\",\"quote\",\"tabby\",\"hyperpay\",\"aramex\",\"branch-management\"]", "2025-08-29 15:36:52", "activated_plugins"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.876762, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 0.686, "width_percent": 0.387}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'analytics_dashboard_widgets'", "type": "query", "params": [], "bindings": ["0", "2025-08-29 15:36:52", "analytics_dashboard_widgets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.882132, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 1.073, "width_percent": 0.427}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'enable_recaptcha_botble_contact_forms_fronts_contact_form'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:52", "enable_recaptcha_botble_contact_forms_fronts_contact_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.887676, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 1.5, "width_percent": 0.382}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'api_layer_api_key'", "type": "query", "params": [], "bindings": ["", "2025-08-29 15:36:52", "api_layer_api_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.892711, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 1.882, "width_percent": 0.422}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:52", "enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.898382, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 2.304, "width_percent": 0.404}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'language_hide_default'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:52", "language_hide_default"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.903844, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 2.708, "width_percent": 0.395}, {"sql": "update `settings` set `value` = 'dropdown', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'language_switcher_display'", "type": "query", "params": [], "bindings": ["dropdown", "2025-08-29 15:36:52", "language_switcher_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.90923, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 3.103, "width_percent": 0.389}, {"sql": "update `settings` set `value` = 'all', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'language_display'", "type": "query", "params": [], "bindings": ["all", "2025-08-29 15:36:52", "language_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.914613, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 3.491, "width_percent": 0.435}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'language_hide_languages'", "type": "query", "params": [], "bindings": ["[]", "2025-08-29 15:36:52", "language_hide_languages"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.920348, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 3.926, "width_percent": 0.407}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'simple_slider_using_assets'", "type": "query", "params": [], "bindings": ["0", "2025-08-29 15:36:52", "simple_slider_using_assets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.925989, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 4.334, "width_percent": 0.39}, {"sql": "update `settings` set `value` = '05f40f708f32be2da075d57c4f3beb37', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'media_random_hash'", "type": "query", "params": [], "bindings": ["05f40f708f32be2da075d57c4f3beb37", "2025-08-29 15:36:52", "media_random_hash"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.93132, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 4.724, "width_percent": 0.404}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'payment_cod_status'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:52", "payment_cod_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.936756, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 5.127, "width_percent": 0.4}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'payment_bank_transfer_status'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:52", "payment_bank_transfer_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.942203, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 5.527, "width_percent": 0.385}, {"sql": "update `settings` set `value` = 'martfury', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'theme'", "type": "query", "params": [], "bindings": ["martfury", "2025-08-29 15:36:52", "theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.947482, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 5.911, "width_percent": 0.431}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'show_admin_bar'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:52", "show_admin_bar"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.953232, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 6.343, "width_percent": 0.404}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'admin_favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-08-29 15:36:52", "admin_favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.9586961, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 6.746, "width_percent": 0.395}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'admin_logo'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-08-29 15:36:52", "admin_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.9640632, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 7.141, "width_percent": 0.408}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'permalink-botble-blog-models-post'", "type": "query", "params": [], "bindings": ["blog", "2025-08-29 15:36:52", "permalink-botble-blog-models-post"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.969589, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 7.549, "width_percent": 0.401}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'permalink-botble-blog-models-category'", "type": "query", "params": [], "bindings": ["blog", "2025-08-29 15:36:52", "permalink-botble-blog-models-category"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.9750311, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 7.95, "width_percent": 0.397}, {"sql": "update `settings` set `value` = 'Please pay money directly to the postman, if you choose cash on delivery method (COD).', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'payment_cod_description'", "type": "query", "params": [], "bindings": ["Please pay money directly to the postman, if you choose cash on delivery method (COD).", "2025-08-29 15:36:52", "payment_cod_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.98039, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 8.346, "width_percent": 0.429}, {"sql": "update `settings` set `value` = 'Please send money to our bank account: ACB - 69270 213 19.', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'payment_bank_transfer_description'", "type": "query", "params": [], "bindings": ["Please send money to our bank account: ACB - 69270 213 19.", "2025-08-29 15:36:52", "payment_bank_transfer_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.986132, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 8.776, "width_percent": 0.417}, {"sql": "update `settings` set `value` = 'stripe_checkout', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'payment_stripe_payment_type'", "type": "query", "params": [], "bindings": ["stripe_checkout", "2025-08-29 15:36:52", "payment_stripe_payment_type"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.991673, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 9.193, "width_percent": 0.394}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-29 15:36:52' where `key` = 'plugins_ecommerce_customer_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-08-29 15:36:52", "plugins_ecommerce_customer_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": **********.99702, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 9.586, "width_percent": 0.406}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'plugins_ecommerce_admin_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-08-29 15:36:53", "plugins_ecommerce_admin_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.002502, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 9.993, "width_percent": 0.414}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_is_enabled_support_digital_products'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:53", "ecommerce_is_enabled_support_digital_products"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.008027, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 10.407, "width_percent": 0.4}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_load_countries_states_cities_from_location_plugin'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:53", "ecommerce_load_countries_states_cities_from_location_plugin"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.013436, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 10.807, "width_percent": 0.415}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'payment_bank_transfer_display_bank_info_at_the_checkout_success_page'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:53", "payment_bank_transfer_display_bank_info_at_the_checkout_success_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.019234, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 11.222, "width_percent": 0.413}, {"sql": "update `settings` set `value` = 'MF-2443-[%S]', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_product_sku_format'", "type": "query", "params": [], "bindings": ["MF-2443-[%S]", "2025-08-29 15:36:53", "ecommerce_product_sku_format"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.0248082, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 11.636, "width_percent": 0.395}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_enable_product_specification'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:53", "ecommerce_enable_product_specification"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.0301652, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 12.03, "width_percent": 0.41}, {"sql": "update `settings` set `value` = 'Martfury', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_store_name'", "type": "query", "params": [], "bindings": ["Martfury", "2025-08-29 15:36:53", "ecommerce_store_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.035677, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 12.441, "width_percent": 0.402}, {"sql": "update `settings` set `value` = '1800979769', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_store_phone'", "type": "query", "params": [], "bindings": ["1800979769", "2025-08-29 15:36:53", "ecommerce_store_phone"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.041124, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 12.842, "width_percent": 0.397}, {"sql": "update `settings` set `value` = '502 New Street', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_store_address'", "type": "query", "params": [], "bindings": ["502 New Street", "2025-08-29 15:36:53", "ecommerce_store_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.046521, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 13.239, "width_percent": 0.42}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_store_state'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-08-29 15:36:53", "ecommerce_store_state"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.052168, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 13.659, "width_percent": 0.402}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_store_city'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-08-29 15:36:53", "ecommerce_store_city"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.057618, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 14.061, "width_percent": 0.39}, {"sql": "update `settings` set `value` = 'AU', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'ecommerce_store_country'", "type": "query", "params": [], "bindings": ["AU", "2025-08-29 15:36:53", "ecommerce_store_country"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.063006, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 14.45, "width_percent": 0.402}, {"sql": "update `settings` set `value` = 'MartFury - Laravel Ecommerce system', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-site_title'", "type": "query", "params": [], "bindings": ["MartFury - Laravel Ecommerce system", "2025-08-29 15:36:53", "theme-martfury-site_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.068496, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 14.852, "width_percent": 0.402}, {"sql": "update `settings` set `value` = 'MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-seo_description'", "type": "query", "params": [], "bindings": ["MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.", "2025-08-29 15:36:53", "theme-martfury-seo_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.073937, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 15.254, "width_percent": 0.401}, {"sql": "update `settings` set `value` = '© %Y MartFury. All Rights Reserved.', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-copyright'", "type": "query", "params": [], "bindings": ["© %Y MartFury. All Rights Reserved.", "2025-08-29 15:36:53", "theme-martfury-copyright"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.079342, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 15.654, "width_percent": 0.404}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-08-29 15:36:53", "theme-martfury-favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.0850198, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 16.058, "width_percent": 0.391}, {"sql": "update `settings` set `value` = 'general/logo.png', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-logo'", "type": "query", "params": [], "bindings": ["general/logo.png", "2025-08-29 15:36:53", "theme-martfury-logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.090322, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 16.448, "width_percent": 0.393}, {"sql": "update `settings` set `value` = 'Welcome to MartFury Online Shopping Store!', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-welcome_message'", "type": "query", "params": [], "bindings": ["Welcome to MartFury Online Shopping Store!", "2025-08-29 15:36:53", "theme-martfury-welcome_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.09566, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 16.841, "width_percent": 0.406}, {"sql": "update `settings` set `value` = '502 New Street, Brighton VIC, Australia', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-address'", "type": "query", "params": [], "bindings": ["502 New Street, Brighton VIC, Australia", "2025-08-29 15:36:53", "theme-martfury-address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.101171, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 17.247, "width_percent": 0.388}, {"sql": "update `settings` set `value` = '1800 97 97 69', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-hotline'", "type": "query", "params": [], "bindings": ["1800 97 97 69", "2025-08-29 15:36:53", "theme-martfury-hotline"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.1064858, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 17.635, "width_percent": 0.404}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-email'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-29 15:36:53", "theme-martfury-email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.111927, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 18.039, "width_percent": 0.4}, {"sql": "update `settings` set `value` = 'general/newsletter.jpg', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-newsletter_image'", "type": "query", "params": [], "bindings": ["general/newsletter.jpg", "2025-08-29 15:36:53", "theme-martfury-newsletter_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.117475, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 18.438, "width_percent": 0.394}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-homepage_id'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:53", "theme-martfury-homepage_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.122828, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 18.832, "width_percent": 0.409}, {"sql": "update `settings` set `value` = '6', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-blog_page_id'", "type": "query", "params": [], "bindings": ["6", "2025-08-29 15:36:53", "theme-martfury-blog_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.128375, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 19.241, "width_percent": 0.392}, {"sql": "update `settings` set `value` = 'Your experience on this site will be improved by allowing cookies ', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-cookie_consent_message'", "type": "query", "params": [], "bindings": ["Your experience on this site will be improved by allowing cookies ", "2025-08-29 15:36:53", "theme-martfury-cookie_consent_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.133599, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 19.633, "width_percent": 0.395}, {"sql": "update `settings` set `value` = '/cookie-policy', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-cookie_consent_learn_more_url'", "type": "query", "params": [], "bindings": ["/cookie-policy", "2025-08-29 15:36:53", "theme-martfury-cookie_consent_learn_more_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.138966, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 20.027, "width_percent": 0.392}, {"sql": "update `settings` set `value` = 'Cookie Policy', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-cookie_consent_learn_more_text'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "2025-08-29 15:36:53", "theme-martfury-cookie_consent_learn_more_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.1444468, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 20.419, "width_percent": 0.403}, {"sql": "update `settings` set `value` = '42', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-number_of_products_per_page'", "type": "query", "params": [], "bindings": ["42", "2025-08-29 15:36:53", "theme-martfury-number_of_products_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.150146, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 20.822, "width_percent": 0.422}, {"sql": "update `settings` set `value` = 'Shipping worldwide', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_1_title'", "type": "query", "params": [], "bindings": ["Shipping worldwide", "2025-08-29 15:36:53", "theme-martfury-product_feature_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.155797, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 21.244, "width_percent": 0.407}, {"sql": "update `settings` set `value` = 'icon-network', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_1_icon'", "type": "query", "params": [], "bindings": ["icon-network", "2025-08-29 15:36:53", "theme-martfury-product_feature_1_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.161274, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 21.651, "width_percent": 0.383}, {"sql": "update `settings` set `value` = 'Free 7-day return if eligible, so easy', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_2_title'", "type": "query", "params": [], "bindings": ["Free 7-day return if eligible, so easy", "2025-08-29 15:36:53", "theme-martfury-product_feature_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.16635, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 22.034, "width_percent": 0.388}, {"sql": "update `settings` set `value` = 'icon-3d-rotate', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_2_icon'", "type": "query", "params": [], "bindings": ["icon-3d-rotate", "2025-08-29 15:36:53", "theme-martfury-product_feature_2_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.1716259, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 22.422, "width_percent": 0.386}, {"sql": "update `settings` set `value` = 'Supplier give bills for this product.', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_3_title'", "type": "query", "params": [], "bindings": ["Supplier give bills for this product.", "2025-08-29 15:36:53", "theme-martfury-product_feature_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.176873, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 22.808, "width_percent": 0.399}, {"sql": "update `settings` set `value` = 'icon-receipt', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_3_icon'", "type": "query", "params": [], "bindings": ["icon-receipt", "2025-08-29 15:36:53", "theme-martfury-product_feature_3_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.182498, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 23.206, "width_percent": 0.438}, {"sql": "update `settings` set `value` = 'Pay online or when receiving goods', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_4_title'", "type": "query", "params": [], "bindings": ["Pay online or when receiving goods", "2025-08-29 15:36:53", "theme-martfury-product_feature_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.188351, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 23.644, "width_percent": 0.403}, {"sql": "update `settings` set `value` = 'icon-credit-card', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-product_feature_4_icon'", "type": "query", "params": [], "bindings": ["icon-credit-card", "2025-08-29 15:36:53", "theme-martfury-product_feature_4_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.193806, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 24.047, "width_percent": 0.381}, {"sql": "update `settings` set `value` = 'Contact Directly', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_1_title'", "type": "query", "params": [], "bindings": ["Contact Directly", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.198952, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 24.428, "width_percent": 0.405}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_1_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_1_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.204395, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 24.833, "width_percent": 0.381}, {"sql": "update `settings` set `value` = '(+004) 912-3548-07', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_1_details'", "type": "query", "params": [], "bindings": ["(+004) 912-3548-07", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_1_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.2100778, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 25.214, "width_percent": 0.391}, {"sql": "update `settings` set `value` = 'Headquarters', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_2_title'", "type": "query", "params": [], "bindings": ["Headquarters", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.2156188, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 25.605, "width_percent": 0.422}, {"sql": "update `settings` set `value` = '17 Queen St, South bank, Melbourne 10560, Australia', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_2_subtitle'", "type": "query", "params": [], "bindings": ["17 Queen St, South bank, Melbourne 10560, Australia", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_2_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.221044, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 26.027, "width_percent": 0.358}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_2_details'", "type": "query", "params": [], "bindings": ["", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_2_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.2257068, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 26.385, "width_percent": 0.355}, {"sql": "update `settings` set `value` = 'Work With Us', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_3_title'", "type": "query", "params": [], "bindings": ["Work With Us", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.2308302, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 26.74, "width_percent": 0.408}, {"sql": "update `settings` set `value` = 'Send your CV to our email:', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_3_subtitle'", "type": "query", "params": [], "bindings": ["Send your CV to our email:", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_3_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.2363431, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 27.148, "width_percent": 0.394}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_3_details'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_3_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.241713, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 27.542, "width_percent": 0.396}, {"sql": "update `settings` set `value` = 'Customer Service', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_4_title'", "type": "query", "params": [], "bindings": ["Customer Service", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.247154, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 27.938, "width_percent": 0.394}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_4_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_4_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.252522, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 28.331, "width_percent": 0.403}, {"sql": "update `settings` set `value` = '(800) 843-2446', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_4_details'", "type": "query", "params": [], "bindings": ["(800) 843-2446", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_4_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.2579699, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 28.734, "width_percent": 0.389}, {"sql": "update `settings` set `value` = 'Media Relations', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_5_title'", "type": "query", "params": [], "bindings": ["Media Relations", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_5_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.263563, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 29.122, "width_percent": 0.418}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_5_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_5_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.269284, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 29.541, "width_percent": 0.398}, {"sql": "update `settings` set `value` = '(801) 947-3564', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_5_details'", "type": "query", "params": [], "bindings": ["(801) 947-3564", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_5_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.274684, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 29.938, "width_percent": 0.403}, {"sql": "update `settings` set `value` = 'Vendor Support', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_6_title'", "type": "query", "params": [], "bindings": ["Vendor Support", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_6_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.2801628, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 30.341, "width_percent": 0.405}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_6_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_6_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.285601, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 30.746, "width_percent": 0.388}, {"sql": "update `settings` set `value` = '(801) 947-3100', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-contact_info_box_6_details'", "type": "query", "params": [], "bindings": ["(801) 947-3100", "2025-08-29 15:36:53", "theme-martfury-contact_info_box_6_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.290886, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 31.134, "width_percent": 0.4}, {"sql": "update `settings` set `value` = '7', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-number_of_cross_sale_product'", "type": "query", "params": [], "bindings": ["7", "2025-08-29 15:36:53", "theme-martfury-number_of_cross_sale_product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.296309, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 31.534, "width_percent": 0.389}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-logo_in_the_checkout_page'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-08-29 15:36:53", "theme-martfury-logo_in_the_checkout_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.301635, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 31.922, "width_percent": 0.389}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-logo_in_invoices'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-08-29 15:36:53", "theme-martfury-logo_in_invoices"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.306724, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 32.311, "width_percent": 0.374}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-logo_vendor_dashboard'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-08-29 15:36:53", "theme-martfury-logo_vendor_dashboard"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.311672, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 32.685, "width_percent": 0.381}, {"sql": "update `settings` set `value` = 'Work Sans', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-primary_font'", "type": "query", "params": [], "bindings": ["Work Sans", "2025-08-29 15:36:53", "theme-martfury-primary_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.317031, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 33.066, "width_percent": 0.403}, {"sql": "update `settings` set `value` = '[\\\"general\\\\/payment-method-1.jpg\\\",\\\"general\\\\/payment-method-2.jpg\\\",\\\"general\\\\/payment-method-3.jpg\\\",\\\"general\\\\/payment-method-4.jpg\\\",\\\"general\\\\/payment-method-5.jpg\\\"]', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-payment_methods'", "type": "query", "params": [], "bindings": ["[\"general\\/payment-method-1.jpg\",\"general\\/payment-method-2.jpg\",\"general\\/payment-method-3.jpg\",\"general\\/payment-method-4.jpg\",\"general\\/payment-method-5.jpg\"]", "2025-08-29 15:36:53", "theme-martfury-payment_methods"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.322266, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 33.468, "width_percent": 0.365}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.facebook.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"X (Twitter)\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/x.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"YouTube\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-youtube\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.youtube.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Instagram\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.linkedin.com\\\"}]]', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-social_links'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"name\",\"value\":\"Facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.facebook.com\"}],[{\"key\":\"name\",\"value\":\"<PERSON> (Twitter)\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"},{\"key\":\"url\",\"value\":\"https:\\/\\/x.com\"}],[{\"key\":\"name\",\"value\":\"YouTube\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-youtube\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.youtube.com\"}],[{\"key\":\"name\",\"value\":\"Instagram\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.linkedin.com\"}]]", "2025-08-29 15:36:53", "theme-martfury-social_links"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.327222, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 33.833, "width_percent": 0.364}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"x\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"pinterest\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-pinterest\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"linkedin\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"whatsapp\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-whatsapp\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"email\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-mail\\\"}]]', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-social_sharing'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"social\",\"value\":\"facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"}],[{\"key\":\"social\",\"value\":\"x\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"}],[{\"key\":\"social\",\"value\":\"pinterest\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-pinterest\"}],[{\"key\":\"social\",\"value\":\"linkedin\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"}],[{\"key\":\"social\",\"value\":\"whatsapp\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-whatsapp\"}],[{\"key\":\"social\",\"value\":\"email\"},{\"key\":\"icon\",\"value\":\"ti ti-mail\"}]]", "2025-08-29 15:36:53", "theme-martfury-social_sharing"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.332242, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 34.197, "width_percent": 0.436}, {"sql": "update `settings` set `value` = '#fcb800', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-primary_color'", "type": "query", "params": [], "bindings": ["#fcb800", "2025-08-29 15:36:53", "theme-martfury-primary_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.3380442, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 34.633, "width_percent": 0.396}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-admin_logo'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-08-29 15:36:53", "theme-martfury-admin_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.34342, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 35.029, "width_percent": 0.386}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'theme-martfury-admin_favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-08-29 15:36:53", "theme-martfury-admin_favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.348868, "duration": 0.004940000000000001, "duration_str": "4.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 35.415, "width_percent": 0.489}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'is_completed_get_started'", "type": "query", "params": [], "bindings": ["1", "2025-08-29 15:36:53", "is_completed_get_started"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.3552089, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 35.903, "width_percent": 0.403}, {"sql": "update `settings` set `value` = 'Pay online via Razorpay', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'payment_razorpay_name'", "type": "query", "params": [], "bindings": ["Pay online via Razorpay", "2025-08-29 15:36:53", "payment_razorpay_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.360777, "duration": 0.00728, "duration_str": "7.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 36.306, "width_percent": 0.72}, {"sql": "update `settings` set `value` = 'Payment with <PERSON><PERSON><PERSON>y', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'payment_razorpay_description'", "type": "query", "params": [], "bindings": ["Payment with Razorpay", "2025-08-29 15:36:53", "payment_razorpay_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.3694408, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 37.026, "width_percent": 0.459}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'payment_razorpay_logo'", "type": "query", "params": [], "bindings": ["", "2025-08-29 15:36:53", "payment_razorpay_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.375447, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 37.485, "width_percent": 0.416}, {"sql": "update `settings` set `value` = 'rzp_test_WdWDmIqADKKMMT', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'payment_razorpay_key'", "type": "query", "params": [], "bindings": ["rzp_test_WdWDmIqADKKMMT", "2025-08-29 15:36:53", "payment_razorpay_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.381042, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 37.901, "width_percent": 0.44}, {"sql": "update `settings` set `value` = 'bYnsMUuTcxNwicoDXwJmAj1e', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'payment_razorpay_secret'", "type": "query", "params": [], "bindings": ["bYnsMUuTcxNwicoDXwJmAj1e", "2025-08-29 15:36:53", "payment_razorpay_secret"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.386888, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 38.341, "width_percent": 0.415}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-08-29 15:36:53' where `key` = 'payment_razorpay_available_countries'", "type": "query", "params": [], "bindings": ["[]", "2025-08-29 15:36:53", "payment_razorpay_available_countries"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Settings/SettingController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Settings\\SettingController.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 31}], "start": 1756481813.392529, "duration": 0.01098, "duration_str": "10.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "martfury", "explain": null, "start_percent": 38.756, "width_percent": 1.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.404871, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 39.842, "width_percent": 0.391}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.4092221, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 40.233, "width_percent": 0.399}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.4136682, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 40.632, "width_percent": 0.359}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.417814, "duration": 0.01502, "duration_str": "15.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 40.991, "width_percent": 1.485}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.433376, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 42.476, "width_percent": 0.396}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.437732, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 42.872, "width_percent": 0.298}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.441005, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 43.169, "width_percent": 0.292}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.444228, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 43.461, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.447487, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 43.745, "width_percent": 0.359}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.4515228, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 44.104, "width_percent": 0.337}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.4552941, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 44.441, "width_percent": 0.358}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.459315, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 44.799, "width_percent": 0.342}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.4631789, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 45.141, "width_percent": 0.338}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.466985, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 45.48, "width_percent": 0.34}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.470818, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 45.82, "width_percent": 0.334}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.474597, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 46.154, "width_percent": 0.333}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.478467, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 46.488, "width_percent": 0.369}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.482716, "duration": 0.00992, "duration_str": "9.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 46.856, "width_percent": 0.981}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.4930391, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 47.838, "width_percent": 0.342}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.496875, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 48.18, "width_percent": 0.209}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.4993348, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 48.388, "width_percent": 0.231}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.502068, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 48.62, "width_percent": 0.243}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.50492, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 48.863, "width_percent": 0.217}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.507512, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 49.08, "width_percent": 0.221}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.51014, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 49.3, "width_percent": 0.226}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.512924, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 49.527, "width_percent": 0.257}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.516016, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 49.784, "width_percent": 0.242}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5188642, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 50.026, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5214698, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 50.245, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.524021, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 50.46, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.526344, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 50.658, "width_percent": 0.208}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5288022, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 50.866, "width_percent": 0.205}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.531178, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 51.071, "width_percent": 0.23}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5339072, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 51.301, "width_percent": 0.235}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5366662, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 51.536, "width_percent": 0.223}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.539311, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 51.759, "width_percent": 0.225}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.541972, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 51.983, "width_percent": 0.226}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.544755, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 52.21, "width_percent": 0.225}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.547437, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 52.435, "width_percent": 0.242}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.55032, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 52.678, "width_percent": 0.234}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.553078, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 52.912, "width_percent": 0.234}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.555837, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 53.147, "width_percent": 0.227}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.558536, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 53.374, "width_percent": 0.235}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.561315, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 53.609, "width_percent": 0.235}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.564066, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 53.845, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.566505, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 54.05, "width_percent": 0.221}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.569128, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 54.271, "width_percent": 0.231}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.571856, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 54.502, "width_percent": 0.236}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.574642, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 54.739, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.577871, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 55.019, "width_percent": 0.239}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5806968, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 55.258, "width_percent": 0.234}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5835168, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 55.492, "width_percent": 0.231}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.5862548, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 55.724, "width_percent": 0.232}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.588993, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 55.956, "width_percent": 0.236}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.591774, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 56.193, "width_percent": 0.235}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.594573, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 56.428, "width_percent": 0.254}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.598563, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 56.682, "width_percent": 0.251}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.60146, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 56.933, "width_percent": 0.208}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.603965, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 57.141, "width_percent": 0.236}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.606748, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 57.378, "width_percent": 0.237}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.6095412, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 57.615, "width_percent": 0.236}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.612288, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 57.851, "width_percent": 0.213}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.614721, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 58.064, "width_percent": 0.236}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.6176012, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 58.3, "width_percent": 0.235}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.620372, "duration": 0.00771, "duration_str": "7.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 58.536, "width_percent": 0.763}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.628477, "duration": 0.0076500000000000005, "duration_str": "7.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 59.298, "width_percent": 0.757}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.636523, "duration": 0.00777, "duration_str": "7.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 60.055, "width_percent": 0.768}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.6446838, "duration": 0.00762, "duration_str": "7.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 60.823, "width_percent": 0.754}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.6527028, "duration": 0.00762, "duration_str": "7.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 61.577, "width_percent": 0.754}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.6607218, "duration": 0.00763, "duration_str": "7.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 62.331, "width_percent": 0.755}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.668751, "duration": 0.00771, "duration_str": "7.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 63.085, "width_percent": 0.763}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.676869, "duration": 0.0077, "duration_str": "7.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 63.848, "width_percent": 0.762}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.684948, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 64.609, "width_percent": 0.343}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.688812, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 64.952, "width_percent": 0.364}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.692893, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 65.316, "width_percent": 0.364}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.696972, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 65.68, "width_percent": 0.347}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.700882, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 66.027, "width_percent": 0.371}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.7050319, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 66.398, "width_percent": 0.348}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.708946, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 66.746, "width_percent": 0.354}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.712935, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 67.101, "width_percent": 0.345}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.716852, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 67.446, "width_percent": 0.349}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.7207808, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 67.795, "width_percent": 0.352}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.724736, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 68.147, "width_percent": 0.355}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.728714, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 68.502, "width_percent": 0.353}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.732661, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 68.855, "width_percent": 0.351}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.736609, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 69.206, "width_percent": 0.352}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.740558, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 69.558, "width_percent": 0.353}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.744525, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 69.911, "width_percent": 0.351}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.748465, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 70.262, "width_percent": 0.379}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.75269, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 70.641, "width_percent": 0.35}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.756624, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 70.991, "width_percent": 0.35}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.760558, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 71.341, "width_percent": 0.353}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.764515, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 71.694, "width_percent": 0.351}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.768458, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 72.046, "width_percent": 0.349}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.772376, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 72.395, "width_percent": 0.341}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.776219, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 72.736, "width_percent": 0.355}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.7802422, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 73.091, "width_percent": 0.426}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.784944, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 73.517, "width_percent": 0.428}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.789666, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 73.945, "width_percent": 0.42}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.794332, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 74.366, "width_percent": 0.404}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.798788, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 74.769, "width_percent": 0.416}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.803393, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 75.186, "width_percent": 0.416}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8079948, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 75.602, "width_percent": 0.421}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8126981, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 76.023, "width_percent": 0.43}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8174949, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 76.454, "width_percent": 0.454}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.822472, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 76.908, "width_percent": 0.418}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.827101, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 77.326, "width_percent": 0.41}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.831666, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 77.736, "width_percent": 0.41}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.836211, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 78.147, "width_percent": 0.425}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.840911, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 78.572, "width_percent": 0.421}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.845602, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 78.993, "width_percent": 0.439}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.850479, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 79.433, "width_percent": 0.436}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8553112, "duration": 0.00451, "duration_str": "4.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 79.869, "width_percent": 0.446}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8602169, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 80.315, "width_percent": 0.403}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8646832, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 80.717, "width_percent": 0.411}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.869243, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 81.129, "width_percent": 0.418}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.873865, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 81.547, "width_percent": 0.42}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8785272, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 81.967, "width_percent": 0.409}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.8831592, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 82.377, "width_percent": 0.443}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.888043, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 82.82, "width_percent": 0.433}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.892816, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 83.253, "width_percent": 0.42}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.897455, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 83.673, "width_percent": 0.407}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.901968, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 84.081, "width_percent": 0.422}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.906655, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 84.503, "width_percent": 0.413}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.911262, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 84.917, "width_percent": 0.408}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.9158041, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 85.325, "width_percent": 0.43}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.920549, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 85.755, "width_percent": 0.417}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.925163, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 86.173, "width_percent": 0.415}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.929753, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 86.588, "width_percent": 0.429}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.934491, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 87.017, "width_percent": 0.417}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.939107, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 87.435, "width_percent": 0.414}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.9437141, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 87.849, "width_percent": 0.413}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.9482799, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 88.262, "width_percent": 0.49}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.9536269, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 88.752, "width_percent": 0.418}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.958254, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 89.17, "width_percent": 0.415}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.962826, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 89.586, "width_percent": 0.412}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.96737, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 89.998, "width_percent": 0.412}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.971939, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 90.411, "width_percent": 0.414}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.976523, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 90.825, "width_percent": 0.416}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.981131, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 91.241, "width_percent": 0.41}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.9856749, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 91.652, "width_percent": 0.425}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.9903722, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 92.077, "width_percent": 0.426}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.995079, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 92.503, "width_percent": 0.41}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1756481813.999641, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 92.914, "width_percent": 0.421}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.004297, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 93.335, "width_percent": 0.429}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0090349, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 93.764, "width_percent": 0.417}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0136619, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 94.182, "width_percent": 0.42}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.018298, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 94.602, "width_percent": 0.407}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.022817, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 95.009, "width_percent": 0.422}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.027479, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 95.432, "width_percent": 0.418}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.032087, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 95.85, "width_percent": 0.411}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.036612, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 96.262, "width_percent": 0.4}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.041003, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 96.661, "width_percent": 0.392}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.045421, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 97.053, "width_percent": 0.421}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0501628, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 97.474, "width_percent": 0.441}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.055015, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 97.915, "width_percent": 0.431}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.05977, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 98.346, "width_percent": 0.416}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.064374, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 98.763, "width_percent": 0.416}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.068979, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 99.179, "width_percent": 0.412}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.073477, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "martfury", "explain": null, "start_percent": 99.592, "width_percent": 0.408}]}, "models": {"data": {"Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://martfury.gc/admin/ecommerce/settings/branch-management", "action_name": "branch-management.settings.update", "controller_action": "Botble\\BranchManagement\\Http\\Controllers\\Settings\\BranchManagementSettingController@update", "uri": "PUT admin/ecommerce/settings/branch-management", "controller": "Botble\\BranchManagement\\Http\\Controllers\\Settings\\BranchManagementSettingController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FHttp%2FControllers%2FSettings%2FBranchManagementSettingController.php&line=18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\BranchManagement\\Http\\Controllers", "prefix": "admin/ecommerce/settings", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FHttp%2FControllers%2FSettings%2FBranchManagementSettingController.php&line=18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/branch-management/src/Http/Controllers/Settings/BranchManagementSettingController.php:18-21</a>", "middleware": "web, core, auth", "duration": "2.07s", "peak_memory": "60MB", "response": "Redirect to https://martfury.gc/admin/ecommerce/settings/branch-management", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-671807818 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-671807818\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-876513864 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VcRzgLLYZnl2p0nut86nGe19fEl1DoQGuytBwvJ0</span>\"\n  \"<span class=sf-dump-key>branch_management_enable_pickup</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>branch_management_pickup_fee</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>branch_management_show_on_product_page</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876513864\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-570336824 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">239</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">https://martfury.gc/admin/ecommerce/settings/branch-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3292 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; perf_dv6Tr4n=1; botble_cookie_newsletter=1; XSRF-TOKEN=eyJpdiI6Ijc5QmkzMlFHTDIxcHZzOWJtZEVzc2c9PSIsInZhbHVlIjoiRkFjbE51M2dpWHVXd1ZlWEozN2tFcUoybG01SXYvNjBZTG9xWDZITEl6V1UvNWJnNFVPa1pObzVWTzVzL3pwaWx4RGdqWm9HTmR6ejVCN3hpU3owVkdDK0JBUXpUVk1CVkpJWVA0dGd4OHowVkQrTytTTWNFaWx0K3lsU3l1SlYiLCJtYWMiOiJiZWM1NzNhMjFhZWYxOWQ4M2I1ZTI0NWNkMGQxMTJkYTk2NzUxMTM0Y2FlY2YxMTg1OTU4NjMxMmE3ZDJmNGU4IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IkRGNllHU1ZLOFYxL3dtS3E5cFQ2Q0E9PSIsInZhbHVlIjoiSENZalc5R0pyK013WlBPY1I3VHFSR3RxKzdwMU9ydkw0S3hySkNSRTBrV2NDNGw3YzNwVG5lUko3bUlyTThqcU9YdnYxOG5QSWI4R0Jzb29RM09HTDBZcWF6Wi9zbDd4dUlLZEhKdmZoYjErcGNPelNQNGVSSldIQ2JaN0p3bnUiLCJtYWMiOiIxMjkyNjc2MDBhMjRkODg0Yjg5MGE4NDU2MmEwNTVhNDE3MmY1OTQ4ZTNlYzU2YmJlODFkY2IyYWRiNzM0NjAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570336824\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VcRzgLLYZnl2p0nut86nGe19fEl1DoQGuytBwvJ0</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jd5XX6SG6uq7qt4J1nCyZma0wd2N24xRE6YVO5Zl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-835789777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 29 Aug 2025 15:36:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">https://martfury.gc/admin/ecommerce/settings/branch-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835789777\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1788962020 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VcRzgLLYZnl2p0nut86nGe19fEl1DoQGuytBwvJ0</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://martfury.gc/admin/ecommerce/settings/branch-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>2</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>7</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756481738\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756481738</span></span> {<a class=sf-dump-ref href=#sf-dump-1788962020-ref24241 title=\"3 occurrences\">#4241</a><samp data-depth=3 id=sf-dump-1788962020-ref24241 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010910000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:01:15.960817 from now\nDST Off\">2025-08-29 15:35:38.139518 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4242</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>aa26812c99f0c997f1e301988b86cbb6</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4243</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">aa26812c99f0c997f1e301988b86cbb6</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>30</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"25 characters\">Red &amp;amp; Black Headphone</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>417.15</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4244</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/4.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"22 characters\">(Color: Blue, Size: S)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-129-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>865.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756481738\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756481738</span></span> {<a class=sf-dump-ref>#4245</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010950000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:01:15.961299 from now\nDST Off\">2025-08-29 15:35:38.139484 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756481738\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756481738</span></span> {<a class=sf-dump-ref>#4246</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010960000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:01:15.961356 from now\nDST Off\">2025-08-29 15:35:38.139478 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">10ed7024f09405b8e39bbeef73d7bedb</span>\"\n  \"<span class=sf-dump-key>4f95712248d816491efaef8b758a20d3</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:17</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756481738\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756481738</span></span> {<a class=sf-dump-ref href=#sf-dump-1788962020-ref24241 title=\"3 occurrences\">#4241</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>83</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756481738\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756481738</span></span> {<a class=sf-dump-ref href=#sf-dump-1788962020-ref24241 title=\"3 occurrences\">#4241</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => <span class=sf-dump-num>10.0</span>\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>branch_pickup</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>pickup_branch</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Branch Pickup</span>\"\n              \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Please select your preferred pickup location</span>\"\n              \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>disabled</span>\" => <span class=sf-dump-const>false</span>\n              \"<span class=sf-dump-key>option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>abandoned_cart_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>abandoned_cart_phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">03147552550</span>\"\n  \"<span class=sf-dump-key>abandoned_cart_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Branch 1</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788962020\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://martfury.gc/admin/ecommerce/settings/branch-management", "action_name": "branch-management.settings.update", "controller_action": "Botble\\BranchManagement\\Http\\Controllers\\Settings\\BranchManagementSettingController@update"}, "badge": "302 Found"}}