@php
    $branchInfo = get_branch_pickup_info_for_order($order);
@endphp

@if($branchInfo)
<x-core::card class="mb-3">
    <x-core::card.header>
        <x-core::card.title>
            <i class="fa fa-store"></i> {{ trans('plugins/branch-management::branch.pickup.title') }}
        </x-core::card.title>
    </x-core::card.header>

    <x-core::card.body>
        <div class="branch-info">
            <!-- Dynamic Shipping Method Name -->
            <div class="mb-3 p-2 bg-light rounded">
                <strong>{{ trans('plugins/ecommerce::order.shipping_method') }}:</strong><br>
                <span class="text-success">{{ $branchInfo['dynamic_shipping_name'] }}</span>
            </div>

            <div class="mb-3">
                <strong>{{ trans('plugins/branch-management::branch.fields.name') }}:</strong><br>
                <span class="text-primary">{{ $branchInfo['branch_name'] }}</span>
            </div>

            <div class="mb-3">
                <strong>{{ trans('plugins/branch-management::branch.fields.address') }}:</strong><br>
                <small class="text-muted">{{ $branchInfo['branch_address'] }}</small>
            </div>

            @if($branchInfo['branch_phone'])
            <div class="mb-3">
                <strong>{{ trans('plugins/branch-management::branch.fields.phone') }}:</strong><br>
                <a href="tel:{{ $branchInfo['branch_phone'] }}" class="text-decoration-none">
                    <i class="fa fa-phone"></i> {{ $branchInfo['branch_phone'] }}
                </a>
            </div>
            @endif

            @if($branchInfo['city_name'])
            <div class="mb-3">
                <strong>{{ trans('plugins/location::city.name') }}:</strong><br>
                <span class="text-muted">{{ $branchInfo['city_name'] }}</span>
            </div>
            @endif



            @if($order->shippingAddress->branch->manager_name || $order->shippingAddress->branch->manager_phone)
            <div class="mb-3">
                <strong>{{ trans('plugins/branch-management::branch.manager_information') }}:</strong><br>
                @if($order->shippingAddress->branch->manager_name)
                    <small>{{ $order->shippingAddress->branch->manager_name }}</small><br>
                @endif
                @if($order->shippingAddress->branch->manager_phone)
                    <small>
                        <a href="tel:{{ $order->shippingAddress->branch->manager_phone }}" class="text-decoration-none">
                            <i class="fa fa-phone"></i> {{ $order->shippingAddress->branch->manager_phone }}
                        </a>
                    </small>
                @endif
            </div>
            @endif

            @if($order->shippingAddress->branch->is_open)
                <div class="mb-3">
                    <span class="badge bg-success">
                        <i class="fa fa-clock"></i> {{ trans('plugins/branch-management::branch.status.open') }}
                    </span>
                </div>
            @else
                <div class="mb-3">
                    <span class="badge bg-warning">
                        <i class="fa fa-clock"></i> {{ trans('plugins/branch-management::branch.status.closed') }}
                    </span>
                </div>
            @endif
        </div>
    </x-core::card.body>
</x-core::card>
@endif
