@extends(Theme::getThemeNamespace('layouts.base'))

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ BaseHelper::getHomepageUrl() }}">{{ __('Home') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('public.branches') }}">{{ trans('plugins/branch-management::branch.name') }}</a></li>
                    <li class="breadcrumb-item active">{{ $branch->name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="branch-detail">
                <h1 class="branch-name">{{ $branch->name }}</h1>

                @if($branch->description)
                <div class="branch-description mb-4">
                    <p>{{ $branch->description }}</p>
                </div>
                @endif

                @if($branch->image)
                <div class="branch-image mb-4">
                    <img src="{{ RvMedia::getImageUrl($branch->image) }}" class="img-fluid rounded" alt="{{ $branch->name }}">
                </div>
                @endif



                @if($branch->special_instructions)
                <div class="special-instructions mb-4">
                    <h4>{{ trans('plugins/branch-management::branch.special_instructions') }}</h4>
                    <div class="alert alert-info">
                        {{ $branch->special_instructions }}
                    </div>
                </div>
                @endif
            </div>
        </div>

        <div class="col-lg-4">
            <div class="branch-sidebar">
                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ trans('plugins/branch-management::branch.contact_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">
                            <i class="fa fa-map-marker-alt text-primary"></i>
                            <strong>{{ __('Address') }}:</strong><br>
                            <small>{{ $branch->full_address }}</small>
                        </p>

                        @if($branch->phone)
                        <p class="mb-2">
                            <i class="fa fa-phone text-primary"></i>
                            <strong>{{ __('Phone') }}:</strong><br>
                            <small><a href="tel:{{ $branch->phone }}">{{ $branch->phone }}</a></small>
                        </p>
                        @endif

                        @if($branch->email)
                        <p class="mb-2">
                            <i class="fa fa-envelope text-primary"></i>
                            <strong>{{ __('Email') }}:</strong><br>
                            <small><a href="mailto:{{ $branch->email }}">{{ $branch->email }}</a></small>
                        </p>
                        @endif

                        @if($branch->latitude && $branch->longitude)
                        <div class="mt-3">
                            <a href="https://maps.google.com/?q={{ $branch->latitude }},{{ $branch->longitude }}" target="_blank" class="btn btn-primary btn-sm">
                                <i class="fa fa-map"></i> {{ __('Get Directions') }}
                            </a>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Pickup Information -->
                @if($branch->is_pickup_available)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ trans('plugins/branch-management::branch.pickup_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">
                            <span class="badge bg-success">{{ __('Pickup Available') }}</span>
                        </p>



                        <p class="mb-0">
                            <small class="text-muted">{{ __('You can select this branch during checkout for order pickup.') }}</small>
                        </p>
                    </div>
                </div>
                @endif

                <!-- Operating Hours -->
                @if($branch->operating_hours)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ trans('plugins/branch-management::branch.operating_hours') }}</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">{!! nl2br(e($branch->operating_hours)) !!}</p>
                    </div>
                </div>
                @endif

                <!-- Manager Information -->
                @if($branch->manager_name || $branch->manager_phone || $branch->manager_email)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ trans('plugins/branch-management::branch.manager_information') }}</h5>
                    </div>
                    <div class="card-body">
                        @if($branch->manager_name)
                        <p class="mb-2">
                            <strong>{{ __('Manager') }}:</strong> {{ $branch->manager_name }}
                        </p>
                        @endif

                        @if($branch->manager_phone)
                        <p class="mb-2">
                            <strong>{{ __('Manager Phone') }}:</strong>
                            <a href="tel:{{ $branch->manager_phone }}">{{ $branch->manager_phone }}</a>
                        </p>
                        @endif

                        @if($branch->manager_email)
                        <p class="mb-0">
                            <strong>{{ __('Manager Email') }}:</strong>
                            <a href="mailto:{{ $branch->manager_email }}">{{ $branch->manager_email }}</a>
                        </p>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
