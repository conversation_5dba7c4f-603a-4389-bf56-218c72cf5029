<?php

namespace Botble\BranchManagement\Http\Controllers\Fronts;

use Botble\Base\Http\Controllers\BaseController;
use Botble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class PublicBranchController extends BaseController
{
    public function __construct(protected BranchInterface $branchRepository)
    {
    }

    public function index()
    {
        $branches = $this->branchRepository->getBranchesGroupedByCity();

        return view('plugins/branch-management::public.branches', compact('branches'));
    }



    public function getBranchesByCity(Request $request): JsonResponse
    {
        $cityId = $request->input('city_id');
        $pickupOnly = $request->boolean('pickup_only', true);

        if (!$cityId) {
            return response()->json([]);
        }

        $branches = $this->branchRepository->getBranchesByCity($cityId, $pickupOnly);

        return response()->json(
            $branches->map(function ($branch) {
                return [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'address' => $branch->address,
                    'phone' => $branch->phone,
                    'pickup_fee' => (float) $branch->pickup_fee,
                    'full_address' => $branch->full_address,
                    'is_open' => $branch->is_open,
                    'is_pickup_available' => (bool) $branch->is_pickup_available,
                    'email' => $branch->email,
                    'manager_name' => $branch->manager_name,
                    'manager_phone' => $branch->manager_phone,
                    'special_instructions' => $branch->special_instructions,
                ];
            })
        );
    }

    public function getBranchDetails(Request $request): JsonResponse
    {
        $branchId = $request->input('branch_id');

        if (!$branchId) {
            return response()->json(['error' => 'Branch ID is required'], 400);
        }

        $branch = $this->branchRepository->findById($branchId);

        if (!$branch) {
            return response()->json(['error' => 'Branch not found'], 404);
        }

        return response()->json([
            'id' => $branch->id,
            'name' => $branch->name,
            'address' => $branch->address,
            'phone' => $branch->phone,
            'pickup_fee' => (float) $branch->pickup_fee,
            'full_address' => $branch->full_address,
            'is_open' => $branch->is_open,
            'is_pickup_available' => (bool) $branch->is_pickup_available,
            'email' => $branch->email,
            'manager_name' => $branch->manager_name,
            'manager_phone' => $branch->manager_phone,
            'special_instructions' => $branch->special_instructions,
            'operating_hours' => $branch->operating_hours,
        ]);
    }

    public function getCitiesWithBranches(): JsonResponse
    {
        try {
            $cities = collect();

            // First try to get cities with pickup branches
            try {
                $cities = $this->branchRepository->getCitiesWithPickupBranches();
                Log::info('Cities with pickup branches found: ' . $cities->count());
            } catch (\Exception $e) {
                Log::warning('Error getting cities with pickup branches: ' . $e->getMessage());
            }

            // If no cities found with pickup branches, get all cities that have any branches
            if ($cities->isEmpty()) {
                try {
                    $cities = \Botble\BranchManagement\Models\Branch::query()
                        ->join('cities', 'branches.city_id', '=', 'cities.id')
                        ->active()
                        ->select('cities.id', 'cities.name')
                        ->distinct()
                        ->orderBy('cities.name')
                        ->get();
                    Log::info('Cities with any branches found: ' . $cities->count());
                } catch (\Exception $e) {
                    Log::warning('Error getting cities with branches: ' . $e->getMessage());
                }
            }

            // If still no cities, get all cities from location plugin
            if ($cities->isEmpty() && class_exists('\Botble\Location\Models\City')) {
                try {
                    $cities = \Botble\Location\Models\City::query()
                        ->wherePublished()
                        ->orderBy('name')
                        ->get(['id', 'name']);
                    Log::info('All cities from location plugin found: ' . $cities->count());
                } catch (\Exception $e) {
                    Log::warning('Error getting cities from location plugin: ' . $e->getMessage());
                }
            }

            return response()->json(
                $cities->map(function ($city) {
                    return [
                        'id' => $city->id,
                        'name' => $city->name,
                    ];
                })
            );
        } catch (\Exception $e) {
            Log::error('Error loading cities with branches: ' . $e->getMessage());

            // Return some default cities as fallback
            return response()->json([
                ['id' => 1, 'name' => 'Karachi'],
                ['id' => 2, 'name' => 'Lahore'],
                ['id' => 3, 'name' => 'Islamabad'],
                ['id' => 4, 'name' => 'Rawalpindi'],
                ['id' => 5, 'name' => 'Faisalabad'],
            ]);
        }
    }
}
