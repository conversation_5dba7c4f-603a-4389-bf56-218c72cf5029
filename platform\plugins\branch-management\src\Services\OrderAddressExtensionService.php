<?php

namespace Botble\BranchManagement\Services;

use Botble\BranchManagement\Models\Branch;
use Botble\Ecommerce\Models\OrderAddress;
use Illuminate\Support\Facades\Schema;

class OrderAddressExtensionService
{
    public static function extendOrderAddressModel(): void
    {
        // Add relationship to Branch model
        OrderAddress::resolveRelationUsing('branch', function ($orderAddress) {
            return $orderAddress->belongsTo(Branch::class)->withDefault();
        });

        // Add accessor methods
        OrderAddress::macro('getIsBranchPickupAttribute', function () {
            return (bool) $this->attributes['is_branch_pickup'] ?? false;
        });

        OrderAddress::macro('getBranchNameAttribute', function () {
            return $this->branch->name ?? null;
        });

        OrderAddress::macro('getBranchFullAddressAttribute', function () {
            return $this->branch->full_address ?? null;
        });

        OrderAddress::macro('getBranchPhoneAttribute', function () {
            return $this->branch->phone ?? null;
        });



        OrderAddress::macro('getDisplayAddressAttribute', function () {
            if ($this->is_branch_pickup && $this->branch->exists) {
                return $this->branch->full_address;
            }

            return $this->full_address;
        });

        OrderAddress::macro('getDisplayNameAttribute', function () {
            if ($this->is_branch_pickup && $this->branch->exists) {
                return $this->branch->name . ' (Branch Pickup)';
            }

            return $this->name;
        });
    }

    public static function checkDatabaseColumns(): bool
    {
        return Schema::hasColumn('ec_order_addresses', 'branch_id')
            && Schema::hasColumn('ec_order_addresses', 'is_branch_pickup');
    }
}
