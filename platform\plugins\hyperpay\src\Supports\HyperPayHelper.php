<?php

namespace Shaqi\HyperPay\Supports;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HyperPayHelper
{
    /**
     * Check if HyperPay is properly configured
     */
    public static function isConfigured(): bool
    {
        $accessToken = get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
        $visaEntityId = get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);

        return !empty($accessToken) && !empty($visaEntityId);
    }

    /**
     * Get HyperPay configuration status
     */
    public static function getConfigurationStatus(): array
    {
        $status = [
            'configured' => false,
            'missing' => [],
            'warnings' => []
        ];

        $requiredSettings = [
            'access_token' => 'Access Token',
            'visa_entity_id' => 'Visa/Mastercard Entity ID'
        ];

        foreach ($requiredSettings as $key => $label) {
            $value = get_payment_setting($key, HYPERPAY_PAYMENT_METHOD_NAME);
            if (empty($value)) {
                $status['missing'][] = $label;
            }
        }

        // Check optional but recommended settings
        $optionalSettings = [
            'mada_entity_id' => 'Mada Entity ID',
            'amex_entity_id' => 'American Express Entity ID',
            'applepay_entity_id' => 'Apple Pay Entity ID',
            'webhook_secret' => 'Webhook Secret'
        ];

        foreach ($optionalSettings as $key => $label) {
            $value = get_payment_setting($key, HYPERPAY_PAYMENT_METHOD_NAME);
            if (empty($value)) {
                $status['warnings'][] = $label . ' is not configured';
            }
        }

        $status['configured'] = empty($status['missing']);

        return $status;
    }

    /**
     * Validate HyperPay result codes
     */
    public static function validateResultCode(string $resultCode): array
    {
        $result = [
            'status' => 'failed',
            'message' => 'Unknown result code',
            'is_success' => false,
            'is_pending' => false
        ];

        // Success patterns
        if (preg_match('/^(000\.000\.|000\.100\.1|000\.[36])/', $resultCode)) {
            $result['status'] = 'success';
            $result['message'] = 'Payment successful';
            $result['is_success'] = true;
        }
        // Pending patterns
        elseif (preg_match('/^(000\.200)/', $resultCode)) {
            $result['status'] = 'pending';
            $result['message'] = 'Payment pending';
            $result['is_pending'] = true;
        }
        elseif (preg_match('/^(800\.400\.5|100\.400\.500)/', $resultCode)) {
            $result['status'] = 'pending';
            $result['message'] = 'Payment pending (3DS)';
            $result['is_pending'] = true;
        }
        // Specific error patterns
        elseif (preg_match('/^(000\.400\.0[0-9]{2})/', $resultCode)) {
            $result['message'] = 'Transaction declined by authorization system';
        }
        elseif (preg_match('/^(000\.400\.1[0-9]{2})/', $resultCode)) {
            $result['message'] = 'Transaction declined due to risk management';
        }
        elseif (preg_match('/^(800\.400\.1)/', $resultCode)) {
            $result['message'] = 'Invalid or missing parameter';
        }
        elseif (preg_match('/^(800\.400\.2)/', $resultCode)) {
            $result['message'] = 'Invalid payment data';
        }
        elseif (preg_match('/^(800\.500\.)/', $resultCode)) {
            $result['message'] = 'System error';
        }
        elseif (preg_match('/^(800\.600\.)/', $resultCode)) {
            $result['message'] = 'Temporary system error';
        }

        return $result;
    }

    /**
     * Format amount for HyperPay (2 decimal places)
     */
    public static function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Get supported currencies
     */
    public static function getSupportedCurrencies(): array
    {
        return [
            'SAR' => 'Saudi Riyal',
            'AED' => 'UAE Dirham',
            'USD' => 'US Dollar',
            'EUR' => 'Euro'
        ];
    }

    /**
     * Get payment type brands mapping
     */
    public static function getPaymentTypeBrands(): array
    {
        return [
            'visa' => 'VISA MASTER',
            'mada' => 'MADA',
            'amex' => 'AMEX',
            'applepay' => 'APPLEPAY'
        ];
    }

    /**
     * Validate webhook signature
     */
    public static function validateWebhookSignature(string $payload, string $signature, string $secret): bool
    {
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Log HyperPay transaction
     */
    public static function logTransaction(string $type, array $data): void
    {
        Log::channel('single')->info("HyperPay {$type}", [
            'timestamp' => now()->toISOString(),
            'data' => $data
        ]);
    }

    /**
     * Get entity ID for payment type
     */
    public static function getEntityIdForPaymentType(string $paymentType): ?string
    {
        $entityIdMap = [
            'visa' => 'visa_entity_id',
            'master' => 'visa_entity_id', // Same as visa
            'mada' => 'mada_entity_id',
            'amex' => 'amex_entity_id',
            'applepay' => 'applepay_entity_id'
        ];

        $settingKey = $entityIdMap[$paymentType] ?? 'visa_entity_id';
        return get_payment_setting($settingKey, HYPERPAY_PAYMENT_METHOD_NAME);
    }

    /**
     * Generate merchant transaction ID
     */
    public static function generateMerchantTransactionId(string $prefix = 'order'): string
    {
        return $prefix . '_' . time() . '_' . uniqid();
    }

    /**
     * Check if sandbox mode is enabled
     */
    public static function isSandboxMode(): bool
    {
        return (bool) get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);
    }

    /**
     * Check if 3DS is enabled
     */
    public static function is3DSEnabled(): bool
    {
        return (bool) get_payment_setting('3ds_enabled', HYPERPAY_PAYMENT_METHOD_NAME, true);
    }

    /**
     * Get HyperPay API base URL
     */
    public static function getApiBaseUrl(): string
    {
        return self::isSandboxMode()
            ? 'https://eu-test.oppwa.com/v1'
            : 'https://eu-prod.oppwa.com/v1';
    }

    /**
     * Get widget script URL
     */
    public static function getWidgetScriptUrl(): string
    {
        return self::isSandboxMode()
            ? 'https://eu-test.oppwa.com/v1/paymentWidgets.js'
            : 'https://eu-prod.oppwa.com/v1/paymentWidgets.js';
    }

    /**
     * Sanitize customer data for HyperPay
     */
    public static function sanitizeCustomerData(array $data): array
    {
        $sanitized = [];

        // Remove special characters from names
        if (isset($data['name'])) {
            $sanitized['name'] = preg_replace('/[^\w\s]/', '', $data['name']);
        }

        // Validate email
        if (isset($data['email']) && filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $sanitized['email'] = $data['email'];
        }

        // Sanitize address
        if (isset($data['address'])) {
            $sanitized['address'] = preg_replace('/[^\w\s,.-]/', '', $data['address']);
        }

        // Sanitize city
        if (isset($data['city'])) {
            $sanitized['city'] = preg_replace('/[^\w\s]/', '', $data['city']);
        }

        // Validate country code (2 letters)
        if (isset($data['country']) && preg_match('/^[A-Z]{2}$/', $data['country'])) {
            $sanitized['country'] = $data['country'];
        }

        // Validate postal code
        if (isset($data['postcode'])) {
            $sanitized['postcode'] = preg_replace('/[^\w\s-]/', '', $data['postcode']);
        }

        return $sanitized;
    }

    /**
     * Debug HyperPay configuration
     */
    public static function debugConfiguration(): array
    {
        $debug = [
            'access_token' => !empty(get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME)),
            'visa_entity_id' => get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME),
            'mada_entity_id' => get_payment_setting('mada_entity_id', HYPERPAY_PAYMENT_METHOD_NAME),
            'amex_entity_id' => get_payment_setting('amex_entity_id', HYPERPAY_PAYMENT_METHOD_NAME),
            'applepay_entity_id' => get_payment_setting('applepay_entity_id', HYPERPAY_PAYMENT_METHOD_NAME),
            'sandbox_mode' => self::isSandboxMode(),
            'currency' => get_payment_setting('currency', HYPERPAY_PAYMENT_METHOD_NAME, 'SAR'),
            'api_url' => self::getApiBaseUrl(),
        ];

        return $debug;
    }

    /**
     * Test HyperPay API connection
     */
    public static function testApiConnection(): array
    {
        $accessToken = get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
        $entityId = get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);

        if (!$accessToken || !$entityId) {
            return [
                'success' => false,
                'message' => 'Access token or entity ID not configured'
            ];
        }

        try {
            $url = self::getApiBaseUrl() . '/checkouts';

            // Test with minimal required parameters
            $testData = [
                'entityId' => trim($entityId),
                'amount' => '1.00',
                'currency' => 'SAR',
                'paymentType' => 'DB',
                'merchantTransactionId' => 'test_' . time(),
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->asForm()->post($url, $testData);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['result']['code'])) {
                return [
                    'success' => true,
                    'message' => 'API connection successful',
                    'result_code' => $responseData['result']['code'],
                    'description' => $responseData['result']['description'] ?? 'Success'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'API connection failed',
                    'status_code' => $response->status(),
                    'response' => $responseData
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $e->getMessage()
            ];
        }
    }
}
