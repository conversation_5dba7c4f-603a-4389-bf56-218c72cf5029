<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('ec_order_addresses', function (Blueprint $table): void {
            $table->foreignId('branch_id')->nullable()->after('zip_code')->constrained('branches')->onDelete('set null');
            $table->boolean('is_branch_pickup')->default(false)->after('branch_id');
        });
    }

    public function down(): void
    {
        Schema::table('ec_order_addresses', function (Blueprint $table): void {
            $table->dropForeign(['branch_id']);
            $table->dropColumn(['branch_id', 'is_branch_pickup']);
        });
    }
};
