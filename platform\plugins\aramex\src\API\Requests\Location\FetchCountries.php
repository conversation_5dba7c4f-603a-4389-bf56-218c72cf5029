<?php

namespace Shaqi\Aramex\API\Requests\Location;

use Exception;
use Shaqi\Aramex\API\Interfaces\Normalize;
use Shaqi\Aramex\API\Requests\API;
use Shaqi\Aramex\API\Response\Location\CountriesFetchingResponse;

/**
 * This method allows users to get the world countries list.
 *
 * Class FetchCountries
 * @package Shaqi\Aramex\API\Requests\Location
 */
class FetchCountries extends API implements Normalize
{
    protected $live_wsdl = 'https://ws.aramex.net/shippingapi.v2/location/service_1_0.svc?wsdl';
    protected $test_wsdl = 'https://ws.sbx.aramex.net/shippingapi.v2/location/service_1_0.svc?wsdl';

    /**
     * @return CountriesFetchingResponse
     * @throws Exception
     */
    public function run()
    {
        $this->validate();

        return CountriesFetchingResponse::make($this->soapClient->FetchCountries($this->normalize()));
    }
}