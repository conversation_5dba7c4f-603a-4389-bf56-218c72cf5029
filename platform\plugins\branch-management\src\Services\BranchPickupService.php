<?php

namespace Bo<PERSON>ble\BranchManagement\Services;

use Bo<PERSON>ble\BranchManagement\Enums\BranchPickupMethodEnum;
use <PERSON><PERSON>ble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Bo<PERSON>ble\Ecommerce\Models\Order;
use Botble\Base\Facades\MetaBox;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BranchPickupService
{
    public function __construct(protected BranchInterface $branchRepository)
    {
    }

    public function getPickupOptions(array $sessionData): array
    {

        if (!get_ecommerce_setting('branch_management_enable_pickup', true)) {
            return [];
        }

        $cityId = $sessionData['address']['city'] ?? null;

        if (!$cityId) {
            return [];
        }

        $branches = $this->branchRepository->getBranchesByCity($cityId, true);

        if ($branches->isEmpty()) {
            return [];
        }

        $options = [];
        foreach ($branches as $branch) {
            $options[$branch->id] = [
                'name' => $branch->name,
                'price' => 0,
                'disabled' => false,
                'branch_id' => $branch->id,
                'branch_name' => $branch->name,
                'branch_address' => $branch->full_address,
                'branch_phone' => $branch->phone,
                'is_open' => $branch->is_open,
            ];
        }

        return [
            BranchPickupMethodEnum::BRANCH_PICKUP => $options,
        ];
    }

    public function calculatePickupFee(int $branchId): float
    {
        return 0; // No pickup fee
    }

    public function getBranchDetails(int $branchId): ?array
    {
        $branch = $this->branchRepository->findById($branchId);

        if (!$branch) {
            return null;
        }

        return [
            'id' => $branch->id,
            'name' => $branch->name,
            'address' => $branch->full_address,
            'phone' => $branch->phone,
            'email' => $branch->email,
            'operating_hours' => $branch->operating_hours,
            'special_instructions' => $branch->special_instructions,
            'is_open' => $branch->is_open,
        ];
    }

    /**
     * Save branch pickup information to order metadata
     */
    public function saveBranchPickupToOrder(Order $order, Request $request): bool
    {
        Log::info('saveBranchPickupToOrder called', [
            'order_id' => $order->id,
        ]);

        // Check if this is a branch pickup order
        $shippingMethod = $request->input('shipping_method');
        $shippingOption = $request->input('shipping_option');

        $isBranchPickup = $shippingMethod === BRANCH_PICKUP_SHIPPING_METHOD_NAME ||
                         $shippingOption === 'pickup_branch' ||
                         strpos($shippingMethod, 'pickup') !== false ||
                         strpos($shippingOption, 'pickup') !== false;

        if (!$isBranchPickup) {
            return false;
        }

        // Get branch ID from request
        $branchId = $request->input('pickup_branch_id');
        $cityId = $request->input('pickup_city_id');

        if (!$branchId) {
            return false;
        }

        // Validate branch exists
        $branch = $this->branchRepository->findById($branchId);
        if (!$branch || !$branch->is_pickup_available) {
            return false;
        }

        // Save branch pickup data to order metadata
        $branchData = [
            'branch_id' => $branchId,
            'branch_name' => $branch->name,
            'branch_address' => $branch->full_address,
            'branch_phone' => $branch->phone,
            'branch_email' => $branch->email,
            'city_id' => $cityId,
            'city_name' => $branch->city ? $branch->city->name : '',
            'manager_name' => $branch->manager_name,
            'manager_phone' => $branch->manager_phone,
            'special_instructions' => $branch->special_instructions,
            'is_branch_pickup' => true,
        ];

        // Save to order metadata
        MetaBox::saveMetaBoxData($order, 'branch_pickup_info', $branchData);

        // Update order shipping method name
        $shippingMethodName = trans('plugins/branch-management::branch.pickup.title') . ': ' . $branch->name;
        if ($branch->city) {
            $shippingMethodName .= ' -> ' . $branch->city->name;
        }

        $order->update([
            'shipping_method_name' => $shippingMethodName,
        ]);

        return true;
    }

    /**
     * Get branch pickup information from order metadata
     */
    public function getBranchPickupFromOrder(Order $order): ?array
    {
        $branchData = MetaBox::getMetaData($order, 'branch_pickup_info', true);

        if (!$branchData || !is_array($branchData) || !($branchData['is_branch_pickup'] ?? false)) {
            return null;
        }

        return $branchData;
    }

    /**
     * Check if order is a branch pickup order
     */
    public function isBranchPickupOrder(Order $order): bool
    {
        $branchData = $this->getBranchPickupFromOrder($order);
        return $branchData !== null;
    }
}
