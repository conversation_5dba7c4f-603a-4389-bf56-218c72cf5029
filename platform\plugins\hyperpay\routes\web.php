<?php

use <PERSON><PERSON><PERSON>\HyperPay\Http\Controllers\HyperPayController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Shaqi\HyperPay\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => 'payment/hyperpay'], function () {
        Route::get('checkout/{checkoutId}', [HyperPayController::class, 'checkout'])
            ->name('payments.hyperpay.checkout');

        Route::get('callback', [HyperPayController::class, 'callback'])
            ->name('payments.hyperpay.callback');

        Route::post('webhook', [HyperPayController::class, 'webhook'])
            ->name('payments.hyperpay.webhook');

        Route::get('status/{checkoutId}', [HyperPayController::class, 'status'])
            ->name('payments.hyperpay.status');

        // Debug routes (only in development)
        if (config('app.debug')) {
            Route::get('debug/config', [HyperPayController::class, 'debugConfig'])
                ->name('payments.hyperpay.debug.config');

            Route::get('debug/test-api', [HyperPayController::class, 'testApi'])
                ->name('payments.hyperpay.debug.test-api');

            Route::get('debug/test-checkout', [HyperPayController::class, 'testCheckout'])
                ->name('payments.hyperpay.debug.test-checkout');

            Route::get('debug/test-api-service', [HyperPayController::class, 'testApiService'])
                ->name('payments.hyperpay.debug.test-api-service');

            Route::get('debug/test-user-model-fix', [HyperPayController::class, 'testUserModelFix'])
                ->name('payments.hyperpay.debug.test-user-model-fix');
        }
    });
});
