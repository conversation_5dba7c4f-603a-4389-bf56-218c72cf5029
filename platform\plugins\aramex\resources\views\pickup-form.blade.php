<div class="container">

    <x-core::form :url="route('ecommerce.shipments.aramex.shipment.pickup.create', $shipment->id)">
        <input name="order_id" type="hidden" value="{{ $order->id }}" />

        <div class="row">
            <div class="col-md-12">
                <h3>Pickup Details</h3>
                <div class="row">
                    <div class="col-md-6">
                        <x-core::form.text-input 
                        :label="trans('Location')" 
                        :required="true"
                        readonly="readonly"
                        name="location" 
                        :value="trans('Reception')"
                        :placeholder="trans('Reception')"
                         />
                    </div>
                    <div class="col-md-6">
                        <x-core::form.select 
                        :label="trans('Vehicle Type')" 
                        name="vehicle" 
                        :required="true"
                        :options="['Bike' => 'Small (no specific vehicle required', 'Car'=>'Medium (regular car or small van)', 'Truck'=>'Large (large van or truck required)']" 
                       />
                    </div>
                     
                    <div class="col-md-4">
                        <div class="mb-3 position-relative">
                            <label class="form-label required" for="date">Date</label>
                            <input class="form-control" type="date" name="date" id="date" value="{{ $current_date }}" required="required" >
                        </div>
                         
                    </div>
                    <div class="col-md-4">
                        <label class="form-label required" for="date">{{ trans('Ready Time') }}</label>
                        <div class="row">
                            <div class="col-md-6">
                                <x-core::form.select 
                                name="ready_hour" 
                                :required="true"
                                :options="$hours" 
                            />
                            </div>
                            <div class="col-md-6">
                                <x-core::form.select 
                                name="ready_minute" 
                                :required="true"
                                :options="$minutes" 
                            />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label required" for="date">{{ trans('Closing Time') }}</label>
                        <div class="row">
                            <div class="col-md-6">
                                <x-core::form.select 
                                name="latest_hour" 
                                :required="true"
                                :options="$hours" 
                            />
                            </div>
                            <div class="col-md-6">
                                <x-core::form.select 
                                name="latest_minute" 
                                :required="true"
                                :options="$minutes" 
                            />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <x-core::form.text-input
                            :label="trans('Total Weight ('. ecommerce_weight_unit().')')"
                            :required="true"
                            name="weight"
                            :value="$shipment->weight"
                        />
                    </div>

                    <div class="col-md-4">
                        <x-core::form.select
                            :label="trans('Payment Type')"
                            name="payment_type"
                            :options="aramexPaymentTypes()"
                            :searchable="true"
                        />
                    </div>
                    <div class="col-md-4">
                        <x-core::form.select
                            :label="trans('Payment Option')"
                            name="payment_option"
                            :options="aramexPaymentOptions()"
                            :searchable="true"
                        />
                    </div>
                    <div class="col-md-4">
                        <div id="product_group">
                            <x-core::form.select
                            :label="trans('Product Group')"
                            name="product_group"
                            :options="aramexProductsGroups()"
                            :value="$product_group"
                            :searchable="true"
                        />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div id="allowed_domestic_methods" @if($product_group=='EXP') style="display: none;" @else  @endif>
                            <x-core::form.select
                            :label="trans('Product Type')"
                            name="allowed_domestic_methods"
                            :options="$allowed_domestic_methods"
                            :searchable="true"
                        />
                        </div>
                        <div id="allowed_international_methods" @if($product_group=='DOM') style="display: none;" @else @endif>
                            <x-core::form.select
                            :label="trans('Product Type')"
                            name="allowed_international_methods"
                            :options="$allowed_international_methods"
                            :searchable="true"
                        />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <x-core::form.text-input
                            :label="trans('Number of Pieces')"
                            :required="true"
                            name="number_of_pieces"
                            :value="$number_of_pieces"
                        />
                    </div>

                    <div class="col-md-4">
                        <x-core::form.select
                        :label="trans('Status')"
                        name="status"
                        :options="aramexPickupStatus()"
                        :searchable="true"
                    />
                       
                    </div>


                    <div class="col-md-12">
                        
                        <x-core::form.textarea 
                            :label="trans('Comments')" 
                            name="comments" 
                            :required="true"
                            />
                    </div>

                </div>
            </div>
            {{-- <div class="col-md-12">
                <h3>Address Information</h3>
                <div class="row">
                    <div class="col-md-6">
                        <x-core::form.text-input :label="trans('plugins/ecommerce::shipping.form_name')" :required="true" name="name" :value="$address->name"
                            :placeholder="trans('plugins/ecommerce::shipping.form_name')" />
                    </div>

                    <div class="col-md-6">
                        <x-core::form.text-input :label="trans('plugins/ecommerce::shipping.phone')" name="phone" :value="$address->phone" :placeholder="trans('plugins/ecommerce::shipping.phone')" />
                    </div>


                    <div class="col-md-6">
                        <x-core::form.text-input :label="trans('plugins/ecommerce::shipping.email')" type="email" name="email" :value="$address->email"
                            :placeholder="trans('plugins/ecommerce::shipping.email')" />
                    </div>

                    <div class="col-md-6">
                        @if (EcommerceHelper::isUsingInMultipleCountries())
                            <x-core::form.select :label="trans('plugins/ecommerce::shipping.country')" name="country" data-type="country"
                                :options="EcommerceHelper::getAvailableCountries()" :value="$address->country" :searchable="true" />
                        @else
                            <input name="country" type="hidden" value="{{ EcommerceHelper::getFirstCountryId() }}">
                        @endif
                    </div>

                    <div class="col-md-6">
                        @if (EcommerceHelper::loadCountriesStatesCitiesFromPluginLocation())
                            <x-core::form.select :label="trans('plugins/ecommerce::shipping.state')" name="state" data-type="state" :data-url="route('ajax.states-by-country')"
                                :searchable="true">
                                <option value="">{{ __('Select state...') }}</option>
                                @if ($address->state || !EcommerceHelper::isUsingInMultipleCountries())
                                    @foreach (EcommerceHelper::getAvailableStatesByCountry($address->country) as $stateId => $stateName)
                                        <option value="{{ $stateId }}"
                                            @if ($address->state == $stateId) selected @endif>{{ $stateName }}
                                        </option>
                                    @endforeach
                                @endif
                            </x-core::form.select>
                        @else
                            <x-core::form.text-input :label="trans('plugins/ecommerce::shipping.state')" name="state" :value="$address->state"
                                placeholder="{{ trans('plugins/ecommerce::shipping.state') }}" />
                        @endif
                    </div>

                    <div class="col-md-6">
                        @if (!EcommerceHelper::useCityFieldAsTextField())
                            <x-core::form.select :label="trans('plugins/ecommerce::shipping.city')" name="city" data-type="city"
                                data-using-select2="false" :data-url="route('ajax.cities-by-state')">
                                <option value="">{{ __('Select city...') }}</option>
                                @if ($address->city)
                                    @foreach (EcommerceHelper::getAvailableCitiesByState($address->state) as $cityId => $cityName)
                                        <option value="{{ $cityId }}"
                                            @if ($address->city == $cityId) selected @endif>{{ $cityName }}
                                        </option>
                                    @endforeach
                                @endif
                            </x-core::form.select>
                        @else
                            <x-core::form.text-input :label="trans('plugins/ecommerce::shipping.city')" name="city" :value="$address->city"
                                placeholder="{{ trans('plugins/ecommerce::shipping.city') }}" />
                        @endif
                    </div>

                    <div class="col-md-6">
                        <x-core::form.text-input :label="trans('plugins/ecommerce::shipping.address')" :required="true" name="address" :value="$address->address"
                            :placeholder="trans('plugins/ecommerce::shipping.address')" />
                    </div>

                    <div class="col-md-6">
                        @if (EcommerceHelper::isZipCodeEnabled())
                            <x-core::form.text-input :label="trans('plugins/ecommerce::shipping.zip_code')" name="zip_code" :value="$address->zip_code"
                                :placeholder="trans('plugins/ecommerce::shipping.zip_code')" />
                        @endif
                    </div>


                </div>
            </div> --}}
            <div class="col-md-12">
                <button class="btn btn-primary float-end" type="submit">
                    <span>{{ trans('Submit') }}</span>
                </button>
            </div>


    </x-core::form>
</div>
</div>
</div>
