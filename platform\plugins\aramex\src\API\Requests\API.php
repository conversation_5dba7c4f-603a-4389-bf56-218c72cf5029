<?php

namespace Shaqi\Aramex\API\Requests;

use Exception;
use Illuminate\Support\Facades\Log;
use Shaqi\Aramex\API\Classes\ClientInfo;
use <PERSON>haqi\Aramex\API\Classes\Transaction;
use <PERSON>haqi\Aramex\API\Interfaces\Normalize;
use SoapClient;
use SoapFault;

abstract class API implements Normalize
{
    /**
     * @var SoapClient $soapClient
     * @var ClientInfo $clientInfo
     * @var Transaction $transaction
     */
    protected $soapClient;
    protected $clientInfo;
    protected $transaction;
    protected $test_wsdl;
    protected $live_wsdl;
    protected $environment;

    /**
     * @throws SoapFault
     */
    public function __construct()
    {
        if(setting('shipping_aramex_test') == 1){
            $this->useTestAsEnvironment();
            // Log::info("ARAMEX TEST MODE:". setting('shipping_aramex_test'));

        }else{

            $this->useLiveAsEnvironment();
            // Log::info("ARAMEX LIVE MODE:". setting('shipping_aramex_test'));
        }

        $this->fillClientInfoFromEnv();

        
        Log::info("ARAMEX URL:". $this->getWsdlAccordingToEnvironment());

        $this->soapClient = new SoapClient($this->getWsdlAccordingToEnvironment(), array('trace' => 1));
    }

    public function setClientInfo(ClientInfo $clientInfo): API
    {
        $this->clientInfo = $clientInfo;
        return $this;
    }

    /**
     * @return ClientInfo
     */
    public function getClientInfo()
    {
        return $this->clientInfo;
    }

    /**
     * @return Transaction|null
     */
    public function getTransaction()
    {
        return $this->transaction;
    }

    /**
     * @param Transaction $transaction
     * @return $this
     */
    public function setTransaction(Transaction $transaction)
    {
        $this->transaction = $transaction;
        return $this;
    }

    /**
     * @param $environment
     * @return $this
     */
    protected function setEnvironment($environment)
    {
        $this->environment = $environment;
        return $this;
    }

    public function useTestAsEnvironment()
    {
        return $this->setEnvironment('test');
    }

    public function useLiveAsEnvironment()
    {
        return $this->setEnvironment('live');
    }

    /**
     * @return bool
     */
    public function isTest()
    {
        return $this->environment === "test";
    }

    /**
     * @return bool
     */
    public function isLive()
    {
        return $this->environment === "live";
    }

    /**
     * @return string
     */
    protected function getWsdlAccordingToEnvironment()
    {
        if ($this->isLive()) {
            return $this->live_wsdl;
        } else {
            return $this->test_wsdl;
        }
    }

    /**
     * @throws Exception
     */
    protected function validate()
    {
        if (!$this->clientInfo) {
            throw new Exception('Client Info Not Provided');
        }
    }

    /**
     * @return void
     */
    private function fillClientInfoFromEnv()
    {
        // $this->clientInfo = (new ClientInfo())
        //     ->setAccountCountryCode(config("plugins.aramex.aramex.$this->environment.country_code"))
        //     ->setAccountEntity(config("plugins.aramex.aramex.$this->environment.entity"))
        //     ->setAccountNumber(config("plugins.aramex.aramex.$this->environment.number"))
        //     ->setAccountPin(config("plugins.aramex.aramex.$this->environment.pin"))
        //     ->setUserName(config("plugins.aramex.aramex.$this->environment.username"))
        //     ->setPassword(config("plugins.aramex.aramex.$this->environment.password"));
        
        $this->clientInfo = (new ClientInfo())
            ->setAccountCountryCode(setting('shipping_aramex_account_country_code'))
            ->setAccountEntity(setting('shipping_aramex_account_entity'))
            ->setAccountNumber(setting('shipping_aramex_account_number'))
            ->setAccountPin(setting('shipping_aramex_account_pin'))
            ->setUserName(setting('shipping_aramex_username'))
            ->setPassword(setting('shipping_aramex_password'));

    }

    public function getAccountNumber()
    {
        return setting('shipping_aramex_account_number');
    }

    public function normalize()
    {
        return [
            'ClientInfo' => $this->getClientInfo()->normalize(),
            'Transaction' => optional($this->getTransaction())->normalize()
        ];
    }
}