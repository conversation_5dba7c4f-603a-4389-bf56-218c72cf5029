{"__meta": {"id": "01K3V7GFR278ECN3C6EDS1D8TB", "datetime": "2025-08-29 15:35:32", "utime": **********.354856, "method": "GET", "uri": "/admin/branches/edit/1", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 511, "start": 1756481728.43012, "end": **********.354873, "duration": 3.924752950668335, "duration_str": "3.92s", "measures": [{"label": "Booting", "start": 1756481728.43012, "relative_start": 0, "end": **********.318245, "relative_end": **********.318245, "duration": 0.***************, "duration_str": "888ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.318258, "relative_start": 0.****************, "end": **********.354875, "relative_end": 2.1457672119140625e-06, "duration": 3.****************, "duration_str": "3.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.339274, "relative_start": 0.***************, "end": **********.354124, "relative_end": **********.354124, "duration": 0.014850139617919922, "duration_str": "14.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/base::forms.form", "start": **********.607807, "relative_start": 1.****************, "end": **********.607807, "relative_end": **********.607807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.611576, "relative_start": 1.****************, "end": **********.611576, "relative_end": **********.611576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.612255, "relative_start": 1.****************, "end": **********.612255, "relative_end": **********.612255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.613123, "relative_start": 1.1830029487609863, "end": **********.613123, "relative_end": **********.613123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.614027, "relative_start": 1.1839070320129395, "end": **********.614027, "relative_end": **********.614027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.614773, "relative_start": 1.1846530437469482, "end": **********.614773, "relative_end": **********.614773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.615269, "relative_start": 1.1851489543914795, "end": **********.615269, "relative_end": **********.615269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.61714, "relative_start": 1.1870200634002686, "end": **********.61714, "relative_end": **********.61714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.61819, "relative_start": 1.1880700588226318, "end": **********.61819, "relative_end": **********.61819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.61867, "relative_start": 1.1885499954223633, "end": **********.61867, "relative_end": **********.61867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.619034, "relative_start": 1.1889140605926514, "end": **********.619034, "relative_end": **********.619034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.619448, "relative_start": 1.1893279552459717, "end": **********.619448, "relative_end": **********.619448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.619883, "relative_start": 1.189763069152832, "end": **********.619883, "relative_end": **********.619883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.620119, "relative_start": 1.1899991035461426, "end": **********.620119, "relative_end": **********.620119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.620385, "relative_start": 1.1902649402618408, "end": **********.620385, "relative_end": **********.620385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.620726, "relative_start": 1.1906061172485352, "end": **********.620726, "relative_end": **********.620726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.62109, "relative_start": 1.1909699440002441, "end": **********.62109, "relative_end": **********.62109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.621446, "relative_start": 1.1913259029388428, "end": **********.621446, "relative_end": **********.621446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.621807, "relative_start": 1.1916871070861816, "end": **********.621807, "relative_end": **********.621807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.622251, "relative_start": 1.1921310424804688, "end": **********.622251, "relative_end": **********.622251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.622466, "relative_start": 1.1923460960388184, "end": **********.622466, "relative_end": **********.622466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.622705, "relative_start": 1.1925849914550781, "end": **********.622705, "relative_end": **********.622705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.623012, "relative_start": 1.192892074584961, "end": **********.623012, "relative_end": **********.623012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.623371, "relative_start": 1.1932508945465088, "end": **********.623371, "relative_end": **********.623371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.623684, "relative_start": 1.1935639381408691, "end": **********.623684, "relative_end": **********.623684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.624017, "relative_start": 1.193897008895874, "end": **********.624017, "relative_end": **********.624017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.62444, "relative_start": 1.1943199634552002, "end": **********.62444, "relative_end": **********.62444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.624654, "relative_start": 1.1945340633392334, "end": **********.624654, "relative_end": **********.624654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.624891, "relative_start": 1.1947710514068604, "end": **********.624891, "relative_end": **********.624891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.625204, "relative_start": 1.1950840950012207, "end": **********.625204, "relative_end": **********.625204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.625534, "relative_start": 1.1954140663146973, "end": **********.625534, "relative_end": **********.625534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.625848, "relative_start": 1.195728063583374, "end": **********.625848, "relative_end": **********.625848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.626203, "relative_start": 1.1960830688476562, "end": **********.626203, "relative_end": **********.626203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.626606, "relative_start": 1.196485996246338, "end": **********.626606, "relative_end": **********.626606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.626822, "relative_start": 1.196702003479004, "end": **********.626822, "relative_end": **********.626822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.62706, "relative_start": 1.1969399452209473, "end": **********.62706, "relative_end": **********.62706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.627373, "relative_start": 1.1972529888153076, "end": **********.627373, "relative_end": **********.627373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.627704, "relative_start": 1.1975839138031006, "end": **********.627704, "relative_end": **********.627704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.628018, "relative_start": 1.1978979110717773, "end": **********.628018, "relative_end": **********.628018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.628369, "relative_start": 1.198249101638794, "end": **********.628369, "relative_end": **********.628369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.628769, "relative_start": 1.1986489295959473, "end": **********.628769, "relative_end": **********.628769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.628984, "relative_start": 1.1988639831542969, "end": **********.628984, "relative_end": **********.628984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.629321, "relative_start": 1.1992011070251465, "end": **********.629321, "relative_end": **********.629321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.629695, "relative_start": 1.1995749473571777, "end": **********.629695, "relative_end": **********.629695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.630043, "relative_start": 1.199923038482666, "end": **********.630043, "relative_end": **********.630043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.630416, "relative_start": 1.2002959251403809, "end": **********.630416, "relative_end": **********.630416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.630801, "relative_start": 1.2006809711456299, "end": **********.630801, "relative_end": **********.630801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.631238, "relative_start": 1.201117992401123, "end": **********.631238, "relative_end": **********.631238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.631448, "relative_start": 1.2013280391693115, "end": **********.631448, "relative_end": **********.631448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.63169, "relative_start": 1.2015700340270996, "end": **********.63169, "relative_end": **********.63169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.631991, "relative_start": 1.2018709182739258, "end": **********.631991, "relative_end": **********.631991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.632331, "relative_start": 1.2022109031677246, "end": **********.632331, "relative_end": **********.632331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.632652, "relative_start": 1.2025320529937744, "end": **********.632652, "relative_end": **********.632652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.632985, "relative_start": 1.2028651237487793, "end": **********.632985, "relative_end": **********.632985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.633408, "relative_start": 1.2032880783081055, "end": **********.633408, "relative_end": **********.633408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.633625, "relative_start": 1.203505039215088, "end": **********.633625, "relative_end": **********.633625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.63386, "relative_start": 1.203740119934082, "end": **********.63386, "relative_end": **********.63386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.634293, "relative_start": 1.2041730880737305, "end": **********.634293, "relative_end": **********.634293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.634728, "relative_start": 1.2046079635620117, "end": **********.634728, "relative_end": **********.634728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.635041, "relative_start": 1.204921007156372, "end": **********.635041, "relative_end": **********.635041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.63549, "relative_start": 1.2053699493408203, "end": **********.63549, "relative_end": **********.63549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.636702, "relative_start": 1.2065820693969727, "end": **********.636702, "relative_end": **********.636702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.637111, "relative_start": 1.2069909572601318, "end": **********.637111, "relative_end": **********.637111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.637343, "relative_start": 1.2072229385375977, "end": **********.637343, "relative_end": **********.637343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.637583, "relative_start": 1.207463026046753, "end": **********.637583, "relative_end": **********.637583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.637886, "relative_start": 1.207766056060791, "end": **********.637886, "relative_end": **********.637886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.638261, "relative_start": 1.2081410884857178, "end": **********.638261, "relative_end": **********.638261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.638596, "relative_start": 1.2084760665893555, "end": **********.638596, "relative_end": **********.638596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.638924, "relative_start": 1.2088038921356201, "end": **********.638924, "relative_end": **********.638924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.639345, "relative_start": 1.2092249393463135, "end": **********.639345, "relative_end": **********.639345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.63956, "relative_start": 1.209439992904663, "end": **********.63956, "relative_end": **********.63956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.639795, "relative_start": 1.2096750736236572, "end": **********.639795, "relative_end": **********.639795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.640209, "relative_start": 1.2100889682769775, "end": **********.640209, "relative_end": **********.640209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.640621, "relative_start": 1.210500955581665, "end": **********.640621, "relative_end": **********.640621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.640935, "relative_start": 1.2108149528503418, "end": **********.640935, "relative_end": **********.640935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.641287, "relative_start": 1.2111670970916748, "end": **********.641287, "relative_end": **********.641287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.64169, "relative_start": 1.2115700244903564, "end": **********.64169, "relative_end": **********.64169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.641903, "relative_start": 1.2117829322814941, "end": **********.641903, "relative_end": **********.641903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.642139, "relative_start": 1.2120189666748047, "end": **********.642139, "relative_end": **********.642139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.642448, "relative_start": 1.2123279571533203, "end": **********.642448, "relative_end": **********.642448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.642779, "relative_start": 1.2126591205596924, "end": **********.642779, "relative_end": **********.642779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.643092, "relative_start": 1.2129719257354736, "end": **********.643092, "relative_end": **********.643092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.643444, "relative_start": 1.2133240699768066, "end": **********.643444, "relative_end": **********.643444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.643845, "relative_start": 1.2137250900268555, "end": **********.643845, "relative_end": **********.643845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.644061, "relative_start": 1.2139410972595215, "end": **********.644061, "relative_end": **********.644061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.644317, "relative_start": 1.2141969203948975, "end": **********.644317, "relative_end": **********.644317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.644604, "relative_start": 1.2144839763641357, "end": **********.644604, "relative_end": **********.644604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.644931, "relative_start": 1.214811086654663, "end": **********.644931, "relative_end": **********.644931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.645332, "relative_start": 1.215212106704712, "end": **********.645332, "relative_end": **********.645332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.645675, "relative_start": 1.21555495262146, "end": **********.645675, "relative_end": **********.645675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.646077, "relative_start": 1.2159569263458252, "end": **********.646077, "relative_end": **********.646077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.646329, "relative_start": 1.2162089347839355, "end": **********.646329, "relative_end": **********.646329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.646577, "relative_start": 1.2164568901062012, "end": **********.646577, "relative_end": **********.646577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.64688, "relative_start": 1.2167599201202393, "end": **********.64688, "relative_end": **********.64688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.647255, "relative_start": 1.217134952545166, "end": **********.647255, "relative_end": **********.647255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.647604, "relative_start": 1.2174839973449707, "end": **********.647604, "relative_end": **********.647604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.648008, "relative_start": 1.2178881168365479, "end": **********.648008, "relative_end": **********.648008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.648618, "relative_start": 1.2184979915618896, "end": **********.648618, "relative_end": **********.648618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.64891, "relative_start": 1.218790054321289, "end": **********.64891, "relative_end": **********.64891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.649232, "relative_start": 1.2191119194030762, "end": **********.649232, "relative_end": **********.649232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.body.index", "start": **********.649684, "relative_start": 1.2195639610290527, "end": **********.649684, "relative_end": **********.649684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.650316, "relative_start": 1.220196008682251, "end": **********.650316, "relative_end": **********.650316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.653994, "relative_start": 1.2238740921020508, "end": **********.653994, "relative_end": **********.653994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-actions", "start": **********.65438, "relative_start": 1.2242600917816162, "end": **********.65438, "relative_end": **********.65438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.655503, "relative_start": 1.2253830432891846, "end": **********.655503, "relative_end": **********.655503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.655957, "relative_start": 1.225836992263794, "end": **********.655957, "relative_end": **********.655957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.656401, "relative_start": 1.226280927658081, "end": **********.656401, "relative_end": **********.656401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.65709, "relative_start": 1.2269699573516846, "end": **********.65709, "relative_end": **********.65709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d212f3e1ce1fc54192f06b7fe29ef932", "start": **********.659913, "relative_start": 1.2297930717468262, "end": **********.659913, "relative_end": **********.659913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.661782, "relative_start": 1.2316620349884033, "end": **********.661782, "relative_end": **********.661782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a11b38818686570b6e20907301bf062c", "start": **********.662948, "relative_start": 1.23282790184021, "end": **********.662948, "relative_end": **********.662948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.body.index", "start": **********.663852, "relative_start": 1.233731985092163, "end": **********.663852, "relative_end": **********.663852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.664076, "relative_start": 1.2339560985565186, "end": **********.664076, "relative_end": **********.664076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.breadcrumbs", "start": **********.667162, "relative_start": 1.23704195022583, "end": **********.667162, "relative_end": **********.667162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.667502, "relative_start": 1.237381935119629, "end": **********.667502, "relative_end": **********.667502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.668065, "relative_start": 1.2379450798034668, "end": **********.668065, "relative_end": **********.668065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d212f3e1ce1fc54192f06b7fe29ef932", "start": **********.668648, "relative_start": 1.2385280132293701, "end": **********.668648, "relative_end": **********.668648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.668913, "relative_start": 1.238792896270752, "end": **********.668913, "relative_end": **********.668913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a11b38818686570b6e20907301bf062c", "start": **********.669466, "relative_start": 1.2393460273742676, "end": **********.669466, "relative_end": **********.669466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.671496, "relative_start": 1.2413759231567383, "end": **********.671496, "relative_end": **********.671496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.671779, "relative_start": 1.2416589260101318, "end": **********.671779, "relative_end": **********.671779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.672107, "relative_start": 1.2419869899749756, "end": **********.672107, "relative_end": **********.672107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.67242, "relative_start": 1.242300033569336, "end": **********.67242, "relative_end": **********.67242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.672726, "relative_start": 1.2426059246063232, "end": **********.672726, "relative_end": **********.672726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.67321, "relative_start": 1.2430899143218994, "end": **********.67321, "relative_end": **********.67321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.673588, "relative_start": 1.2434680461883545, "end": **********.673588, "relative_end": **********.673588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.673922, "relative_start": 1.2438020706176758, "end": **********.673922, "relative_end": **********.673922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.674351, "relative_start": 1.2442309856414795, "end": **********.674351, "relative_end": **********.674351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.674588, "relative_start": 1.2444679737091064, "end": **********.674588, "relative_end": **********.674588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.674879, "relative_start": 1.2447590827941895, "end": **********.674879, "relative_end": **********.674879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.675146, "relative_start": 1.2450261116027832, "end": **********.675146, "relative_end": **********.675146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.675489, "relative_start": 1.2453689575195312, "end": **********.675489, "relative_end": **********.675489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.675824, "relative_start": 1.245703935623169, "end": **********.675824, "relative_end": **********.675824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.676147, "relative_start": 1.2460269927978516, "end": **********.676147, "relative_end": **********.676147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.676453, "relative_start": 1.246333122253418, "end": **********.676453, "relative_end": **********.676453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.676991, "relative_start": 1.246870994567871, "end": **********.676991, "relative_end": **********.676991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.677607, "relative_start": 1.2474870681762695, "end": **********.677607, "relative_end": **********.677607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.toggle", "start": **********.678163, "relative_start": 1.2480430603027344, "end": **********.678163, "relative_end": **********.678163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.678828, "relative_start": 1.2487080097198486, "end": **********.678828, "relative_end": **********.678828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.67933, "relative_start": 1.2492101192474365, "end": **********.67933, "relative_end": **********.67933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.679584, "relative_start": 1.2494640350341797, "end": **********.679584, "relative_end": **********.679584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.679864, "relative_start": 1.249743938446045, "end": **********.679864, "relative_end": **********.679864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.680145, "relative_start": 1.2500250339508057, "end": **********.680145, "relative_end": **********.680145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.68053, "relative_start": 1.2504100799560547, "end": **********.68053, "relative_end": **********.68053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.680895, "relative_start": 1.2507750988006592, "end": **********.680895, "relative_end": **********.680895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.681259, "relative_start": 1.2511389255523682, "end": **********.681259, "relative_end": **********.681259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.681618, "relative_start": 1.2514979839324951, "end": **********.681618, "relative_end": **********.681618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.68212, "relative_start": 1.252000093460083, "end": **********.68212, "relative_end": **********.68212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.682526, "relative_start": 1.252406120300293, "end": **********.682526, "relative_end": **********.682526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.toggle", "start": **********.682883, "relative_start": 1.252763032913208, "end": **********.682883, "relative_end": **********.682883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.683268, "relative_start": 1.253148078918457, "end": **********.683268, "relative_end": **********.683268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.683769, "relative_start": 1.2536489963531494, "end": **********.683769, "relative_end": **********.683769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.684013, "relative_start": 1.2538928985595703, "end": **********.684013, "relative_end": **********.684013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.684283, "relative_start": 1.2541630268096924, "end": **********.684283, "relative_end": **********.684283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.684547, "relative_start": 1.2544269561767578, "end": **********.684547, "relative_end": **********.684547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.684899, "relative_start": 1.2547791004180908, "end": **********.684899, "relative_end": **********.684899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.685237, "relative_start": 1.2551169395446777, "end": **********.685237, "relative_end": **********.685237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.685561, "relative_start": 1.2554409503936768, "end": **********.685561, "relative_end": **********.685561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.685868, "relative_start": 1.2557480335235596, "end": **********.685868, "relative_end": **********.685868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": **********.686285, "relative_start": 1.2561650276184082, "end": **********.686285, "relative_end": **********.686285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": **********.686844, "relative_start": 1.2567241191864014, "end": **********.686844, "relative_end": **********.686844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.image", "start": **********.687347, "relative_start": 1.2572269439697266, "end": **********.687347, "relative_end": **********.687347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::image", "start": **********.6884, "relative_start": 1.2582800388336182, "end": **********.6884, "relative_end": **********.6884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.688832, "relative_start": 1.2587120532989502, "end": **********.688832, "relative_end": **********.688832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::14ad31fb3af14d3ba24d3c578af35e73", "start": **********.689978, "relative_start": 1.2598578929901123, "end": **********.689978, "relative_end": **********.689978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.691441, "relative_start": 1.2613210678100586, "end": **********.691441, "relative_end": **********.691441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.691853, "relative_start": 1.261733055114746, "end": **********.691853, "relative_end": **********.691853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.692116, "relative_start": 1.2619960308074951, "end": **********.692116, "relative_end": **********.692116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.692396, "relative_start": 1.2622759342193604, "end": **********.692396, "relative_end": **********.692396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.69268, "relative_start": 1.2625598907470703, "end": **********.69268, "relative_end": **********.69268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.693011, "relative_start": 1.2628910541534424, "end": **********.693011, "relative_end": **********.693011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.693352, "relative_start": 1.2632319927215576, "end": **********.693352, "relative_end": **********.693352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.693654, "relative_start": 1.2635340690612793, "end": **********.693654, "relative_end": **********.693654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.693939, "relative_start": 1.2638189792633057, "end": **********.693939, "relative_end": **********.693939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.694206, "relative_start": 1.2640860080718994, "end": **********.694206, "relative_end": **********.694206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.694607, "relative_start": 1.2644870281219482, "end": **********.694607, "relative_end": **********.694607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.695012, "relative_start": 1.2648921012878418, "end": **********.695012, "relative_end": **********.695012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.695217, "relative_start": 1.26509690284729, "end": **********.695217, "relative_end": **********.695217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.69545, "relative_start": 1.2653300762176514, "end": **********.69545, "relative_end": **********.69545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.695687, "relative_start": 1.2655670642852783, "end": **********.695687, "relative_end": **********.695687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.696002, "relative_start": 1.2658820152282715, "end": **********.696002, "relative_end": **********.696002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.69636, "relative_start": 1.266240119934082, "end": **********.69636, "relative_end": **********.69636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.696701, "relative_start": 1.2665810585021973, "end": **********.696701, "relative_end": **********.696701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.697016, "relative_start": 1.2668960094451904, "end": **********.697016, "relative_end": **********.697016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.697355, "relative_start": 1.2672350406646729, "end": **********.697355, "relative_end": **********.697355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.field", "start": **********.697783, "relative_start": 1.2676630020141602, "end": **********.697783, "relative_end": **********.697783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.698418, "relative_start": 1.2682979106903076, "end": **********.698418, "relative_end": **********.698418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.698864, "relative_start": 1.2687439918518066, "end": **********.698864, "relative_end": **********.698864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.699267, "relative_start": 1.2691469192504883, "end": **********.699267, "relative_end": **********.699267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.699646, "relative_start": 1.2695260047912598, "end": **********.699646, "relative_end": **********.699646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.702654, "relative_start": 1.272533893585205, "end": **********.702654, "relative_end": **********.702654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.733985, "relative_start": 1.3038649559020996, "end": **********.733985, "relative_end": **********.733985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.735283, "relative_start": 1.3051629066467285, "end": **********.735283, "relative_end": **********.735283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.736195, "relative_start": 1.306075096130371, "end": **********.736195, "relative_end": **********.736195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.736628, "relative_start": 1.3065080642700195, "end": **********.736628, "relative_end": **********.736628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.737953, "relative_start": 1.307832956314087, "end": **********.737953, "relative_end": **********.737953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.738654, "relative_start": 1.3085339069366455, "end": **********.738654, "relative_end": **********.738654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.750997, "relative_start": 1.3208770751953125, "end": **********.750997, "relative_end": **********.750997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.751651, "relative_start": 1.321531057357788, "end": **********.751651, "relative_end": **********.751651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.752604, "relative_start": 1.322484016418457, "end": **********.752604, "relative_end": **********.752604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.7531, "relative_start": 1.3229799270629883, "end": **********.7531, "relative_end": **********.7531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.753533, "relative_start": 1.3234128952026367, "end": **********.753533, "relative_end": **********.753533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.766635, "relative_start": 1.336514949798584, "end": **********.766635, "relative_end": **********.766635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d2cfde89f704c31422aff2fae16ddb81", "start": **********.768036, "relative_start": 1.3379158973693848, "end": **********.768036, "relative_end": **********.768036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.768751, "relative_start": 1.3386309146881104, "end": **********.768751, "relative_end": **********.768751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a5645d2a1f3c74251fc89224c575fed8", "start": **********.76992, "relative_start": 1.3398001194000244, "end": **********.76992, "relative_end": **********.76992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.776099, "relative_start": 1.3459789752960205, "end": **********.776099, "relative_end": **********.776099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::639d159f54869d7a8362974885dec505", "start": **********.777402, "relative_start": 1.3472819328308105, "end": **********.777402, "relative_end": **********.777402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.788036, "relative_start": 1.3579161167144775, "end": **********.788036, "relative_end": **********.788036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.789606, "relative_start": 1.3594861030578613, "end": **********.789606, "relative_end": **********.789606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.790655, "relative_start": 1.360534906387329, "end": **********.790655, "relative_end": **********.790655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.actions", "start": **********.791288, "relative_start": 1.3611679077148438, "end": **********.791288, "relative_end": **********.791288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.791775, "relative_start": 1.3616549968719482, "end": **********.791775, "relative_end": **********.791775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.747033, "relative_start": 3.31691312789917, "end": **********.747033, "relative_end": **********.747033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.notification", "start": **********.764523, "relative_start": 3.3344030380249023, "end": **********.764523, "relative_end": **********.764523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "start": **********.766235, "relative_start": 3.3361151218414307, "end": **********.766235, "relative_end": **********.766235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.768662, "relative_start": 3.3385419845581055, "end": **********.768662, "relative_end": **********.768662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.783254, "relative_start": 3.3531339168548584, "end": **********.783254, "relative_end": **********.783254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.784107, "relative_start": 3.353986978530884, "end": **********.784107, "relative_end": **********.784107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.795966, "relative_start": 3.3658459186553955, "end": **********.795966, "relative_end": **********.795966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.797492, "relative_start": 3.3673720359802246, "end": **********.797492, "relative_end": **********.797492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.798203, "relative_start": 3.3680830001831055, "end": **********.798203, "relative_end": **********.798203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.799453, "relative_start": 3.369333028793335, "end": **********.799453, "relative_end": **********.799453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.800063, "relative_start": 3.3699429035186768, "end": **********.800063, "relative_end": **********.800063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.800848, "relative_start": 3.370728015899658, "end": **********.800848, "relative_end": **********.800848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.801677, "relative_start": 3.3715569972991943, "end": **********.801677, "relative_end": **********.801677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.801931, "relative_start": 3.3718109130859375, "end": **********.801931, "relative_end": **********.801931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.811097, "relative_start": 3.380976915359497, "end": **********.811097, "relative_end": **********.811097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.811756, "relative_start": 3.381635904312134, "end": **********.811756, "relative_end": **********.811756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.812109, "relative_start": 3.381989002227783, "end": **********.812109, "relative_end": **********.812109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.812766, "relative_start": 3.382646083831787, "end": **********.812766, "relative_end": **********.812766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.813014, "relative_start": 3.3828940391540527, "end": **********.813014, "relative_end": **********.813014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.813587, "relative_start": 3.383466958999634, "end": **********.813587, "relative_end": **********.813587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.814008, "relative_start": 3.383888006210327, "end": **********.814008, "relative_end": **********.814008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.832837, "relative_start": 3.402717113494873, "end": **********.832837, "relative_end": **********.832837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.833877, "relative_start": 3.403757095336914, "end": **********.833877, "relative_end": **********.833877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3b17f7ce9738894b58a8b70b9624457", "start": **********.83534, "relative_start": 3.4052200317382812, "end": **********.83534, "relative_end": **********.83534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.836292, "relative_start": 3.406172037124634, "end": **********.836292, "relative_end": **********.836292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.836996, "relative_start": 3.4068760871887207, "end": **********.836996, "relative_end": **********.836996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c6e6838aa476b78aace81114936689c", "start": **********.838759, "relative_start": 3.4086389541625977, "end": **********.838759, "relative_end": **********.838759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.839847, "relative_start": 3.409727096557617, "end": **********.839847, "relative_end": **********.839847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.840381, "relative_start": 3.4102609157562256, "end": **********.840381, "relative_end": **********.840381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.840875, "relative_start": 3.410754919052124, "end": **********.840875, "relative_end": **********.840875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::37fae22c8e215ea2e54e69a5e3a007cc", "start": **********.842569, "relative_start": 3.4124491214752197, "end": **********.842569, "relative_end": **********.842569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.843522, "relative_start": 3.4134020805358887, "end": **********.843522, "relative_end": **********.843522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8916176d99d4ae2024cd36e11e35b821", "start": **********.844636, "relative_start": 3.414515972137451, "end": **********.844636, "relative_end": **********.844636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.845476, "relative_start": 3.415355920791626, "end": **********.845476, "relative_end": **********.845476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.845753, "relative_start": 3.415632963180542, "end": **********.845753, "relative_end": **********.845753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.846122, "relative_start": 3.416002035140991, "end": **********.846122, "relative_end": **********.846122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd27433a6607127acdaf6dc541ab2435", "start": **********.847214, "relative_start": 3.4170939922332764, "end": **********.847214, "relative_end": **********.847214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.848045, "relative_start": 3.4179251194000244, "end": **********.848045, "relative_end": **********.848045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "start": **********.849282, "relative_start": 3.4191620349884033, "end": **********.849282, "relative_end": **********.849282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.85022, "relative_start": 3.420099973678589, "end": **********.85022, "relative_end": **********.85022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.850704, "relative_start": 3.420583963394165, "end": **********.850704, "relative_end": **********.850704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.851249, "relative_start": 3.421128988265991, "end": **********.851249, "relative_end": **********.851249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::080b92e00b37bcc97c1cd249894494a2", "start": **********.853728, "relative_start": 3.4236080646514893, "end": **********.853728, "relative_end": **********.853728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.854646, "relative_start": 3.4245259761810303, "end": **********.854646, "relative_end": **********.854646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6a26943e77184871de1629f41c534094", "start": **********.85634, "relative_start": 3.426219940185547, "end": **********.85634, "relative_end": **********.85634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.857182, "relative_start": 3.4270620346069336, "end": **********.857182, "relative_end": **********.857182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8f29f8012139c7a3eb6593c906e1db38", "start": **********.858808, "relative_start": 3.4286880493164062, "end": **********.858808, "relative_end": **********.858808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.859719, "relative_start": 3.4295990467071533, "end": **********.859719, "relative_end": **********.859719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.86001, "relative_start": 3.4298899173736572, "end": **********.86001, "relative_end": **********.86001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.860426, "relative_start": 3.4303059577941895, "end": **********.860426, "relative_end": **********.860426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4553f5b37130b2effba490dbdf5419d2", "start": **********.86196, "relative_start": 3.431839942932129, "end": **********.86196, "relative_end": **********.86196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.862553, "relative_start": 3.4324328899383545, "end": **********.862553, "relative_end": **********.862553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59c947fc9b2121a5885d4f4e7b1242d8", "start": **********.864061, "relative_start": 3.433941125869751, "end": **********.864061, "relative_end": **********.864061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.864896, "relative_start": 3.4347760677337646, "end": **********.864896, "relative_end": **********.864896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "start": **********.866216, "relative_start": 3.436095952987671, "end": **********.866216, "relative_end": **********.866216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.867197, "relative_start": 3.437077045440674, "end": **********.867197, "relative_end": **********.867197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.868435, "relative_start": 3.438314914703369, "end": **********.868435, "relative_end": **********.868435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.869303, "relative_start": 3.439182996749878, "end": **********.869303, "relative_end": **********.869303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.870904, "relative_start": 3.440783977508545, "end": **********.870904, "relative_end": **********.870904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.871799, "relative_start": 3.441679000854492, "end": **********.871799, "relative_end": **********.871799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::84f17fac377525e2e49f32058361220b", "start": **********.873405, "relative_start": 3.4432849884033203, "end": **********.873405, "relative_end": **********.873405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.87429, "relative_start": 3.4441699981689453, "end": **********.87429, "relative_end": **********.87429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.875414, "relative_start": 3.44529390335083, "end": **********.875414, "relative_end": **********.875414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.875997, "relative_start": 3.4458770751953125, "end": **********.875997, "relative_end": **********.875997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c8617dee734f51544a3883923ddca6f", "start": **********.877523, "relative_start": 3.4474029541015625, "end": **********.877523, "relative_end": **********.877523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.878375, "relative_start": 3.4482550621032715, "end": **********.878375, "relative_end": **********.878375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3d3bfe5e8598abeb74083f6c26233cb5", "start": **********.87948, "relative_start": 3.449359893798828, "end": **********.87948, "relative_end": **********.87948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.880301, "relative_start": 3.450181007385254, "end": **********.880301, "relative_end": **********.880301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::35fe997a7b87ef55d749630606a50a1b", "start": **********.881873, "relative_start": 3.4517529010772705, "end": **********.881873, "relative_end": **********.881873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.885385, "relative_start": 3.4552650451660156, "end": **********.885385, "relative_end": **********.885385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a4f1583597dec7e67a8ae044f0915dbe", "start": **********.887508, "relative_start": 3.457387924194336, "end": **********.887508, "relative_end": **********.887508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.888478, "relative_start": 3.4583580493927, "end": **********.888478, "relative_end": **********.888478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c0cbd16b0cc2226ec5536610974ba3c3", "start": **********.890265, "relative_start": 3.4601449966430664, "end": **********.890265, "relative_end": **********.890265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.891111, "relative_start": 3.4609909057617188, "end": **********.891111, "relative_end": **********.891111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.892702, "relative_start": 3.4625821113586426, "end": **********.892702, "relative_end": **********.892702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.893559, "relative_start": 3.4634389877319336, "end": **********.893559, "relative_end": **********.893559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.894253, "relative_start": 3.4641330242156982, "end": **********.894253, "relative_end": **********.894253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc78b90963e9d9963376e0e829411cea", "start": **********.895835, "relative_start": 3.465714931488037, "end": **********.895835, "relative_end": **********.895835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.896696, "relative_start": 3.466576099395752, "end": **********.896696, "relative_end": **********.896696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.89779, "relative_start": 3.46766996383667, "end": **********.89779, "relative_end": **********.89779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.898655, "relative_start": 3.4685349464416504, "end": **********.898655, "relative_end": **********.898655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::481a833ebeb573258c941c925aa45f7b", "start": **********.899953, "relative_start": 3.4698328971862793, "end": **********.899953, "relative_end": **********.899953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.901082, "relative_start": 3.4709620475769043, "end": **********.901082, "relative_end": **********.901082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b42bb0aa5fceca31ad61711414a614f0", "start": **********.902342, "relative_start": 3.472222089767456, "end": **********.902342, "relative_end": **********.902342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.903236, "relative_start": 3.473115921020508, "end": **********.903236, "relative_end": **********.903236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.904142, "relative_start": 3.4740219116210938, "end": **********.904142, "relative_end": **********.904142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.90533, "relative_start": 3.475209951400757, "end": **********.90533, "relative_end": **********.90533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.906266, "relative_start": 3.4761459827423096, "end": **********.906266, "relative_end": **********.906266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.906576, "relative_start": 3.4764559268951416, "end": **********.906576, "relative_end": **********.906576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.907009, "relative_start": 3.47688889503479, "end": **********.907009, "relative_end": **********.907009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::471e83668278198d730a7a3f4a475d45", "start": **********.908143, "relative_start": 3.478023052215576, "end": **********.908143, "relative_end": **********.908143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.909055, "relative_start": 3.4789350032806396, "end": **********.909055, "relative_end": **********.909055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.909708, "relative_start": 3.479588031768799, "end": **********.909708, "relative_end": **********.909708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.910267, "relative_start": 3.480147123336792, "end": **********.910267, "relative_end": **********.910267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "start": **********.911983, "relative_start": 3.481863021850586, "end": **********.911983, "relative_end": **********.911983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.912901, "relative_start": 3.482780933380127, "end": **********.912901, "relative_end": **********.912901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.9132, "relative_start": 3.4830799102783203, "end": **********.9132, "relative_end": **********.9132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.913586, "relative_start": 3.4834659099578857, "end": **********.913586, "relative_end": **********.913586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.914245, "relative_start": 3.4841248989105225, "end": **********.914245, "relative_end": **********.914245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.914799, "relative_start": 3.4846789836883545, "end": **********.914799, "relative_end": **********.914799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acb69140835a74210411469faeab3034", "start": **********.916642, "relative_start": 3.4865219593048096, "end": **********.916642, "relative_end": **********.916642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.917813, "relative_start": 3.4876930713653564, "end": **********.917813, "relative_end": **********.917813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.918378, "relative_start": 3.488258123397827, "end": **********.918378, "relative_end": **********.918378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.918911, "relative_start": 3.488790988922119, "end": **********.918911, "relative_end": **********.918911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19cd49cd69455e40dc223df6b4eaf954", "start": **********.921137, "relative_start": 3.4910171031951904, "end": **********.921137, "relative_end": **********.921137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.922153, "relative_start": 3.492033004760742, "end": **********.922153, "relative_end": **********.922153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.92288, "relative_start": 3.492759943008423, "end": **********.92288, "relative_end": **********.92288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2979b72aeeca0047ecdecc3ad66e7e16", "start": **********.924041, "relative_start": 3.4939210414886475, "end": **********.924041, "relative_end": **********.924041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.924954, "relative_start": 3.4948339462280273, "end": **********.924954, "relative_end": **********.924954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.925651, "relative_start": 3.4955310821533203, "end": **********.925651, "relative_end": **********.925651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3c40febd70fcdc245d99ae7cd02cface", "start": **********.926785, "relative_start": 3.4966650009155273, "end": **********.926785, "relative_end": **********.926785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.927698, "relative_start": 3.4975779056549072, "end": **********.927698, "relative_end": **********.927698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.928818, "relative_start": 3.4986979961395264, "end": **********.928818, "relative_end": **********.928818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.929739, "relative_start": 3.4996190071105957, "end": **********.929739, "relative_end": **********.929739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.930803, "relative_start": 3.500683069229126, "end": **********.930803, "relative_end": **********.930803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.931366, "relative_start": 3.5012459754943848, "end": **********.931366, "relative_end": **********.931366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.932631, "relative_start": 3.5025110244750977, "end": **********.932631, "relative_end": **********.932631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.933325, "relative_start": 3.5032050609588623, "end": **********.933325, "relative_end": **********.933325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.93428, "relative_start": 3.504159927368164, "end": **********.93428, "relative_end": **********.93428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7ced212b797c29086a7922a858f3070", "start": **********.936677, "relative_start": 3.506556987762451, "end": **********.936677, "relative_end": **********.936677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.937671, "relative_start": 3.5075509548187256, "end": **********.937671, "relative_end": **********.937671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.938007, "relative_start": 3.507887125015259, "end": **********.938007, "relative_end": **********.938007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.938506, "relative_start": 3.5083858966827393, "end": **********.938506, "relative_end": **********.938506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.939849, "relative_start": 3.5097289085388184, "end": **********.939849, "relative_end": **********.939849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.940773, "relative_start": 3.510653018951416, "end": **********.940773, "relative_end": **********.940773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.941099, "relative_start": 3.510978937149048, "end": **********.941099, "relative_end": **********.941099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.94157, "relative_start": 3.5114500522613525, "end": **********.94157, "relative_end": **********.94157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.942319, "relative_start": 3.5121989250183105, "end": **********.942319, "relative_end": **********.942319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.942913, "relative_start": 3.5127930641174316, "end": **********.942913, "relative_end": **********.942913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.944222, "relative_start": 3.514101982116699, "end": **********.944222, "relative_end": **********.944222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.945199, "relative_start": 3.5150790214538574, "end": **********.945199, "relative_end": **********.945199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.945979, "relative_start": 3.5158591270446777, "end": **********.945979, "relative_end": **********.945979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.946929, "relative_start": 3.5168089866638184, "end": **********.946929, "relative_end": **********.946929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.947658, "relative_start": 3.517538070678711, "end": **********.947658, "relative_end": **********.947658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.948428, "relative_start": 3.51830792427063, "end": **********.948428, "relative_end": **********.948428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8c52d9b1ef0685ec10fdc3e877751e02", "start": **********.949909, "relative_start": 3.519788980484009, "end": **********.949909, "relative_end": **********.949909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.951345, "relative_start": 3.5212249755859375, "end": **********.951345, "relative_end": **********.951345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.95239, "relative_start": 3.5222699642181396, "end": **********.95239, "relative_end": **********.95239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42668be6e8e5266862c6994eaa88bb55", "start": **********.954299, "relative_start": 3.524178981781006, "end": **********.954299, "relative_end": **********.954299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.955231, "relative_start": 3.525110960006714, "end": **********.955231, "relative_end": **********.955231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.955552, "relative_start": 3.5254321098327637, "end": **********.955552, "relative_end": **********.955552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.956023, "relative_start": 3.5259029865264893, "end": **********.956023, "relative_end": **********.956023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.956789, "relative_start": 3.5266690254211426, "end": **********.956789, "relative_end": **********.956789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "start": **********.958141, "relative_start": 3.5280210971832275, "end": **********.958141, "relative_end": **********.958141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.959066, "relative_start": 3.5289459228515625, "end": **********.959066, "relative_end": **********.959066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.959815, "relative_start": 3.5296950340270996, "end": **********.959815, "relative_end": **********.959815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.960389, "relative_start": 3.530268907546997, "end": **********.960389, "relative_end": **********.960389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.9611, "relative_start": 3.530980110168457, "end": **********.9611, "relative_end": **********.9611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.96168, "relative_start": 3.531559944152832, "end": **********.96168, "relative_end": **********.96168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.962392, "relative_start": 3.5322721004486084, "end": **********.962392, "relative_end": **********.962392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.963074, "relative_start": 3.532953977584839, "end": **********.963074, "relative_end": **********.963074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.963586, "relative_start": 3.533466100692749, "end": **********.963586, "relative_end": **********.963586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.963861, "relative_start": 3.533740997314453, "end": **********.963861, "relative_end": **********.963861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.964259, "relative_start": 3.5341389179229736, "end": **********.964259, "relative_end": **********.964259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::613233f0072612a02c74dd1699c0b74c", "start": **********.965603, "relative_start": 3.5354831218719482, "end": **********.965603, "relative_end": **********.965603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.966515, "relative_start": 3.5363950729370117, "end": **********.966515, "relative_end": **********.966515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16225ede2ef5cc17292fd2eb9026fc80", "start": **********.967826, "relative_start": 3.537705898284912, "end": **********.967826, "relative_end": **********.967826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.968721, "relative_start": 3.5386009216308594, "end": **********.968721, "relative_end": **********.968721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.969424, "relative_start": 3.53930401802063, "end": **********.969424, "relative_end": **********.969424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46010cb1cb88bb5ead5d94603a4a3d16", "start": **********.9706, "relative_start": 3.540479898452759, "end": **********.9706, "relative_end": **********.9706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.971456, "relative_start": 3.5413360595703125, "end": **********.971456, "relative_end": **********.971456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.97223, "relative_start": 3.542109966278076, "end": **********.97223, "relative_end": **********.97223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::34e3d89351b1208b7f313125eec52879", "start": **********.973976, "relative_start": 3.543855905532837, "end": **********.973976, "relative_end": **********.973976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.974833, "relative_start": 3.544713020324707, "end": **********.974833, "relative_end": **********.974833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::67034900569133b2c83b32da3dd4f5e5", "start": **********.975963, "relative_start": 3.5458431243896484, "end": **********.975963, "relative_end": **********.975963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.976831, "relative_start": 3.546710968017578, "end": **********.976831, "relative_end": **********.976831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.977882, "relative_start": 3.547761917114258, "end": **********.977882, "relative_end": **********.977882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.978431, "relative_start": 3.5483109951019287, "end": **********.978431, "relative_end": **********.978431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.979112, "relative_start": 3.5489919185638428, "end": **********.979112, "relative_end": **********.979112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.97976, "relative_start": 3.549639940261841, "end": **********.97976, "relative_end": **********.97976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.980313, "relative_start": 3.5501930713653564, "end": **********.980313, "relative_end": **********.980313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.981138, "relative_start": 3.551017999649048, "end": **********.981138, "relative_end": **********.981138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.982769, "relative_start": 3.5526490211486816, "end": **********.982769, "relative_end": **********.982769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.983956, "relative_start": 3.5538361072540283, "end": **********.983956, "relative_end": **********.983956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d623715926c24f9fbc8a4b72c106d5d", "start": **********.986401, "relative_start": 3.556281089782715, "end": **********.986401, "relative_end": **********.986401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.987385, "relative_start": 3.557265043258667, "end": **********.987385, "relative_end": **********.987385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6c50fc55276d93f8ed03f5c85273b6cc", "start": **********.989165, "relative_start": 3.5590450763702393, "end": **********.989165, "relative_end": **********.989165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.990061, "relative_start": 3.559941053390503, "end": **********.990061, "relative_end": **********.990061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::084d2b43c9ab4b881d9b34a15580aa2d", "start": **********.991679, "relative_start": 3.561558961868286, "end": **********.991679, "relative_end": **********.991679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.992586, "relative_start": 3.5624659061431885, "end": **********.992586, "relative_end": **********.992586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.994217, "relative_start": 3.5640969276428223, "end": **********.994217, "relative_end": **********.994217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.995089, "relative_start": 3.564969062805176, "end": **********.995089, "relative_end": **********.995089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7db8cad89359963c1e9aa8fcc6c89817", "start": **********.996766, "relative_start": 3.566646099090576, "end": **********.996766, "relative_end": **********.996766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.997643, "relative_start": 3.5675230026245117, "end": **********.997643, "relative_end": **********.997643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.99835, "relative_start": 3.568229913711548, "end": **********.99835, "relative_end": **********.99835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.999111, "relative_start": 3.56899094581604, "end": **********.999111, "relative_end": **********.999111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.999725, "relative_start": 3.5696051120758057, "end": **********.999725, "relative_end": **********.999725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.000488, "relative_start": 3.5703680515289307, "end": **********.000488, "relative_end": **********.000488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::325be2a8c3ca3843efa76c03adaee1dc", "start": **********.002473, "relative_start": 3.5723531246185303, "end": **********.002473, "relative_end": **********.002473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.003419, "relative_start": 3.573298931121826, "end": **********.003419, "relative_end": **********.003419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e12a669ffa0346a27198bed32e63b7ba", "start": **********.004667, "relative_start": 3.574547052383423, "end": **********.004667, "relative_end": **********.004667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.00559, "relative_start": 3.575469970703125, "end": **********.00559, "relative_end": **********.00559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d0b20db301db9a47503a93a879bb206", "start": **********.00679, "relative_start": 3.576669931411743, "end": **********.00679, "relative_end": **********.00679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.007715, "relative_start": 3.5775949954986572, "end": **********.007715, "relative_end": **********.007715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b985af7bcdacbeac70eaf3979ad19f5a", "start": **********.008909, "relative_start": 3.578788995742798, "end": **********.008909, "relative_end": **********.008909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.009813, "relative_start": 3.579693078994751, "end": **********.009813, "relative_end": **********.009813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a0bb1d43b71cff86abe626fd376492e9", "start": **********.01098, "relative_start": 3.580859899520874, "end": **********.01098, "relative_end": **********.01098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.012047, "relative_start": 3.5819270610809326, "end": **********.012047, "relative_end": **********.012047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e25f2b305e6de46c04f91fa1ce50f68f", "start": **********.01326, "relative_start": 3.5831398963928223, "end": **********.01326, "relative_end": **********.01326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.014162, "relative_start": 3.5840420722961426, "end": **********.014162, "relative_end": **********.014162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69152f707ea1358f8997b77a28e38a6f", "start": **********.015345, "relative_start": 3.5852251052856445, "end": **********.015345, "relative_end": **********.015345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.01634, "relative_start": 3.5862200260162354, "end": **********.01634, "relative_end": **********.01634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b85eba35d3b7929c2988678b725baebf", "start": **********.018028, "relative_start": 3.5879080295562744, "end": **********.018028, "relative_end": **********.018028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.019299, "relative_start": 3.589179039001465, "end": **********.019299, "relative_end": **********.019299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f3d3b83c612f68036b4d79d53ae851e", "start": **********.020892, "relative_start": 3.5907719135284424, "end": **********.020892, "relative_end": **********.020892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.021865, "relative_start": 3.591744899749756, "end": **********.021865, "relative_end": **********.021865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.022601, "relative_start": 3.5924808979034424, "end": **********.022601, "relative_end": **********.022601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f143d1296cea16d82e2c87956e445593", "start": **********.024552, "relative_start": 3.5944321155548096, "end": **********.024552, "relative_end": **********.024552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.025435, "relative_start": 3.5953149795532227, "end": **********.025435, "relative_end": **********.025435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8cae4e5056b67c6778a54389a62ac7a0", "start": **********.026671, "relative_start": 3.596550941467285, "end": **********.026671, "relative_end": **********.026671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.027626, "relative_start": 3.597506046295166, "end": **********.027626, "relative_end": **********.027626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d5e509b6eb9084ec382ec05ccab41d1a", "start": **********.028955, "relative_start": 3.598834991455078, "end": **********.028955, "relative_end": **********.028955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.029831, "relative_start": 3.5997109413146973, "end": **********.029831, "relative_end": **********.029831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.03053, "relative_start": 3.600409984588623, "end": **********.03053, "relative_end": **********.03053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81a46563ed9378aa4d9a4fcb55e743e", "start": **********.031703, "relative_start": 3.6015830039978027, "end": **********.031703, "relative_end": **********.031703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.032633, "relative_start": 3.602513074874878, "end": **********.032633, "relative_end": **********.032633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.034098, "relative_start": 3.603977918624878, "end": **********.034098, "relative_end": **********.034098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.034782, "relative_start": 3.6046619415283203, "end": **********.034782, "relative_end": **********.034782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.035576, "relative_start": 3.6054561138153076, "end": **********.035576, "relative_end": **********.035576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.036434, "relative_start": 3.606313943862915, "end": **********.036434, "relative_end": **********.036434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.037162, "relative_start": 3.607042074203491, "end": **********.037162, "relative_end": **********.037162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.038072, "relative_start": 3.607952117919922, "end": **********.038072, "relative_end": **********.038072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da3c3de008e5793cbbdad005d78f49b1", "start": **********.03976, "relative_start": 3.609640121459961, "end": **********.03976, "relative_end": **********.03976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.041025, "relative_start": 3.6109049320220947, "end": **********.041025, "relative_end": **********.041025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.061817, "relative_start": 3.631696939468384, "end": **********.061817, "relative_end": **********.061817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.06308, "relative_start": 3.632960081100464, "end": **********.06308, "relative_end": **********.06308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.063534, "relative_start": 3.6334140300750732, "end": **********.063534, "relative_end": **********.063534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.064435, "relative_start": 3.634315013885498, "end": **********.064435, "relative_end": **********.064435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.license-invalid", "start": **********.065121, "relative_start": 3.6350009441375732, "end": **********.065121, "relative_end": **********.065121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.066035, "relative_start": 3.6359150409698486, "end": **********.066035, "relative_end": **********.066035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.partials.license-activation-modal", "start": **********.066922, "relative_start": 3.6368019580841064, "end": **********.066922, "relative_end": **********.066922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::license.form", "start": **********.067776, "relative_start": 3.6376559734344482, "end": **********.067776, "relative_end": **********.067776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.068701, "relative_start": 3.6385810375213623, "end": **********.068701, "relative_end": **********.068701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27ec08f706fece52ef1cc0ec5563cef9", "start": **********.069731, "relative_start": 3.639611005783081, "end": **********.069731, "relative_end": **********.069731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.070298, "relative_start": 3.6401779651641846, "end": **********.070298, "relative_end": **********.070298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.071017, "relative_start": 3.640897035598755, "end": **********.071017, "relative_end": **********.071017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.07151, "relative_start": 3.641390085220337, "end": **********.07151, "relative_end": **********.07151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.071949, "relative_start": 3.641829013824463, "end": **********.071949, "relative_end": **********.071949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.072274, "relative_start": 3.6421539783477783, "end": **********.072274, "relative_end": **********.072274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.072605, "relative_start": 3.6424849033355713, "end": **********.072605, "relative_end": **********.072605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.073295, "relative_start": 3.6431751251220703, "end": **********.073295, "relative_end": **********.073295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.073662, "relative_start": 3.6435420513153076, "end": **********.073662, "relative_end": **********.073662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.073921, "relative_start": 3.643800973892212, "end": **********.073921, "relative_end": **********.073921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.074236, "relative_start": 3.644115924835205, "end": **********.074236, "relative_end": **********.074236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "start": **********.074716, "relative_start": 3.6445960998535156, "end": **********.074716, "relative_end": **********.074716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.075218, "relative_start": 3.6450979709625244, "end": **********.075218, "relative_end": **********.075218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.075708, "relative_start": 3.645587921142578, "end": **********.075708, "relative_end": **********.075708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.076177, "relative_start": 3.646056890487671, "end": **********.076177, "relative_end": **********.076177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.076928, "relative_start": 3.646807909011841, "end": **********.076928, "relative_end": **********.076928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.077318, "relative_start": 3.647197961807251, "end": **********.077318, "relative_end": **********.077318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.077736, "relative_start": 3.647615909576416, "end": **********.077736, "relative_end": **********.077736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.078635, "relative_start": 3.648514986038208, "end": **********.078635, "relative_end": **********.078635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.079405, "relative_start": 3.649285078048706, "end": **********.079405, "relative_end": **********.079405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.080812, "relative_start": 3.6506919860839844, "end": **********.080812, "relative_end": **********.080812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.081463, "relative_start": 3.6513431072235107, "end": **********.081463, "relative_end": **********.081463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.082236, "relative_start": 3.652116060256958, "end": **********.082236, "relative_end": **********.082236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.082641, "relative_start": 3.6525208950042725, "end": **********.082641, "relative_end": **********.082641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.083073, "relative_start": 3.6529529094696045, "end": **********.083073, "relative_end": **********.083073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.083463, "relative_start": 3.6533429622650146, "end": **********.083463, "relative_end": **********.083463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53362b6227831afe8e4d7d3436ab607f", "start": **********.084879, "relative_start": 3.654758930206299, "end": **********.084879, "relative_end": **********.084879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e76aef074ac8ea84c711b8437720a22", "start": **********.086437, "relative_start": 3.6563169956207275, "end": **********.086437, "relative_end": **********.086437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::04edbddbda254d131a3439b11c880f12", "start": **********.087505, "relative_start": 3.6573851108551025, "end": **********.087505, "relative_end": **********.087505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.088009, "relative_start": 3.6578891277313232, "end": **********.088009, "relative_end": **********.088009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::custom-template", "start": **********.088881, "relative_start": 3.6587610244750977, "end": **********.088881, "relative_end": **********.088881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.089487, "relative_start": 3.659367084503174, "end": **********.089487, "relative_end": **********.089487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.090237, "relative_start": 3.6601169109344482, "end": **********.090237, "relative_end": **********.090237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::loading", "start": **********.090569, "relative_start": 3.6604490280151367, "end": **********.090569, "relative_end": **********.090569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.090996, "relative_start": 3.6608760356903076, "end": **********.090996, "relative_end": **********.090996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.091716, "relative_start": 3.6615960597991943, "end": **********.091716, "relative_end": **********.091716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.092094, "relative_start": 3.6619739532470703, "end": **********.092094, "relative_end": **********.092094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.09239, "relative_start": 3.6622700691223145, "end": **********.09239, "relative_end": **********.09239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.checkbox", "start": **********.092761, "relative_start": 3.6626410484313965, "end": **********.092761, "relative_end": **********.092761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.093713, "relative_start": 3.663593053817749, "end": **********.093713, "relative_end": **********.093713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.094535, "relative_start": 3.664415121078491, "end": **********.094535, "relative_end": **********.094535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.095131, "relative_start": 3.665010929107666, "end": **********.095131, "relative_end": **********.095131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.095798, "relative_start": 3.665678024291992, "end": **********.095798, "relative_end": **********.095798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.096226, "relative_start": 3.6661059856414795, "end": **********.096226, "relative_end": **********.096226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::debug-badge", "start": **********.332212, "relative_start": 3.9020919799804688, "end": **********.332212, "relative_end": **********.332212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.332946, "relative_start": 3.9028260707855225, "end": **********.332946, "relative_end": **********.332946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.333847, "relative_start": 3.9037270545959473, "end": **********.333847, "relative_end": **********.333847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.334536, "relative_start": 3.904416084289551, "end": **********.334536, "relative_end": **********.334536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::93ebde3601860db875cfe9a96164bda6", "start": **********.335394, "relative_start": 3.905273914337158, "end": **********.335394, "relative_end": **********.335394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.336002, "relative_start": 3.9058821201324463, "end": **********.336002, "relative_end": **********.336002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.336796, "relative_start": 3.9066760540008545, "end": **********.336796, "relative_end": **********.336796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.337383, "relative_start": 3.9072630405426025, "end": **********.337383, "relative_end": **********.337383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.33796, "relative_start": 3.9078400135040283, "end": **********.33796, "relative_end": **********.33796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16c15d36d71c18d83a7e9e7e1b68a92b", "start": **********.338618, "relative_start": 3.9084980487823486, "end": **********.338618, "relative_end": **********.338618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.339158, "relative_start": 3.9090380668640137, "end": **********.339158, "relative_end": **********.339158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::layouts.base", "start": **********.340012, "relative_start": 3.9098920822143555, "end": **********.340012, "relative_end": **********.340012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.341043, "relative_start": 3.9109230041503906, "end": **********.341043, "relative_end": **********.341043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.34393, "relative_start": 3.9138100147247314, "end": **********.34393, "relative_end": **********.34393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.346339, "relative_start": 3.9162189960479736, "end": **********.346339, "relative_end": **********.346339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.34863, "relative_start": 3.9185099601745605, "end": **********.34863, "relative_end": **********.34863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.349753, "relative_start": 3.919632911682129, "end": **********.349753, "relative_end": **********.349753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.35098, "relative_start": 3.9208600521087646, "end": **********.352091, "relative_end": **********.352091, "duration": 0.0011110305786132812, "duration_str": "1.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 77059576, "peak_usage_str": "73MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 507, "nb_templates": 507, "templates": [{"name": "1x core/base::forms.form", "param_count": null, "params": [], "start": **********.60778, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/form.blade.phpcore/base::forms.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form"}, {"name": "7x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.611555, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.fields.text"}, {"name": "20x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.612234, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::forms.partials.label"}, {"name": "25x 8def1252668913628243c4d363bee1ef::form.label", "param_count": null, "params": [], "start": **********.613103, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/label.blade.php8def1252668913628243c4d363bee1ef::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 25, "name_original": "8def1252668913628243c4d363bee1ef::form.label"}, {"name": "20x 8def1252668913628243c4d363bee1ef::form.field", "param_count": null, "params": [], "start": **********.613996, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/field.blade.php8def1252668913628243c4d363bee1ef::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 20, "name_original": "8def1252668913628243c4d363bee1ef::form.field"}, {"name": "20x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.614753, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::forms.partials.help-block"}, {"name": "20x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.61525, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::forms.partials.errors"}, {"name": "20x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.617115, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::forms.columns.column-span"}, {"name": "4x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": **********.61817, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.fields.textarea"}, {"name": "2x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": **********.634274, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.custom-select"}, {"name": "2x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": **********.635472, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.custom-select"}, {"name": "4x core/base::forms.fields.number", "param_count": null, "params": [], "start": **********.640181, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/number.blade.phpcore/base::forms.fields.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.fields.number"}, {"name": "2x 8def1252668913628243c4d363bee1ef::card.body.index", "param_count": null, "params": [], "start": **********.649663, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/body/index.blade.php8def1252668913628243c4d363bee1ef::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::card.body.index"}, {"name": "10x 8def1252668913628243c4d363bee1ef::card.index", "param_count": null, "params": [], "start": **********.650296, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/index.blade.php8def1252668913628243c4d363bee1ef::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::card.index"}, {"name": "3x core/base::elements.meta-box", "param_count": null, "params": [], "start": **********.653974, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/elements/meta-box.blade.phpcore/base::elements.meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::elements.meta-box"}, {"name": "1x core/base::forms.partials.form-actions", "param_count": null, "params": [], "start": **********.65436, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/form-actions.blade.phpcore/base::forms.partials.form-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-actions.blade.php&line=1", "ajax": false, "filename": "form-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.form-actions"}, {"name": "8x 8def1252668913628243c4d363bee1ef::card.title", "param_count": null, "params": [], "start": **********.655483, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/title.blade.php8def1252668913628243c4d363bee1ef::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 8, "name_original": "8def1252668913628243c4d363bee1ef::card.title"}, {"name": "9x 8def1252668913628243c4d363bee1ef::card.header.index", "param_count": null, "params": [], "start": **********.655937, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/header/index.blade.php8def1252668913628243c4d363bee1ef::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 9, "name_original": "8def1252668913628243c4d363bee1ef::card.header.index"}, {"name": "2x core/base::forms.partials.form-buttons", "param_count": null, "params": [], "start": **********.656381, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/form-buttons.blade.phpcore/base::forms.partials.form-buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-buttons.blade.php&line=1", "ajax": false, "filename": "form-buttons.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.form-buttons"}, {"name": "9x 8def1252668913628243c4d363bee1ef::button", "param_count": null, "params": [], "start": **********.65707, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/button.blade.php8def1252668913628243c4d363bee1ef::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 9, "name_original": "8def1252668913628243c4d363bee1ef::button"}, {"name": "2x __components::d212f3e1ce1fc54192f06b7fe29ef932", "param_count": null, "params": [], "start": **********.659891, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d212f3e1ce1fc54192f06b7fe29ef932.blade.php__components::d212f3e1ce1fc54192f06b7fe29ef932", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd212f3e1ce1fc54192f06b7fe29ef932.blade.php&line=1", "ajax": false, "filename": "d212f3e1ce1fc54192f06b7fe29ef932.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d212f3e1ce1fc54192f06b7fe29ef932"}, {"name": "2x __components::a11b38818686570b6e20907301bf062c", "param_count": null, "params": [], "start": **********.662927, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a11b38818686570b6e20907301bf062c.blade.php__components::a11b38818686570b6e20907301bf062c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa11b38818686570b6e20907301bf062c.blade.php&line=1", "ajax": false, "filename": "a11b38818686570b6e20907301bf062c.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::a11b38818686570b6e20907301bf062c"}, {"name": "1x core/base::layouts.partials.breadcrumbs", "param_count": null, "params": [], "start": **********.667141, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.phpcore/base::layouts.partials.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.breadcrumbs"}, {"name": "2x core/base::forms.fields.on-off", "param_count": null, "params": [], "start": **********.676959, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/on-off.blade.phpcore/base::forms.fields.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.on-off"}, {"name": "2x core/base::forms.partials.on-off", "param_count": null, "params": [], "start": **********.677586, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/on-off.blade.phpcore/base::forms.partials.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.on-off"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.toggle", "param_count": null, "params": [], "start": **********.678142, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/toggle.blade.php8def1252668913628243c4d363bee1ef::form.toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.toggle"}, {"name": "1x core/base::forms.fields.media-image", "param_count": null, "params": [], "start": **********.686265, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/fields/media-image.blade.phpcore/base::forms.fields.media-image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmedia-image.blade.php&line=1", "ajax": false, "filename": "media-image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.media-image"}, {"name": "1x core/base::forms.partials.image", "param_count": null, "params": [], "start": **********.686824, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/forms/partials/image.blade.phpcore/base::forms.partials.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.image"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.image", "param_count": null, "params": [], "start": **********.687327, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/image.blade.php8def1252668913628243c4d363bee1ef::form.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.image"}, {"name": "1x 8def1252668913628243c4d363bee1ef::image", "param_count": null, "params": [], "start": **********.68838, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/image.blade.php8def1252668913628243c4d363bee1ef::image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::image"}, {"name": "1x __components::14ad31fb3af14d3ba24d3c578af35e73", "param_count": null, "params": [], "start": **********.689957, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/14ad31fb3af14d3ba24d3c578af35e73.blade.php__components::14ad31fb3af14d3ba24d3c578af35e73", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F14ad31fb3af14d3ba24d3c578af35e73.blade.php&line=1", "ajax": false, "filename": "14ad31fb3af14d3ba24d3c578af35e73.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::14ad31fb3af14d3ba24d3c578af35e73"}, {"name": "1x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.733962, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.735262, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.736175, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.736608, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::3a4eb377d01a3c4bb09865b43ffbd313", "param_count": null, "params": [], "start": **********.737933, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3a4eb377d01a3c4bb09865b43ffbd313.blade.php__components::3a4eb377d01a3c4bb09865b43ffbd313", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3a4eb377d01a3c4bb09865b43ffbd313.blade.php&line=1", "ajax": false, "filename": "3a4eb377d01a3c4bb09865b43ffbd313.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::3a4eb377d01a3c4bb09865b43ffbd313"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.738625, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.750969, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.text-input", "param_count": null, "params": [], "start": **********.75163, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/text-input.blade.php8def1252668913628243c4d363bee1ef::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.text-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.error", "param_count": null, "params": [], "start": **********.753079, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/error.blade.php8def1252668913628243c4d363bee1ef::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.error"}, {"name": "7x 8def1252668913628243c4d363bee1ef::form-group", "param_count": null, "params": [], "start": **********.753513, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form-group.blade.php8def1252668913628243c4d363bee1ef::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 7, "name_original": "8def1252668913628243c4d363bee1ef::form-group"}, {"name": "1x __components::d2cfde89f704c31422aff2fae16ddb81", "param_count": null, "params": [], "start": **********.768016, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d2cfde89f704c31422aff2fae16ddb81.blade.php__components::d2cfde89f704c31422aff2fae16ddb81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd2cfde89f704c31422aff2fae16ddb81.blade.php&line=1", "ajax": false, "filename": "d2cfde89f704c31422aff2fae16ddb81.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d2cfde89f704c31422aff2fae16ddb81"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.768732, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::a5645d2a1f3c74251fc89224c575fed8", "param_count": null, "params": [], "start": **********.7699, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a5645d2a1f3c74251fc89224c575fed8.blade.php__components::a5645d2a1f3c74251fc89224c575fed8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa5645d2a1f3c74251fc89224c575fed8.blade.php&line=1", "ajax": false, "filename": "a5645d2a1f3c74251fc89224c575fed8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a5645d2a1f3c74251fc89224c575fed8"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.776073, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::639d159f54869d7a8362974885dec505", "param_count": null, "params": [], "start": **********.777366, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/639d159f54869d7a8362974885dec505.blade.php__components::639d159f54869d7a8362974885dec505", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F639d159f54869d7a8362974885dec505.blade.php&line=1", "ajax": false, "filename": "639d159f54869d7a8362974885dec505.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::639d159f54869d7a8362974885dec505"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.788006, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::cf41524c2db4e8ac4f30aba28550db55", "param_count": null, "params": [], "start": **********.789581, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/cf41524c2db4e8ac4f30aba28550db55.blade.php__components::cf41524c2db4e8ac4f30aba28550db55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fcf41524c2db4e8ac4f30aba28550db55.blade.php&line=1", "ajax": false, "filename": "cf41524c2db4e8ac4f30aba28550db55.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::cf41524c2db4e8ac4f30aba28550db55"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.actions", "param_count": null, "params": [], "start": **********.791248, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/actions.blade.php8def1252668913628243c4d363bee1ef::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.actions"}, {"name": "1x plugins/ecommerce::orders.notification", "param_count": null, "params": [], "start": **********.7645, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/notification.blade.phpplugins/ecommerce::orders.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.notification"}, {"name": "1x __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "param_count": null, "params": [], "start": **********.766211, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php__components::8394ebb1c2841e3a1166cd3fb0a6e03f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php&line=1", "ajax": false, "filename": "8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8394ebb1c2841e3a1166cd3fb0a6e03f"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.784077, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "4x 8def1252668913628243c4d363bee1ef::dropdown.item", "param_count": null, "params": [], "start": **********.795941, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/item.blade.php8def1252668913628243c4d363bee1ef::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.item"}, {"name": "2x __components::d059faaba602d6895d68258ab3c890a6", "param_count": null, "params": [], "start": **********.797469, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d059faaba602d6895d68258ab3c890a6.blade.php__components::d059faaba602d6895d68258ab3c890a6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd059faaba602d6895d68258ab3c890a6.blade.php&line=1", "ajax": false, "filename": "d059faaba602d6895d68258ab3c890a6.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d059faaba602d6895d68258ab3c890a6"}, {"name": "2x __components::a3cb4601eb64a80dc01a3c268590a3c8", "param_count": null, "params": [], "start": **********.799431, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a3cb4601eb64a80dc01a3c268590a3c8.blade.php__components::a3cb4601eb64a80dc01a3c268590a3c8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa3cb4601eb64a80dc01a3c268590a3c8.blade.php&line=1", "ajax": false, "filename": "a3cb4601eb64a80dc01a3c268590a3c8.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::a3cb4601eb64a80dc01a3c268590a3c8"}, {"name": "2x 8def1252668913628243c4d363bee1ef::dropdown.index", "param_count": null, "params": [], "start": **********.800042, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/index.blade.php8def1252668913628243c4d363bee1ef::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.index"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.800827, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.813568, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.813989, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "22x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.832813, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 22, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "79x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.833855, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 79, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::e3b17f7ce9738894b58a8b70b9624457", "param_count": null, "params": [], "start": **********.83532, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e3b17f7ce9738894b58a8b70b9624457.blade.php__components::e3b17f7ce9738894b58a8b70b9624457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe3b17f7ce9738894b58a8b70b9624457.blade.php&line=1", "ajax": false, "filename": "e3b17f7ce9738894b58a8b70b9624457.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3b17f7ce9738894b58a8b70b9624457"}, {"name": "1x __components::0c6e6838aa476b78aace81114936689c", "param_count": null, "params": [], "start": **********.838739, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0c6e6838aa476b78aace81114936689c.blade.php__components::0c6e6838aa476b78aace81114936689c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0c6e6838aa476b78aace81114936689c.blade.php&line=1", "ajax": false, "filename": "0c6e6838aa476b78aace81114936689c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0c6e6838aa476b78aace81114936689c"}, {"name": "11x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.839827, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "11x 8def1252668913628243c4d363bee1ef::navbar.badge-count", "param_count": null, "params": [], "start": **********.840361, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/navbar/badge-count.blade.php8def1252668913628243c4d363bee1ef::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::navbar.badge-count"}, {"name": "1x __components::37fae22c8e215ea2e54e69a5e3a007cc", "param_count": null, "params": [], "start": **********.84255, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/37fae22c8e215ea2e54e69a5e3a007cc.blade.php__components::37fae22c8e215ea2e54e69a5e3a007cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F37fae22c8e215ea2e54e69a5e3a007cc.blade.php&line=1", "ajax": false, "filename": "37fae22c8e215ea2e54e69a5e3a007cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::37fae22c8e215ea2e54e69a5e3a007cc"}, {"name": "1x __components::8916176d99d4ae2024cd36e11e35b821", "param_count": null, "params": [], "start": **********.844617, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8916176d99d4ae2024cd36e11e35b821.blade.php__components::8916176d99d4ae2024cd36e11e35b821", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8916176d99d4ae2024cd36e11e35b821.blade.php&line=1", "ajax": false, "filename": "8916176d99d4ae2024cd36e11e35b821.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8916176d99d4ae2024cd36e11e35b821"}, {"name": "1x __components::bd27433a6607127acdaf6dc541ab2435", "param_count": null, "params": [], "start": **********.847195, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/bd27433a6607127acdaf6dc541ab2435.blade.php__components::bd27433a6607127acdaf6dc541ab2435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbd27433a6607127acdaf6dc541ab2435.blade.php&line=1", "ajax": false, "filename": "bd27433a6607127acdaf6dc541ab2435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd27433a6607127acdaf6dc541ab2435"}, {"name": "1x __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "param_count": null, "params": [], "start": **********.849261, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php__components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php&line=1", "ajax": false, "filename": "5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5f4f9ebbae249bdc8b0d599a1ac6ad06"}, {"name": "1x __components::080b92e00b37bcc97c1cd249894494a2", "param_count": null, "params": [], "start": **********.853707, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/080b92e00b37bcc97c1cd249894494a2.blade.php__components::080b92e00b37bcc97c1cd249894494a2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F080b92e00b37bcc97c1cd249894494a2.blade.php&line=1", "ajax": false, "filename": "080b92e00b37bcc97c1cd249894494a2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::080b92e00b37bcc97c1cd249894494a2"}, {"name": "1x __components::6a26943e77184871de1629f41c534094", "param_count": null, "params": [], "start": **********.856321, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6a26943e77184871de1629f41c534094.blade.php__components::6a26943e77184871de1629f41c534094", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6a26943e77184871de1629f41c534094.blade.php&line=1", "ajax": false, "filename": "6a26943e77184871de1629f41c534094.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6a26943e77184871de1629f41c534094"}, {"name": "1x __components::8f29f8012139c7a3eb6593c906e1db38", "param_count": null, "params": [], "start": **********.858785, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8f29f8012139c7a3eb6593c906e1db38.blade.php__components::8f29f8012139c7a3eb6593c906e1db38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8f29f8012139c7a3eb6593c906e1db38.blade.php&line=1", "ajax": false, "filename": "8f29f8012139c7a3eb6593c906e1db38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8f29f8012139c7a3eb6593c906e1db38"}, {"name": "1x __components::4553f5b37130b2effba490dbdf5419d2", "param_count": null, "params": [], "start": **********.861941, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/4553f5b37130b2effba490dbdf5419d2.blade.php__components::4553f5b37130b2effba490dbdf5419d2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F4553f5b37130b2effba490dbdf5419d2.blade.php&line=1", "ajax": false, "filename": "4553f5b37130b2effba490dbdf5419d2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4553f5b37130b2effba490dbdf5419d2"}, {"name": "1x __components::59c947fc9b2121a5885d4f4e7b1242d8", "param_count": null, "params": [], "start": **********.864042, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/59c947fc9b2121a5885d4f4e7b1242d8.blade.php__components::59c947fc9b2121a5885d4f4e7b1242d8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F59c947fc9b2121a5885d4f4e7b1242d8.blade.php&line=1", "ajax": false, "filename": "59c947fc9b2121a5885d4f4e7b1242d8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::59c947fc9b2121a5885d4f4e7b1242d8"}, {"name": "1x __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "param_count": null, "params": [], "start": **********.866193, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php__components::1e9698c460b468bfcaf0f7dbbebf9bf0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php&line=1", "ajax": false, "filename": "1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1e9698c460b468bfcaf0f7dbbebf9bf0"}, {"name": "2x __components::0d4eb4544a5328bea40b7b01743b8f82", "param_count": null, "params": [], "start": **********.868415, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d4eb4544a5328bea40b7b01743b8f82.blade.php__components::0d4eb4544a5328bea40b7b01743b8f82", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d4eb4544a5328bea40b7b01743b8f82.blade.php&line=1", "ajax": false, "filename": "0d4eb4544a5328bea40b7b01743b8f82.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0d4eb4544a5328bea40b7b01743b8f82"}, {"name": "2x __components::5270fef4db64e6c2fedf42ea8ac88f25", "param_count": null, "params": [], "start": **********.870884, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5270fef4db64e6c2fedf42ea8ac88f25.blade.php__components::5270fef4db64e6c2fedf42ea8ac88f25", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5270fef4db64e6c2fedf42ea8ac88f25.blade.php&line=1", "ajax": false, "filename": "5270fef4db64e6c2fedf42ea8ac88f25.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5270fef4db64e6c2fedf42ea8ac88f25"}, {"name": "1x __components::84f17fac377525e2e49f32058361220b", "param_count": null, "params": [], "start": **********.873386, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/84f17fac377525e2e49f32058361220b.blade.php__components::84f17fac377525e2e49f32058361220b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F84f17fac377525e2e49f32058361220b.blade.php&line=1", "ajax": false, "filename": "84f17fac377525e2e49f32058361220b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::84f17fac377525e2e49f32058361220b"}, {"name": "1x __components::1c8617dee734f51544a3883923ddca6f", "param_count": null, "params": [], "start": **********.877504, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c8617dee734f51544a3883923ddca6f.blade.php__components::1c8617dee734f51544a3883923ddca6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c8617dee734f51544a3883923ddca6f.blade.php&line=1", "ajax": false, "filename": "1c8617dee734f51544a3883923ddca6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c8617dee734f51544a3883923ddca6f"}, {"name": "1x __components::3d3bfe5e8598abeb74083f6c26233cb5", "param_count": null, "params": [], "start": **********.87946, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3d3bfe5e8598abeb74083f6c26233cb5.blade.php__components::3d3bfe5e8598abeb74083f6c26233cb5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3d3bfe5e8598abeb74083f6c26233cb5.blade.php&line=1", "ajax": false, "filename": "3d3bfe5e8598abeb74083f6c26233cb5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3d3bfe5e8598abeb74083f6c26233cb5"}, {"name": "1x __components::35fe997a7b87ef55d749630606a50a1b", "param_count": null, "params": [], "start": **********.881853, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/35fe997a7b87ef55d749630606a50a1b.blade.php__components::35fe997a7b87ef55d749630606a50a1b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F35fe997a7b87ef55d749630606a50a1b.blade.php&line=1", "ajax": false, "filename": "35fe997a7b87ef55d749630606a50a1b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::35fe997a7b87ef55d749630606a50a1b"}, {"name": "1x __components::a4f1583597dec7e67a8ae044f0915dbe", "param_count": null, "params": [], "start": **********.887486, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a4f1583597dec7e67a8ae044f0915dbe.blade.php__components::a4f1583597dec7e67a8ae044f0915dbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa4f1583597dec7e67a8ae044f0915dbe.blade.php&line=1", "ajax": false, "filename": "a4f1583597dec7e67a8ae044f0915dbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a4f1583597dec7e67a8ae044f0915dbe"}, {"name": "1x __components::c0cbd16b0cc2226ec5536610974ba3c3", "param_count": null, "params": [], "start": **********.890246, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c0cbd16b0cc2226ec5536610974ba3c3.blade.php__components::c0cbd16b0cc2226ec5536610974ba3c3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc0cbd16b0cc2226ec5536610974ba3c3.blade.php&line=1", "ajax": false, "filename": "c0cbd16b0cc2226ec5536610974ba3c3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c0cbd16b0cc2226ec5536610974ba3c3"}, {"name": "2x __components::70b8df706e60982a72f15e9e2d486203", "param_count": null, "params": [], "start": **********.892683, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/70b8df706e60982a72f15e9e2d486203.blade.php__components::70b8df706e60982a72f15e9e2d486203", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F70b8df706e60982a72f15e9e2d486203.blade.php&line=1", "ajax": false, "filename": "70b8df706e60982a72f15e9e2d486203.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::70b8df706e60982a72f15e9e2d486203"}, {"name": "1x __components::dc78b90963e9d9963376e0e829411cea", "param_count": null, "params": [], "start": **********.895816, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/dc78b90963e9d9963376e0e829411cea.blade.php__components::dc78b90963e9d9963376e0e829411cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fdc78b90963e9d9963376e0e829411cea.blade.php&line=1", "ajax": false, "filename": "dc78b90963e9d9963376e0e829411cea.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc78b90963e9d9963376e0e829411cea"}, {"name": "4x __components::7a6e3d0dfcd673b5659893aa4dd54e33", "param_count": null, "params": [], "start": **********.897771, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7a6e3d0dfcd673b5659893aa4dd54e33.blade.php__components::7a6e3d0dfcd673b5659893aa4dd54e33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7a6e3d0dfcd673b5659893aa4dd54e33.blade.php&line=1", "ajax": false, "filename": "7a6e3d0dfcd673b5659893aa4dd54e33.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::7a6e3d0dfcd673b5659893aa4dd54e33"}, {"name": "1x __components::481a833ebeb573258c941c925aa45f7b", "param_count": null, "params": [], "start": **********.899933, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/481a833ebeb573258c941c925aa45f7b.blade.php__components::481a833ebeb573258c941c925aa45f7b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F481a833ebeb573258c941c925aa45f7b.blade.php&line=1", "ajax": false, "filename": "481a833ebeb573258c941c925aa45f7b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::481a833ebeb573258c941c925aa45f7b"}, {"name": "1x __components::b42bb0aa5fceca31ad61711414a614f0", "param_count": null, "params": [], "start": **********.902322, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b42bb0aa5fceca31ad61711414a614f0.blade.php__components::b42bb0aa5fceca31ad61711414a614f0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb42bb0aa5fceca31ad61711414a614f0.blade.php&line=1", "ajax": false, "filename": "b42bb0aa5fceca31ad61711414a614f0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b42bb0aa5fceca31ad61711414a614f0"}, {"name": "3x __components::1c969038219bd5c599f1ca2d81401cea", "param_count": null, "params": [], "start": **********.905311, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c969038219bd5c599f1ca2d81401cea.blade.php__components::1c969038219bd5c599f1ca2d81401cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c969038219bd5c599f1ca2d81401cea.blade.php&line=1", "ajax": false, "filename": "1c969038219bd5c599f1ca2d81401cea.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1c969038219bd5c599f1ca2d81401cea"}, {"name": "1x __components::471e83668278198d730a7a3f4a475d45", "param_count": null, "params": [], "start": **********.908123, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/471e83668278198d730a7a3f4a475d45.blade.php__components::471e83668278198d730a7a3f4a475d45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F471e83668278198d730a7a3f4a475d45.blade.php&line=1", "ajax": false, "filename": "471e83668278198d730a7a3f4a475d45.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::471e83668278198d730a7a3f4a475d45"}, {"name": "1x __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "param_count": null, "params": [], "start": **********.911964, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php__components::d7b40194b2b4ba91a975fc9aafe2d3e8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7b40194b2b4ba91a975fc9aafe2d3e8.blade.php&line=1", "ajax": false, "filename": "d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b40194b2b4ba91a975fc9aafe2d3e8"}, {"name": "1x __components::acb69140835a74210411469faeab3034", "param_count": null, "params": [], "start": **********.91662, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/acb69140835a74210411469faeab3034.blade.php__components::acb69140835a74210411469faeab3034", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Facb69140835a74210411469faeab3034.blade.php&line=1", "ajax": false, "filename": "acb69140835a74210411469faeab3034.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acb69140835a74210411469faeab3034"}, {"name": "1x __components::19cd49cd69455e40dc223df6b4eaf954", "param_count": null, "params": [], "start": **********.921116, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/19cd49cd69455e40dc223df6b4eaf954.blade.php__components::19cd49cd69455e40dc223df6b4eaf954", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F19cd49cd69455e40dc223df6b4eaf954.blade.php&line=1", "ajax": false, "filename": "19cd49cd69455e40dc223df6b4eaf954.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19cd49cd69455e40dc223df6b4eaf954"}, {"name": "1x __components::2979b72aeeca0047ecdecc3ad66e7e16", "param_count": null, "params": [], "start": **********.924015, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/2979b72aeeca0047ecdecc3ad66e7e16.blade.php__components::2979b72aeeca0047ecdecc3ad66e7e16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F2979b72aeeca0047ecdecc3ad66e7e16.blade.php&line=1", "ajax": false, "filename": "2979b72aeeca0047ecdecc3ad66e7e16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2979b72aeeca0047ecdecc3ad66e7e16"}, {"name": "1x __components::3c40febd70fcdc245d99ae7cd02cface", "param_count": null, "params": [], "start": **********.926765, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3c40febd70fcdc245d99ae7cd02cface.blade.php__components::3c40febd70fcdc245d99ae7cd02cface", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3c40febd70fcdc245d99ae7cd02cface.blade.php&line=1", "ajax": false, "filename": "3c40febd70fcdc245d99ae7cd02cface.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3c40febd70fcdc245d99ae7cd02cface"}, {"name": "2x __components::90ccac5c8bbb25741ef262bfd81c7551", "param_count": null, "params": [], "start": **********.928799, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/90ccac5c8bbb25741ef262bfd81c7551.blade.php__components::90ccac5c8bbb25741ef262bfd81c7551", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F90ccac5c8bbb25741ef262bfd81c7551.blade.php&line=1", "ajax": false, "filename": "90ccac5c8bbb25741ef262bfd81c7551.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::90ccac5c8bbb25741ef262bfd81c7551"}, {"name": "1x __components::d7ced212b797c29086a7922a858f3070", "param_count": null, "params": [], "start": **********.936654, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7ced212b797c29086a7922a858f3070.blade.php__components::d7ced212b797c29086a7922a858f3070", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7ced212b797c29086a7922a858f3070.blade.php&line=1", "ajax": false, "filename": "d7ced212b797c29086a7922a858f3070.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7ced212b797c29086a7922a858f3070"}, {"name": "2x __components::9d41a7757b46012fb4a0d6634d04a1e0", "param_count": null, "params": [], "start": **********.939828, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d41a7757b46012fb4a0d6634d04a1e0.blade.php__components::9d41a7757b46012fb4a0d6634d04a1e0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d41a7757b46012fb4a0d6634d04a1e0.blade.php&line=1", "ajax": false, "filename": "9d41a7757b46012fb4a0d6634d04a1e0.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9d41a7757b46012fb4a0d6634d04a1e0"}, {"name": "3x __components::86b7e33bd2198279086ebb1f21c0e2cc", "param_count": null, "params": [], "start": **********.944201, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/86b7e33bd2198279086ebb1f21c0e2cc.blade.php__components::86b7e33bd2198279086ebb1f21c0e2cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F86b7e33bd2198279086ebb1f21c0e2cc.blade.php&line=1", "ajax": false, "filename": "86b7e33bd2198279086ebb1f21c0e2cc.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::86b7e33bd2198279086ebb1f21c0e2cc"}, {"name": "1x __components::8c52d9b1ef0685ec10fdc3e877751e02", "param_count": null, "params": [], "start": **********.949885, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8c52d9b1ef0685ec10fdc3e877751e02.blade.php__components::8c52d9b1ef0685ec10fdc3e877751e02", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8c52d9b1ef0685ec10fdc3e877751e02.blade.php&line=1", "ajax": false, "filename": "8c52d9b1ef0685ec10fdc3e877751e02.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8c52d9b1ef0685ec10fdc3e877751e02"}, {"name": "1x __components::42668be6e8e5266862c6994eaa88bb55", "param_count": null, "params": [], "start": **********.954275, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/42668be6e8e5266862c6994eaa88bb55.blade.php__components::42668be6e8e5266862c6994eaa88bb55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F42668be6e8e5266862c6994eaa88bb55.blade.php&line=1", "ajax": false, "filename": "42668be6e8e5266862c6994eaa88bb55.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42668be6e8e5266862c6994eaa88bb55"}, {"name": "1x __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "param_count": null, "params": [], "start": **********.95812, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php__components::311d8c591d63d3cbd12dceb2fb1ac1c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php&line=1", "ajax": false, "filename": "311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::311d8c591d63d3cbd12dceb2fb1ac1c1"}, {"name": "1x __components::613233f0072612a02c74dd1699c0b74c", "param_count": null, "params": [], "start": **********.965582, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/613233f0072612a02c74dd1699c0b74c.blade.php__components::613233f0072612a02c74dd1699c0b74c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F613233f0072612a02c74dd1699c0b74c.blade.php&line=1", "ajax": false, "filename": "613233f0072612a02c74dd1699c0b74c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::613233f0072612a02c74dd1699c0b74c"}, {"name": "1x __components::16225ede2ef5cc17292fd2eb9026fc80", "param_count": null, "params": [], "start": **********.967805, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16225ede2ef5cc17292fd2eb9026fc80.blade.php__components::16225ede2ef5cc17292fd2eb9026fc80", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16225ede2ef5cc17292fd2eb9026fc80.blade.php&line=1", "ajax": false, "filename": "16225ede2ef5cc17292fd2eb9026fc80.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16225ede2ef5cc17292fd2eb9026fc80"}, {"name": "1x __components::46010cb1cb88bb5ead5d94603a4a3d16", "param_count": null, "params": [], "start": **********.970581, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/46010cb1cb88bb5ead5d94603a4a3d16.blade.php__components::46010cb1cb88bb5ead5d94603a4a3d16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F46010cb1cb88bb5ead5d94603a4a3d16.blade.php&line=1", "ajax": false, "filename": "46010cb1cb88bb5ead5d94603a4a3d16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46010cb1cb88bb5ead5d94603a4a3d16"}, {"name": "1x __components::34e3d89351b1208b7f313125eec52879", "param_count": null, "params": [], "start": **********.973957, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/34e3d89351b1208b7f313125eec52879.blade.php__components::34e3d89351b1208b7f313125eec52879", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F34e3d89351b1208b7f313125eec52879.blade.php&line=1", "ajax": false, "filename": "34e3d89351b1208b7f313125eec52879.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::34e3d89351b1208b7f313125eec52879"}, {"name": "1x __components::67034900569133b2c83b32da3dd4f5e5", "param_count": null, "params": [], "start": **********.975944, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/67034900569133b2c83b32da3dd4f5e5.blade.php__components::67034900569133b2c83b32da3dd4f5e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F67034900569133b2c83b32da3dd4f5e5.blade.php&line=1", "ajax": false, "filename": "67034900569133b2c83b32da3dd4f5e5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::67034900569133b2c83b32da3dd4f5e5"}, {"name": "1x __components::c33cfbd01dd1d76718fcd68287a40728", "param_count": null, "params": [], "start": **********.982748, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c33cfbd01dd1d76718fcd68287a40728.blade.php__components::c33cfbd01dd1d76718fcd68287a40728", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc33cfbd01dd1d76718fcd68287a40728.blade.php&line=1", "ajax": false, "filename": "c33cfbd01dd1d76718fcd68287a40728.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c33cfbd01dd1d76718fcd68287a40728"}, {"name": "1x __components::0d623715926c24f9fbc8a4b72c106d5d", "param_count": null, "params": [], "start": **********.986378, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d623715926c24f9fbc8a4b72c106d5d.blade.php__components::0d623715926c24f9fbc8a4b72c106d5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d623715926c24f9fbc8a4b72c106d5d.blade.php&line=1", "ajax": false, "filename": "0d623715926c24f9fbc8a4b72c106d5d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d623715926c24f9fbc8a4b72c106d5d"}, {"name": "1x __components::6c50fc55276d93f8ed03f5c85273b6cc", "param_count": null, "params": [], "start": **********.989144, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6c50fc55276d93f8ed03f5c85273b6cc.blade.php__components::6c50fc55276d93f8ed03f5c85273b6cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6c50fc55276d93f8ed03f5c85273b6cc.blade.php&line=1", "ajax": false, "filename": "6c50fc55276d93f8ed03f5c85273b6cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6c50fc55276d93f8ed03f5c85273b6cc"}, {"name": "1x __components::084d2b43c9ab4b881d9b34a15580aa2d", "param_count": null, "params": [], "start": **********.99166, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/084d2b43c9ab4b881d9b34a15580aa2d.blade.php__components::084d2b43c9ab4b881d9b34a15580aa2d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F084d2b43c9ab4b881d9b34a15580aa2d.blade.php&line=1", "ajax": false, "filename": "084d2b43c9ab4b881d9b34a15580aa2d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::084d2b43c9ab4b881d9b34a15580aa2d"}, {"name": "2x __components::b33d20952e90e5c4a596ff58ad010448", "param_count": null, "params": [], "start": **********.994198, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b33d20952e90e5c4a596ff58ad010448.blade.php__components::b33d20952e90e5c4a596ff58ad010448", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb33d20952e90e5c4a596ff58ad010448.blade.php&line=1", "ajax": false, "filename": "b33d20952e90e5c4a596ff58ad010448.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b33d20952e90e5c4a596ff58ad010448"}, {"name": "1x __components::7db8cad89359963c1e9aa8fcc6c89817", "param_count": null, "params": [], "start": **********.996746, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7db8cad89359963c1e9aa8fcc6c89817.blade.php__components::7db8cad89359963c1e9aa8fcc6c89817", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7db8cad89359963c1e9aa8fcc6c89817.blade.php&line=1", "ajax": false, "filename": "7db8cad89359963c1e9aa8fcc6c89817.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7db8cad89359963c1e9aa8fcc6c89817"}, {"name": "1x __components::325be2a8c3ca3843efa76c03adaee1dc", "param_count": null, "params": [], "start": **********.002454, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/325be2a8c3ca3843efa76c03adaee1dc.blade.php__components::325be2a8c3ca3843efa76c03adaee1dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F325be2a8c3ca3843efa76c03adaee1dc.blade.php&line=1", "ajax": false, "filename": "325be2a8c3ca3843efa76c03adaee1dc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::325be2a8c3ca3843efa76c03adaee1dc"}, {"name": "1x __components::e12a669ffa0346a27198bed32e63b7ba", "param_count": null, "params": [], "start": **********.004648, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e12a669ffa0346a27198bed32e63b7ba.blade.php__components::e12a669ffa0346a27198bed32e63b7ba", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe12a669ffa0346a27198bed32e63b7ba.blade.php&line=1", "ajax": false, "filename": "e12a669ffa0346a27198bed32e63b7ba.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e12a669ffa0346a27198bed32e63b7ba"}, {"name": "1x __components::9d0b20db301db9a47503a93a879bb206", "param_count": null, "params": [], "start": **********.00677, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d0b20db301db9a47503a93a879bb206.blade.php__components::9d0b20db301db9a47503a93a879bb206", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d0b20db301db9a47503a93a879bb206.blade.php&line=1", "ajax": false, "filename": "9d0b20db301db9a47503a93a879bb206.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9d0b20db301db9a47503a93a879bb206"}, {"name": "1x __components::b985af7bcdacbeac70eaf3979ad19f5a", "param_count": null, "params": [], "start": **********.00889, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b985af7bcdacbeac70eaf3979ad19f5a.blade.php__components::b985af7bcdacbeac70eaf3979ad19f5a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb985af7bcdacbeac70eaf3979ad19f5a.blade.php&line=1", "ajax": false, "filename": "b985af7bcdacbeac70eaf3979ad19f5a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b985af7bcdacbeac70eaf3979ad19f5a"}, {"name": "1x __components::a0bb1d43b71cff86abe626fd376492e9", "param_count": null, "params": [], "start": **********.010961, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a0bb1d43b71cff86abe626fd376492e9.blade.php__components::a0bb1d43b71cff86abe626fd376492e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa0bb1d43b71cff86abe626fd376492e9.blade.php&line=1", "ajax": false, "filename": "a0bb1d43b71cff86abe626fd376492e9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a0bb1d43b71cff86abe626fd376492e9"}, {"name": "1x __components::e25f2b305e6de46c04f91fa1ce50f68f", "param_count": null, "params": [], "start": **********.013241, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e25f2b305e6de46c04f91fa1ce50f68f.blade.php__components::e25f2b305e6de46c04f91fa1ce50f68f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe25f2b305e6de46c04f91fa1ce50f68f.blade.php&line=1", "ajax": false, "filename": "e25f2b305e6de46c04f91fa1ce50f68f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e25f2b305e6de46c04f91fa1ce50f68f"}, {"name": "1x __components::69152f707ea1358f8997b77a28e38a6f", "param_count": null, "params": [], "start": **********.015326, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/69152f707ea1358f8997b77a28e38a6f.blade.php__components::69152f707ea1358f8997b77a28e38a6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F69152f707ea1358f8997b77a28e38a6f.blade.php&line=1", "ajax": false, "filename": "69152f707ea1358f8997b77a28e38a6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69152f707ea1358f8997b77a28e38a6f"}, {"name": "1x __components::b85eba35d3b7929c2988678b725baebf", "param_count": null, "params": [], "start": **********.018, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b85eba35d3b7929c2988678b725baebf.blade.php__components::b85eba35d3b7929c2988678b725baebf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb85eba35d3b7929c2988678b725baebf.blade.php&line=1", "ajax": false, "filename": "b85eba35d3b7929c2988678b725baebf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b85eba35d3b7929c2988678b725baebf"}, {"name": "1x __components::1f3d3b83c612f68036b4d79d53ae851e", "param_count": null, "params": [], "start": **********.020871, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1f3d3b83c612f68036b4d79d53ae851e.blade.php__components::1f3d3b83c612f68036b4d79d53ae851e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1f3d3b83c612f68036b4d79d53ae851e.blade.php&line=1", "ajax": false, "filename": "1f3d3b83c612f68036b4d79d53ae851e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1f3d3b83c612f68036b4d79d53ae851e"}, {"name": "1x __components::f143d1296cea16d82e2c87956e445593", "param_count": null, "params": [], "start": **********.024532, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/f143d1296cea16d82e2c87956e445593.blade.php__components::f143d1296cea16d82e2c87956e445593", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Ff143d1296cea16d82e2c87956e445593.blade.php&line=1", "ajax": false, "filename": "f143d1296cea16d82e2c87956e445593.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f143d1296cea16d82e2c87956e445593"}, {"name": "1x __components::8cae4e5056b67c6778a54389a62ac7a0", "param_count": null, "params": [], "start": **********.026651, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8cae4e5056b67c6778a54389a62ac7a0.blade.php__components::8cae4e5056b67c6778a54389a62ac7a0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8cae4e5056b67c6778a54389a62ac7a0.blade.php&line=1", "ajax": false, "filename": "8cae4e5056b67c6778a54389a62ac7a0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8cae4e5056b67c6778a54389a62ac7a0"}, {"name": "1x __components::d5e509b6eb9084ec382ec05ccab41d1a", "param_count": null, "params": [], "start": **********.028936, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d5e509b6eb9084ec382ec05ccab41d1a.blade.php__components::d5e509b6eb9084ec382ec05ccab41d1a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd5e509b6eb9084ec382ec05ccab41d1a.blade.php&line=1", "ajax": false, "filename": "d5e509b6eb9084ec382ec05ccab41d1a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d5e509b6eb9084ec382ec05ccab41d1a"}, {"name": "1x __components::e81a46563ed9378aa4d9a4fcb55e743e", "param_count": null, "params": [], "start": **********.031683, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e81a46563ed9378aa4d9a4fcb55e743e.blade.php__components::e81a46563ed9378aa4d9a4fcb55e743e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe81a46563ed9378aa4d9a4fcb55e743e.blade.php&line=1", "ajax": false, "filename": "e81a46563ed9378aa4d9a4fcb55e743e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81a46563ed9378aa4d9a4fcb55e743e"}, {"name": "1x __components::da3c3de008e5793cbbdad005d78f49b1", "param_count": null, "params": [], "start": **********.039739, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/da3c3de008e5793cbbdad005d78f49b1.blade.php__components::da3c3de008e5793cbbdad005d78f49b1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fda3c3de008e5793cbbdad005d78f49b1.blade.php&line=1", "ajax": false, "filename": "da3c3de008e5793cbbdad005d78f49b1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::da3c3de008e5793cbbdad005d78f49b1"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.041003, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.061796, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.063061, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.063515, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.064415, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::system.license-invalid", "param_count": null, "params": [], "start": **********.065101, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/license-invalid.blade.phpcore/base::system.license-invalid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Flicense-invalid.blade.php&line=1", "ajax": false, "filename": "license-invalid.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.license-invalid"}, {"name": "2x 8def1252668913628243c4d363bee1ef::alert", "param_count": null, "params": [], "start": **********.066016, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/alert.blade.php8def1252668913628243c4d363bee1ef::alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::alert"}, {"name": "1x core/base::system.partials.license-activation-modal", "param_count": null, "params": [], "start": **********.066881, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/partials/license-activation-modal.blade.phpcore/base::system.partials.license-activation-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Fpartials%2Flicense-activation-modal.blade.php&line=1", "ajax": false, "filename": "license-activation-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.partials.license-activation-modal"}, {"name": "1x 8def1252668913628243c4d363bee1ef::license.form", "param_count": null, "params": [], "start": **********.067755, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/license/form.blade.php8def1252668913628243c4d363bee1ef::license.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flicense%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::license.form"}, {"name": "1x __components::27ec08f706fece52ef1cc0ec5563cef9", "param_count": null, "params": [], "start": **********.069712, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/27ec08f706fece52ef1cc0ec5563cef9.blade.php__components::27ec08f706fece52ef1cc0ec5563cef9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F27ec08f706fece52ef1cc0ec5563cef9.blade.php&line=1", "ajax": false, "filename": "27ec08f706fece52ef1cc0ec5563cef9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27ec08f706fece52ef1cc0ec5563cef9"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.helper-text", "param_count": null, "params": [], "start": **********.07149, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/helper-text.blade.php8def1252668913628243c4d363bee1ef::form.helper-text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fhelper-text.blade.php&line=1", "ajax": false, "filename": "helper-text.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.helper-text"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "param_count": null, "params": [], "start": **********.074696, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": **********.075191, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "1x ********************************::form-group", "param_count": null, "params": [], "start": **********.076909, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/setting/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form-group"}, {"name": "5x 8def1252668913628243c4d363bee1ef::modal", "param_count": null, "params": [], "start": **********.077718, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal.blade.php8def1252668913628243c4d363bee1ef::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::modal"}, {"name": "5x 8def1252668913628243c4d363bee1ef::modal.close-button", "param_count": null, "params": [], "start": **********.078616, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/close-button.blade.php8def1252668913628243c4d363bee1ef::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::modal.close-button"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.079386, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::6f3b10173cc6f5c541f27080145e1a40", "param_count": null, "params": [], "start": **********.080793, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6f3b10173cc6f5c541f27080145e1a40.blade.php__components::6f3b10173cc6f5c541f27080145e1a40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6f3b10173cc6f5c541f27080145e1a40.blade.php&line=1", "ajax": false, "filename": "6f3b10173cc6f5c541f27080145e1a40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6f3b10173cc6f5c541f27080145e1a40"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.index", "param_count": null, "params": [], "start": **********.08344, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/index.blade.php8def1252668913628243c4d363bee1ef::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.index"}, {"name": "1x __components::53362b6227831afe8e4d7d3436ab607f", "param_count": null, "params": [], "start": **********.084853, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/53362b6227831afe8e4d7d3436ab607f.blade.php__components::53362b6227831afe8e4d7d3436ab607f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F53362b6227831afe8e4d7d3436ab607f.blade.php&line=1", "ajax": false, "filename": "53362b6227831afe8e4d7d3436ab607f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53362b6227831afe8e4d7d3436ab607f"}, {"name": "1x __components::9e76aef074ac8ea84c711b8437720a22", "param_count": null, "params": [], "start": **********.086412, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9e76aef074ac8ea84c711b8437720a22.blade.php__components::9e76aef074ac8ea84c711b8437720a22", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9e76aef074ac8ea84c711b8437720a22.blade.php&line=1", "ajax": false, "filename": "9e76aef074ac8ea84c711b8437720a22.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9e76aef074ac8ea84c711b8437720a22"}, {"name": "1x __components::04edbddbda254d131a3439b11c880f12", "param_count": null, "params": [], "start": **********.087486, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/04edbddbda254d131a3439b11c880f12.blade.php__components::04edbddbda254d131a3439b11c880f12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F04edbddbda254d131a3439b11c880f12.blade.php&line=1", "ajax": false, "filename": "04edbddbda254d131a3439b11c880f12.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::04edbddbda254d131a3439b11c880f12"}, {"name": "1x 8def1252668913628243c4d363bee1ef::custom-template", "param_count": null, "params": [], "start": **********.08886, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/custom-template.blade.php8def1252668913628243c4d363bee1ef::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.089467, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x 8def1252668913628243c4d363bee1ef::loading", "param_count": null, "params": [], "start": **********.090549, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/loading.blade.php8def1252668913628243c4d363bee1ef::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::loading"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.checkbox", "param_count": null, "params": [], "start": **********.092742, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.096206, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x 8def1252668913628243c4d363bee1ef::debug-badge", "param_count": null, "params": [], "start": **********.332178, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/debug-badge.blade.php8def1252668913628243c4d363bee1ef::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::debug-badge"}, {"name": "2x 8def1252668913628243c4d363bee1ef::modal.action", "param_count": null, "params": [], "start": **********.332925, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/action.blade.php8def1252668913628243c4d363bee1ef::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::modal.action"}, {"name": "2x 8def1252668913628243c4d363bee1ef::modal.alert", "param_count": null, "params": [], "start": **********.333825, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/alert.blade.php8def1252668913628243c4d363bee1ef::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::modal.alert"}, {"name": "1x __components::93ebde3601860db875cfe9a96164bda6", "param_count": null, "params": [], "start": **********.335374, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/93ebde3601860db875cfe9a96164bda6.blade.php__components::93ebde3601860db875cfe9a96164bda6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F93ebde3601860db875cfe9a96164bda6.blade.php&line=1", "ajax": false, "filename": "93ebde3601860db875cfe9a96164bda6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::93ebde3601860db875cfe9a96164bda6"}, {"name": "1x __components::16c15d36d71c18d83a7e9e7e1b68a92b", "param_count": null, "params": [], "start": **********.338598, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16c15d36d71c18d83a7e9e7e1b68a92b.blade.php__components::16c15d36d71c18d83a7e9e7e1b68a92b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16c15d36d71c18d83a7e9e7e1b68a92b.blade.php&line=1", "ajax": false, "filename": "16c15d36d71c18d83a7e9e7e1b68a92b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16c15d36d71c18d83a7e9e7e1b68a92b"}, {"name": "1x 8def1252668913628243c4d363bee1ef::layouts.base", "param_count": null, "params": [], "start": **********.33999, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/base.blade.php8def1252668913628243c4d363bee1ef::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.341023, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.343909, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.346319, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.34861, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.349732, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 14, "nb_statements": 14, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00836, "accumulated_duration_str": "8.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.370122, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 6.579}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.375539, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 6.579, "width_percent": 3.589}, {"sql": "select * from `branches` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 966}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.378296, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 10.167, "width_percent": 9.211}, {"sql": "select * from `user_meta` where `user_meta`.`user_id` = 1 and `user_meta`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 62}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 99}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 39}], "start": **********.3827841, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 19.378, "width_percent": 5.622}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.387781, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 25, "width_percent": 4.545}, {"sql": "select `name`, `id` from `cities` where `status` = 'published' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/branch-management/src/Forms/BranchForm.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\branch-management\\src\\Forms\\BranchForm.php", "line": 168}, {"index": 15, "namespace": null, "name": "platform/plugins/branch-management/src/Forms/BranchForm.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\branch-management\\src\\Forms\\BranchForm.php", "line": 83}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Forms\\FormAbstract.php", "line": 101}, {"index": 17, "namespace": null, "name": "vendor/botble/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.411522, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BranchForm.php:168", "source": {"index": 14, "namespace": null, "name": "platform/plugins/branch-management/src/Forms/BranchForm.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\branch-management\\src\\Forms\\BranchForm.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FForms%2FBranchForm.php&line=168", "ajax": false, "filename": "BranchForm.php", "line": "168"}, "connection": "martfury", "explain": null, "start_percent": 29.545, "width_percent": 8.612}, {"sql": "select count(*) as aggregate from `admin_notifications` where `read_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/botble/platform/base/src/Models/AdminNotification.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\AdminNotification.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/base/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Providers\\EventServiceProvider.php", "line": 117}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Providers\\EventServiceProvider.php", "line": 113}], "start": **********.772127, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "AdminNotification.php:62", "source": {"index": 16, "namespace": null, "name": "vendor/botble/platform/base/src/Models/AdminNotification.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\AdminNotification.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FAdminNotification.php&line=62", "ajax": false, "filename": "AdminNotification.php", "line": "62"}, "connection": "martfury", "explain": null, "start_percent": 38.158, "width_percent": 13.038}, {"sql": "select count(*) as aggregate from `contacts` where `status` = 'unread'", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/contact/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\contact\\src\\Providers\\HookServiceProvider.php", "line": 69}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.7800858, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:69", "source": {"index": 16, "namespace": null, "name": "platform/plugins/contact/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\contact\\src\\Providers\\HookServiceProvider.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcontact%2Fsrc%2FProviders%2FHookServiceProvider.php&line=69", "ajax": false, "filename": "HookServiceProvider.php", "line": "69"}, "connection": "martfury", "explain": null, "start_percent": 51.196, "width_percent": 7.297}, {"sql": "select `id`, `name`, `email`, `phone`, `created_at` from `contacts` where `status` = 'unread' order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/contact/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\contact\\src\\Providers\\HookServiceProvider.php", "line": 69}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.782139, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 58.493, "width_percent": 9.689}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.747931, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1401", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1401", "ajax": false, "filename": "HookServiceProvider.php", "line": "1401"}, "connection": "martfury", "explain": null, "start_percent": 68.182, "width_percent": 6.22}, {"sql": "select * from `ec_orders` where (`status` = 'pending' and `is_finished` = 1) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.750071, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 74.402, "width_percent": 7.536}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (72, 73, 75, 76, 77, 78, 80, 81)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.757612, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 81.938, "width_percent": 7.297}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.7617352, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.234, "width_percent": 5.502}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.8837261, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1445", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1445", "ajax": false, "filename": "HookServiceProvider.php", "line": "1445"}, "connection": "martfury", "explain": null, "start_percent": 94.737, "width_percent": 5.263}]}, "models": {"data": {"Botble\\Location\\Models\\City": {"retrieved": 379, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php&line=1", "ajax": false, "filename": "City.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\Contact\\Models\\Contact": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcontact%2Fsrc%2FModels%2FContact.php&line=1", "ajax": false, "filename": "Contact.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\BranchManagement\\Models\\Branch": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FModels%2FBranch.php&line=1", "ajax": false, "filename": "Branch.php", "line": "?"}}, "Botble\\ACL\\Models\\UserMeta": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUserMeta.php&line=1", "ajax": false, "filename": "UserMeta.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 407, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 407}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/admin/branches/edit/1", "action_name": "branches.edit", "controller_action": "Botble\\BranchManagement\\Http\\Controllers\\BranchController@edit", "uri": "GET admin/branches/edit/{branch}", "controller": "Botble\\BranchManagement\\Http\\Controllers\\BranchController@edit<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FHttp%2FControllers%2FBranchController.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\BranchManagement\\Http\\Controllers", "prefix": "admin/branches", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FHttp%2FControllers%2FBranchController.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/branch-management/src/Http/Controllers/BranchController.php:54-59</a>", "middleware": "web, core, auth", "duration": "3.93s", "peak_memory": "76MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-387194383 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-387194383\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1112962412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1112962412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-836427141 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">https://martfury.gc/admin/branches/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3292 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; perf_dv6Tr4n=1; botble_cookie_newsletter=1; XSRF-TOKEN=eyJpdiI6IjQ4ZHBBOFV5WGlsV2dRN3Zxa0RsTVE9PSIsInZhbHVlIjoiOTJKU1d5bzVIRWJ3Y2JqVWxNTDBOZUZoZ2NJZ1lKQXNabjc1U0ZVaWJtQTF2djBqNk1QQ3NOZEtmaVpBT3dubjkxQVVFVXBVMTlIN2NCMGpxbGlFRjkvVlN0Ukp6c1hlWHFGNnRaQmFZMk53QVE4RTJHcTdFUnpPNDBBbXFaYjAiLCJtYWMiOiJmOGNjNzg0YjRjMTc2N2ZlZDJmY2ZiYWRkOWVjZDA0YjM0YzU5NjA1YmY4NzhjZjFmN2Y5YzM0OGFlOTlkODBhIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InpxVER5SmlpTDJLYjJmckUvN1UrRGc9PSIsInZhbHVlIjoiZjNlNWhGZnZsUjN0ODJ4Uk90dGtZR2JTQnY0NFNNRlI1MndMcjNRZGEwVXFQYXdudi8yYU9RVDQ3MTVVaFhCOFZXK084d0x5eVg4MGtqcXNSKzBDZ1RLTkRrSUJmRi9kRmVaRHhZbi90eExtbWlUSXpMR2FKMVl4YUQycnhvM3MiLCJtYWMiOiIyZThiMzFhZDJlYzVkYjk1NWNlMWQzN2M4NTEyNDBhM2JhZGQ2NWQxOWJkYWVjNWM5NTRjNmRiNTI4M2ViY2Q5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836427141\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-866231945 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VcRzgLLYZnl2p0nut86nGe19fEl1DoQGuytBwvJ0</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jd5XX6SG6uq7qt4J1nCyZma0wd2N24xRE6YVO5Zl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-866231945\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-839970169 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 29 Aug 2025 15:35:32 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839970169\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1522229636 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VcRzgLLYZnl2p0nut86nGe19fEl1DoQGuytBwvJ0</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://martfury.gc/admin/branches/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>2</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>7</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K3V7GBWN6AZE87ZAK8J09B1A</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756480542\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756480542</span></span> {<a class=sf-dump-ref href=#sf-dump-1522229636-ref24722 title=\"3 occurrences\">#4722</a><samp data-depth=3 id=sf-dump-1522229636-ref24722 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012720000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:19:49.8118 from now\nDST Off\">2025-08-29 15:15:42.550962 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4723</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>aa26812c99f0c997f1e301988b86cbb6</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4724</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">aa26812c99f0c997f1e301988b86cbb6</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>30</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"25 characters\">Red &amp;amp; Black Headphone</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>417.15</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4725</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/4.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"22 characters\">(Color: Blue, Size: S)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-129-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>865.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756480542\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756480542</span></span> {<a class=sf-dump-ref>#4726</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012760000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:19:49.812241 from now\nDST Off\">2025-08-29 15:15:42.550928 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756480542\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756480542</span></span> {<a class=sf-dump-ref>#4727</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012770000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:19:49.812303 from now\nDST Off\">2025-08-29 15:15:42.550921 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">10ed7024f09405b8e39bbeef73d7bedb</span>\"\n  \"<span class=sf-dump-key>4f95712248d816491efaef8b758a20d3</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:17</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756480542\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756480542</span></span> {<a class=sf-dump-ref href=#sf-dump-1522229636-ref24722 title=\"3 occurrences\">#4722</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>83</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756480542\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756480542</span></span> {<a class=sf-dump-ref href=#sf-dump-1522229636-ref24722 title=\"3 occurrences\">#4722</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => <span class=sf-dump-num>10.0</span>\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>branch_pickup</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>pickup_branch</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Branch Pickup</span>\"\n              \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Please select your preferred pickup location</span>\"\n              \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>disabled</span>\" => <span class=sf-dump-const>false</span>\n              \"<span class=sf-dump-key>option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Created successfully</span>\"\n  \"<span class=sf-dump-key>abandoned_cart_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>abandoned_cart_phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">03147552550</span>\"\n  \"<span class=sf-dump-key>abandoned_cart_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Branch 1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522229636\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/admin/branches/edit/1", "action_name": "branches.edit", "controller_action": "Botble\\BranchManagement\\Http\\Controllers\\BranchController@edit"}, "badge": null}}