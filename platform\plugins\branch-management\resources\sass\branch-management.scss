// Branch Management Plugin Styles

.branch-pickup-section {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;

    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
}

.branch-details {
    .card {
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .card-title {
        color: #495057;
        font-size: 1.1rem;
    }
}

.branch-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-color: #007bff;
    }

    .card-img-top {
        transition: transform 0.3s ease;
    }

    &:hover .card-img-top {
        transform: scale(1.05);
    }
}

.operating-hours-wrapper {
    .row {
        border-bottom: 1px solid #f8f9fa;
        padding: 0.5rem 0;

        &:last-child {
            border-bottom: none;
        }
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
}

.branch-status {
    .badge {
        margin-right: 0.25rem;
        font-size: 0.75rem;
    }
}

.branch-info {
    font-size: 0.9rem;

    i {
        width: 16px;
        text-align: center;
        margin-right: 0.5rem;
    }
}

.branches-shortcode {
    .city-name {
        color: #495057;
        border-bottom: 2px solid #007bff;
        padding-bottom: 0.5rem;
    }
}

// Admin specific styles
.admin-bar {
    .operating-hours-wrapper {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
    }

    .branch-form-section {
        margin-bottom: 2rem;

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #495057;
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .branch-card {
        margin-bottom: 1rem;
    }

    .operating-hours-wrapper .row {
        flex-direction: column;
        
        .col-md-2, .col-md-3 {
            margin-bottom: 0.5rem;
        }
    }

    .branch-details .row {
        flex-direction: column;
    }
}
