<?php

namespace Botble\BranchManagement\Repositories\Interfaces;

use Botble\Support\Repositories\Interfaces\RepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

interface BranchInterface extends RepositoryInterface
{
    /**
     * Get branches by city ID
     */
    public function getBranchesByCity(int $cityId, bool $pickupOnly = false): Collection;

    /**
     * Get featured branches
     */
    public function getFeaturedBranches(int $limit = 10): Collection;

    /**
     * Get branches with pickup available
     */
    public function getPickupAvailableBranches(): Collection;

    /**
     * Get branches grouped by city
     */
    public function getBranchesGroupedByCity(): Collection;

    /**
     * Search branches by name or address
     */
    public function searchBranches(string $keyword): Collection;

    /**
     * Get cities that have branches with pickup available
     */
    public function getCitiesWithPickupBranches(): Collection;
}
