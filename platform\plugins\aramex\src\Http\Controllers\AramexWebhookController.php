<?php

namespace Shaqi\Aramex\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Ecommerce\Enums\ShippingStatusEnum;
use <PERSON><PERSON><PERSON>\Ecommerce\Facades\OrderHelper;
use Botble\Ecommerce\Models\Shipment;
use Botble\Ecommerce\Repositories\Interfaces\ShipmentHistoryInterface;
use Botble\Ecommerce\Repositories\Interfaces\ShipmentInterface;
use Shaqi\Aramex\Aramex;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Shaqi\Aramex\API\Classes\Address;
use Shaqi\Aramex\API\Classes\ShipmentDetails;

class AramexWebhookController extends BaseController
{
    public function __construct(
        protected ShipmentInterface $shipmentRepository,
        protected ShipmentHistoryInterface $shipmentHistoryRepository,
        protected Aramex $aramex
    ) {
    }

    public function index(Request $request, BaseHttpResponse $response)
    {
        $event = $request->input('event');
        $data = (array) $request->input('data', []);

        $transactionId = null;

        switch ($event) {
            case 'transaction_updated':
                $transactionId = Arr::get($data, 'object_id');

                break;
            case 'track_updated':
                $transactionId = Arr::get($data, 'tracking_status.object_id');

                break;
            default:
                //$this->aramex->log([__LINE__, print_r($request->input(), true)]);

                break;
        }

        if (! $transactionId) {
            return $response;
        }

        $condition = [
            'tracking_id' => $transactionId,
        ];

        $shipment = $this->shipmentRepository->getFirstBy($condition);

        if (! $shipment) {
            //$this->aramex->log([__LINE__, print_r($condition, true)]);

            return $response;
        }

        switch ($event) {
            case 'transaction_updated':
                $this->transactionUpdated($shipment, $data);

                break;
            case 'track_updated':
                $this->trackUpdated($shipment, $data);

                break;
        }

        return $response;
    }

    protected function transactionUpdated(Shipment $shipment, array $data)
    {
        $status = Arr::get($data, 'status');
        if ($status == 'REFUNDED') {
            $shipment->status = ShippingStatusEnum::CANCELED;
            $shipment->save();
        }

        $this->shipmentHistoryRepository->createOrUpdate([
            'action' => 'transaction_updated',
            'description' => trans('plugins/aramex::aramex.transaction.updated', [
                'tracking' => Arr::get($data, 'tracking_number'),
            ]),
            'order_id' => $shipment->order_id,
            'user_id' => 0,
            'shipment_id' => $shipment->id,
        ]);
    }

    protected function trackUpdated(Shipment $shipment, array $data)
    {
        $status = Arr::get($data, 'tracking_status.status');
        switch ($status) {
            case 'PRE_TRANSIT':

                break;
            case 'TRANSIT':
                $shipment->status = ShippingStatusEnum::DELIVERING;
                $shipment->save();

                break;
            case 'DELIVERED':
                $shipment->status = ShippingStatusEnum::DELIVERED;
                $shipment->date_shipped = Carbon::now();
                $shipment->save();

                OrderHelper::shippingStatusDelivered($shipment, request());

                break;
            case 'RETURNED':
                $shipment->status = ShippingStatusEnum::CANCELED;
                $shipment->save();

                break;
        }

        $this->shipmentHistoryRepository->createOrUpdate([
            'action' => 'track_updated',
            'description' => trans('plugins/aramex::aramex.tracking.statuses.' . Str::lower($status)),
            'order_id' => $shipment->order_id,
            'user_id' => 0,
            'shipment_id' => $shipment->id,
        ]);
    }

    public function test(){

        
        // dd($this->aramex::fetchCountries()->run());
        // dd($this->aramex::fetchCountry()->setCode('AE')->run());
        // dd( $this->aramex::fetchStates()->setCountryCode('AE')->run());
        // dd( $this->aramex::fetchCities()->setCountryCode('AE')->run());
        // dd( $this->aramex::createShipments()->run());

        // $source = new Address('','AE');
        // $source->setCity('Dubai');
        // // $source->setStateOrProvinceCode('Dubai');
        // $source->setPostCode('131');
        // $source->setCountryCode('AE');

        // $destination = new Address('','AE');
        // $destination->setCity('Dubai');
        // // $destination->setStateOrProvinceCode('Dubai');
        // $destination->setPostCode('131');
        // $destination->setCountryCode('AE');

        // $details = new ShipmentDetails(0.6,  'null', 'null', 1, 'DOM', 'ONP', 'P', []);

        // // dd($this->aramex::validateAddress()->setAddress($source)->run());
        
        // $rateCalculatorResponse = $this->aramex::calculateRate()
        // ->setOriginalAddress($source)
        // ->setDestinationAddress($destination)
        // ->setShipmentDetails($details)
        // ->setPreferredCurrencyCode('AED')
        // ->run();

        // // Access the totalAmount property
        // $totalAmount = $rateCalculatorResponse->getTotalAmount();

        // // Access the value property of the Money class
        // // $value = $totalAmount->value;

        // // Print the value
        //  dd($totalAmount->getValue());

    }
}
