<?php

use Botble\Ecommerce\Facades\EcommerceHelper;
use Botble\Location\Facades\Location;
use Botble\Location\Models\City;
use Botble\Location\Models\Country;
use Botble\Location\Models\State;
use Illuminate\Support\Arr;
use Shaqi\Aramex\API\Classes\Address;
use Shaqi\Aramex\API\Classes\ShipmentDetails;
use Shaqi\Aramex\Aramex;

if (!function_exists('aramexAllowedDomesticMethods')) {
    function aramexAllowedDomesticMethods()
    {
        $methods = [
            "BLK" => "Special: Bulk Mail Delivery",
            "BLT" => "Domestic - Bullet Delivery",
            "CDA" => "Special Delivery",
            "CDS" => "E-commerce/Special Credit Card Delivery",
            "CGO" => "Air Cargo (India)",
            "COM" => "Special: Cheque Collection",
            "DEC" => "Special: Invoice Delivery",
            "EMD" => "Early Morning delivery",
            "FIX" => "Special: Bank Branches Run",
            "LGS" => "Logistic Shipment",
            "OND" => "Overnight (Document)",
            "ONP" => "Overnight (Parcel)",
            "P24" => "Road Freight 24 hours service",
            "P48" => "Road Freight 48 hours service",
            "PEC" => "Economy Delivery",
            "PEX" => "Road Express",
            "SFC" => "Surface Cargo (India)",
            "SMD" => "Same Day (Document)",
            "SMP" => "Same Day (Parcel)",
            "SDD" => "Same Day Delivery",
            "HVY" => "Heavy (20kgs and more)",
            "SPD" => "Special: Legal Branches Mail Service",
            "SPL" => "Special : Legal Notifications Delivery",
        ];

        return $methods;
    }
}

if (!function_exists('aramexAllowedDomesticAdditionalServices')) {
    function aramexAllowedDomesticAdditionalServices()
    {
        $additional_services = [
            "AM10" => "Morning delivery",
            "CHST" => "Chain Stores Delivery",
            "CODS" => "Cash On Delivery Service",
            "COMM" => "Commercial",
            "CRDT" => "Credit Card",
            "DDP"  => "DDP - Delivery Duty Paid - For European Use",
            "DDU"  => "DDU - Delivery Duty Unpaid - For the European Freight",
            "EXW"  => "Not An Aramex Customer - For European Freight",
            "INSR" => "Insurance",
            "RTRN" => "Return",
            "SPCL" => "Special Services",
            "ABX"  => "ABX",
            "EUCO" => "NULL",
        ];

        return $additional_services;
    }
}

if (!function_exists('aramexAllowedInternationalMethods')) {

    function aramexAllowedInternationalMethods()
    {
        $methods = [
            'DPX' => 'Value Express Parcels',
            'EDX' => 'Economy Document Express',
            'EPX' => 'Economy Parcel Express',
            'GDX' => 'Ground Document Express',
            'GPX' => 'Ground Parcel Express',
            'IBD' => 'International defered',
            'PDX' => 'Priority Document Express',
            'PLX' => 'Priority Letter Express (<.5 kg Docs)',
            'PPX' => 'Priority Parcel Express',
            'ABX' => 'ABX',
            'PXP' => 'Premium Express',
            'DGX' => 'Dangerous Goods Express',
            'DGG' => 'Dangerous Goods Ground',
        ];

        return $methods;
    }
}
if (!function_exists('aramexAllowedInternationalAdditionalServices')) {
    function aramexAllowedInternationalAdditionalServices()
    {
        $additional_services = [
            'AM10' => 'Morning delivery',
            'CODS' => 'Cash On Delivery',
            'CSTM' => 'CSTM',
            'EUCO' => 'NULL',
            'FDAC' => 'FDAC',
            'FRDM' => 'FRDM',
            'INSR' => 'Insurance',
            'NOON' => 'Noon Delivery',
            'ODDS' => 'Over Size',
            'RTRN' => 'RTRN',
            'SIGR' => 'Signature Required',
            'SPCL' => 'Special Services',
        ];

        return $additional_services;
    }
}
if (!function_exists('aramexProductsGroups')) {
    function aramexProductsGroups()
    {
        $product_groups = [
            "DOM" => "Domestic",
            "EXP" => "International Express",
        ];

        return $product_groups;
    }
}

if (!function_exists('aramexPaymentTypes')) {
    function aramexPaymentTypes()
    {
        $payment_types = [
            "P" => "Prepaid",
            "C" => "Collect",
            "3" => "Third Party",
        ];

        return $payment_types;
    }
}
if (!function_exists('aramexPaymentOptions')) {
    function aramexPaymentOptions()
    {
        $payment_options = [
            "CASH" => "Cash",
            "ACCT" => "Account",
            "PPST" => "Prepaid Stock",
            "CRDT" => "Credit",
        ];

        return $payment_options;
    }
}

if (!function_exists('aramexPickupStatus')) {
    function aramexPickupStatus()
    {
        $status = [
            "Ready" => "Ready",
            "Pending" => "Pending",
        ];

        return $status;
    }
}
