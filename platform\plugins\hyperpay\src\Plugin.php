<?php

namespace Shaqi\HyperPay;

use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use Bo<PERSON><PERSON>\Setting\Facades\Setting;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Setting::delete([
            'payment_hyperpay_name',
            'payment_hyperpay_description',
            'payment_hyperpay_access_token',
            'payment_hyperpay_visa_entity_id',
            'payment_hyperpay_mada_entity_id',
            'payment_hyperpay_amex_entity_id',
            'payment_hyperpay_applepay_entity_id',
            'payment_hyperpay_sandbox_mode',
            'payment_hyperpay_status',
            'payment_hyperpay_currency',
            'payment_hyperpay_webhook_secret',
            'payment_hyperpay_3ds_enabled',
            'payment_hyperpay_available_countries',
        ]);
    }
}
