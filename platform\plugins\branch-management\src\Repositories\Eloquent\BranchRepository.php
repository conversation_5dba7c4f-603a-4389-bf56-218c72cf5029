<?php

namespace Botble\BranchManagement\Repositories\Eloquent;

use Bo<PERSON>ble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Botble\Support\Repositories\Eloquent\RepositoriesAbstract;
use Illuminate\Database\Eloquent\Collection;

class BranchRepository extends RepositoriesAbstract implements BranchInterface
{
    public function getBranchesByCity(int $cityId, bool $pickupOnly = false): Collection
    {
        $query = $this->model
            ->newQuery()
            ->with(['city', 'state', 'country'])
            ->active()
            ->byCity($cityId)
            ->orderBy('order')
            ->orderBy('name');

        if ($pickupOnly) {
            $query->pickupAvailable();
        }

        return $query->get();
    }

    public function getFeaturedBranches(int $limit = 10): Collection
    {
        return $this->model
            ->newQuery()
            ->with(['city', 'state', 'country'])
            ->active()
            ->featured()
            ->orderBy('order')
            ->orderBy('name')
            ->limit($limit)
            ->get();
    }

    public function getPickupAvailableBranches(): Collection
    {
        return $this->model
            ->newQuery()
            ->with(['city', 'state', 'country'])
            ->active()
            ->pickupAvailable()
            ->orderBy('order')
            ->orderBy('name')
            ->get();
    }

    public function getBranchesGroupedByCity(): Collection
    {
        return $this->model
            ->newQuery()
            ->with(['city', 'state', 'country'])
            ->active()
            ->orderBy('order')
            ->orderBy('name')
            ->get()
            ->groupBy('city.name');
    }

    public function searchBranches(string $keyword): Collection
    {
        return $this->model
            ->newQuery()
            ->with(['city', 'state', 'country'])
            ->active()
            ->where(function ($query) use ($keyword) {
                $query->where('name', 'LIKE', "%{$keyword}%")
                    ->orWhere('address', 'LIKE', "%{$keyword}%")
                    ->orWhere('description', 'LIKE', "%{$keyword}%");
            })
            ->orderBy('order')
            ->orderBy('name')
            ->get();
    }



    public function getCitiesWithPickupBranches(): Collection
    {
        return $this->model
            ->newQuery()
            ->join('cities', 'branches.city_id', '=', 'cities.id')
            ->where('branches.status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
            ->where('branches.is_pickup_available', true)
            ->select('cities.id', 'cities.name')
            ->distinct()
            ->orderBy('cities.name')
            ->get();
    }
}
