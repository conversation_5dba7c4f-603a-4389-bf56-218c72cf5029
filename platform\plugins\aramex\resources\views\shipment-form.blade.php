<div class="container">

    <x-core::form  :url="route('ecommerce.shipments.aramex.shipment.create', $shipment->id)">
        <input name="order_id" type="hidden" value="{{ $order->id }}" />

        <div class="row">
            <div class="col-md-12">
                <fieldset class="form-fieldset">
                <h3>Shipment Information</h3>
                <div class="row">
                    <div class="col-md-4">
                        <x-core::form.text-input
                            :label="trans('Total Weight ('. ecommerce_weight_unit().')')"
                            :required="true"
                            name="weight"
                            :value="$shipment->weight"
                        />
                    </div>

                    <div class="col-md-4">
                        <x-core::form.select
                            :label="trans('Payment Type')"
                            name="payment_type"
                            :options="aramexPaymentTypes()"
                            :searchable="true"
                        />
                    </div>
                    <div class="col-md-4">
                        <x-core::form.select
                            :label="trans('Payment Option')"
                            name="payment_option"
                            :options="aramexPaymentOptions()"
                            :searchable="true"
                        />
                    </div>
                    <div class="col-md-6">
                        <div id="product_group">
                            <x-core::form.select
                            :label="trans('Product Group')"
                            name="product_group"
                            :options="aramexProductsGroups()"
                            :value="$product_group"
                            :searchable="true"
                        />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div id="allowed_domestic_methods" @if($product_group=='EXP') style="display: none;" @else  @endif>
                            <x-core::form.select
                            :label="trans('Product Type')"
                            name="allowed_domestic_methods"
                            :options="$allowed_domestic_methods"
                            :searchable="true"
                        />
                        </div>
                        <div id="allowed_international_methods" @if($product_group=='DOM') style="display: none;" @else @endif>
                            <x-core::form.select
                            :label="trans('Product Type')"
                            name="allowed_international_methods"
                            :options="$allowed_international_methods"
                            :searchable="true"
                        />
                        </div>
                    </div>
                    {{-- <div class="col-md-4">
                        <div id="allowed_domestic_additional_service" @if($product_group=='EXP') style="display: none;" @else  @endif>
                            <x-core::form.select
                            :label="trans('Additional Services')"
                            name="allowed_domestic_additional_service"
                            :options="$allowed_domestic_additional_service"
                            :searchable="true"
                        />
                        </div>
                        <div id="allowed_international_additional_service" @if($product_group=='DOM') style="display: none;" @else @endif>
                            <x-core::form.select
                            :label="trans('Additional Services')"
                            name="allowed_international_additional_service"
                            :options="$allowed_international_additional_service"
                            :searchable="true"
                        />
                        </div>
                    </div> --}}
                    <div class="col-md-4">
                        <x-core::form.text-input
                            :label="trans('Custom Amount')"
                            :required="true"
                            name="custom_amount"
                            :value="$order->amount"
                        />
                    </div>
                    <div class="col-md-4">
                        <x-core::form.text-input
                            :label="trans('Items Price')"
                            :required="true"
                            readonly
                            name="items_price"
                            :value="$order->amount"
                        />
                    </div>
                    <div class="col-md-4">
                        <x-core::form.text-input
                            :label="trans('Number of Pieces')"
                            :required="true"
                            name="number_of_pieces"
                            :value="$number_of_pieces"
                        />
                    </div>

                </div>
                </fieldset>
            </div>
          
            <div class="col-md-6">
                <fieldset class="form-fieldset">
                <h3>Shipper Details</h3>

                <div class="row">
                    <div class="col-md-6">
                        <x-core::form.text-input
                            :label="trans('plugins/ecommerce::shipping.form_name')"
                            :required="true"
                            name="shipper_name"
                            :value="$shipper_address->name"
                            :placeholder="trans('plugins/ecommerce::shipping.form_name')"
                        />
                    </div>

                    <div class="col-md-6">
                        <x-core::form.text-input
                            :label="trans('plugins/ecommerce::shipping.phone')"
                            :required="true"
                            name="shipper_phone"
                            :value="$shipper_address->phone"
                            :placeholder="trans('plugins/ecommerce::shipping.phone')"
                        />
                    </div>
                </div>

                <x-core::form.text-input
                    :label="trans('plugins/ecommerce::shipping.email')"
                    :required="true"
                    type="email"
                    name="shipper_email"
                    :value="$shipper_address->email"
                    :placeholder="trans('plugins/ecommerce::shipping.email')"
                />

                <x-core::form.text-input
                    :label="trans('Company')"
                    :required="true"
                    name="shipper_company"
                    :value="$shipper_address->company"
                    :placeholder="trans('Company')"
                />

                @if (EcommerceHelper::isUsingInMultipleCountries())
                    <x-core::form.select
                        :label="trans('plugins/ecommerce::shipping.country')"
                        :required="true"
                        name="shipper_country"
                        data-type="country"
                        :options="EcommerceHelper::getAvailableCountries()"
                        :value="$shipper_address->country"
                        :searchable="true"
                    />
                @else
                    <input
                        name="shipper_country"
                        required
                        type="hidden"
                        value="{{ EcommerceHelper::getFirstCountryId() }}"
                    >
                @endif

                @if (EcommerceHelper::loadCountriesStatesCitiesFromPluginLocation())
                    <x-core::form.select
                        :label="trans('plugins/ecommerce::shipping.state')"
                        name="shipper_state"
                        data-type="state"
                        :required="true"
                        :data-url="route('ajax.states-by-country')"
                        :searchable="true"
                    >
                        <option value="">{{ __('Select state...') }}</option>
                        @if ($shipper_address->state || !EcommerceHelper::isUsingInMultipleCountries())
                            @foreach (EcommerceHelper::getAvailableStatesByCountry($shipper_address->country) as $stateId => $stateName)
                                <option
                                    value="{{ $stateId }}"
                                    @if ($shipper_address->state == $stateId) selected @endif
                                >{{ $stateName }}</option>
                            @endforeach
                        @endif
                    </x-core::form.select>
                @else
                    <x-core::form.text-input
                        :label="trans('plugins/ecommerce::shipping.state')"
                        name="shipper_state"
                        :value="$shipper_address->state"
                        placeholder="{{ trans('plugins/ecommerce::shipping.state') }}"
                    />
                @endif

                @if (! EcommerceHelper::useCityFieldAsTextField())
                    <x-core::form.select
                        :label="trans('plugins/ecommerce::shipping.city')"
                        name="shipper_city"
                        data-type="city"
                        :required="true"
                        data-using-select2="false"
                        :data-url="route('ajax.cities-by-state')"
                    >
                        <option value="">{{ __('Select city...') }}</option>
                        @if ($shipper_address->city)
                            @foreach (EcommerceHelper::getAvailableCitiesByState($shipper_address->state) as $cityId => $cityName)
                                <option
                                    value="{{ $cityId }}"
                                    @if ($shipper_address->city == $cityId) selected @endif
                                >{{ $cityName }}</option>
                            @endforeach
                        @endif
                    </x-core::form.select>
                @else
                    <x-core::form.text-input
                        :label="trans('plugins/ecommerce::shipping.city')"
                        name="shipper_city"
                        :required="true"
                        :value="$shipper_address->city"
                        placeholder="{{ trans('plugins/ecommerce::shipping.city') }}"
                    />
                @endif

                <x-core::form.text-input
                    :label="trans('plugins/ecommerce::shipping.address')"
                    :required="true"
                    name="shipper_address"
                    :value="$shipper_address->address"
                    :placeholder="trans('plugins/ecommerce::shipping.address')"
                />

                @if (EcommerceHelper::isZipCodeEnabled())
                    <x-core::form.text-input
                        :label="trans('plugins/ecommerce::shipping.zip_code')"
                        name="shipper_zip_code"
                        :required="true"
                        :value="$shipper_address->zip_code"
                        :placeholder="trans('plugins/ecommerce::shipping.zip_code')"
                    />
                @endif
                </fieldset>
            </div>
            <div class="col-md-6">
                <fieldset class="form-fieldset">
                <h3>Receiver Details</h3>

                <div class="row">
                    <div class="col-md-6">
                        <x-core::form.text-input
                            :label="trans('plugins/ecommerce::shipping.form_name')"
                            :required="true"
                            name="receiver_name"
                            :value="$receiver_address->name"
                            :placeholder="trans('plugins/ecommerce::shipping.form_name')"
                        />
                    </div>

                    <div class="col-md-6">
                        <x-core::form.text-input
                            :label="trans('plugins/ecommerce::shipping.phone')"
                            name="receiver_phone"
                            :required="true"
                            :value="$receiver_address->phone"
                            :placeholder="trans('plugins/ecommerce::shipping.phone')"
                        />
                    </div>
                </div>

                <x-core::form.text-input
                    :label="trans('plugins/ecommerce::shipping.email')"
                    type="email"
                    :required="true"
                    name="receiver_email"
                    :value="$receiver_address->email"
                    :placeholder="trans('plugins/ecommerce::shipping.email')"
                />
                <x-core::form.text-input
                    :label="trans('Company')"
                    :required="true"
                    name="receiver_company"
                    :value="$receiver_address->name"
                    :placeholder="trans('Company')"
                />

                @if (EcommerceHelper::isUsingInMultipleCountries())
                    <x-core::form.select
                        :label="trans('plugins/ecommerce::shipping.country')"
                        name="receiver_country"
                        data-type="country"
                        :required="true"
                        :options="EcommerceHelper::getAvailableCountries()"
                        :value="$receiver_address->country"
                        :searchable="true"
                    />
                @else
                    <input
                        name="receiver_country"
                        type="hidden"
                        required
                        value="{{ EcommerceHelper::getFirstCountryId() }}"
                    >
                @endif

                @if (EcommerceHelper::loadCountriesStatesCitiesFromPluginLocation())
                    <x-core::form.select
                        :label="trans('plugins/ecommerce::shipping.state')"
                        name="receiver_state"
                        data-type="state"
                        :data-url="route('ajax.states-by-country')"
                        :searchable="true"
                        :required="true"
                    >
                        <option value="">{{ __('Select state...') }}</option>
                        @if ($receiver_address->state || !EcommerceHelper::isUsingInMultipleCountries())
                            @foreach (EcommerceHelper::getAvailableStatesByCountry($receiver_address->country) as $stateId => $stateName)
                                <option
                                    value="{{ $stateId }}"
                                    @if ($receiver_address->state == $stateId) selected @endif
                                >{{ $stateName }}</option>
                            @endforeach
                        @endif
                    </x-core::form.select>
                @else
                    <x-core::form.text-input
                        :label="trans('plugins/ecommerce::shipping.state')"
                        name="receiver_state"
                        :value="$receiver_address->state"
                        placeholder="{{ trans('plugins/ecommerce::shipping.state') }}"
                    />
                @endif

                @if (! EcommerceHelper::useCityFieldAsTextField())
                    <x-core::form.select
                        :label="trans('plugins/ecommerce::shipping.city')"
                        name="receiver_city"
                        data-type="city"
                        :required="true"
                        data-using-select2="false"
                        :data-url="route('ajax.cities-by-state')"
                    >
                        <option value="">{{ __('Select city...') }}</option>
                        @if ($receiver_address->city)
                            @foreach (EcommerceHelper::getAvailableCitiesByState($receiver_address->state) as $cityId => $cityName)
                                <option
                                    value="{{ $cityId }}"
                                    @if ($receiver_address->city == $cityId) selected @endif
                                >{{ $cityName }}</option>
                            @endforeach
                        @endif
                    </x-core::form.select>
                @else
                    <x-core::form.text-input
                        :label="trans('plugins/ecommerce::shipping.city')"
                        name="receiver_city"
                        :required="true"
                        :value="$receiver_address->city"
                        placeholder="{{ trans('plugins/ecommerce::shipping.city') }}"
                    />
                @endif

                <x-core::form.text-input
                    :label="trans('plugins/ecommerce::shipping.address')"
                    :required="true"
                    name="receiver_address"
                    :value="$receiver_address->address"
                    :placeholder="trans('plugins/ecommerce::shipping.address')"
                />

                @if (EcommerceHelper::isZipCodeEnabled())
                    <x-core::form.text-input
                        :label="trans('plugins/ecommerce::shipping.zip_code')"
                        name="receiver_zip_code"
                        :required="true"
                        :value="$receiver_address->zip_code"
                        :placeholder="trans('plugins/ecommerce::shipping.zip_code')"
                    />
                @endif
                </fieldset>
            </div>


            <div class="col-md-12">
                <button class="btn btn-primary float-end" type="submit">
                    <span>{{ trans('Submit') }}</span>
                </button>
            </div>


    </x-core::form>
</div>
</div>
</div>


