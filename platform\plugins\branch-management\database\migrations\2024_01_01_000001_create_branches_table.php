<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table): void {
            $table->id();
            $table->string('name', 255);
            $table->text('description')->nullable();
            $table->string('address', 500);
            $table->string('phone', 20)->nullable();
            $table->string('email', 100)->nullable();
            $table->string('manager_name', 100)->nullable();
            $table->string('manager_phone', 20)->nullable();
            $table->string('manager_email', 100)->nullable();

            // Location relationships
            $table->foreignId('city_id')->constrained('cities')->onDelete('cascade');
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('set null');
            $table->foreignId('country_id')->nullable()->constrained('countries')->onDelete('set null');

            // Operating hours (simple text format)
            $table->text('operating_hours')->nullable();

            // Additional fields
            $table->string('zip_code', 20)->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('image', 255)->nullable();
            $table->text('special_instructions')->nullable();

            // Status and ordering
            $table->string('status', 60)->default('published');
            $table->tinyInteger('order')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_pickup_available')->default(true);
   
            $table->timestamps();

            // Indexes
            $table->index(['city_id', 'status']);
            $table->index(['status', 'order']);
            $table->index(['is_pickup_available', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('branches');
    }
};
