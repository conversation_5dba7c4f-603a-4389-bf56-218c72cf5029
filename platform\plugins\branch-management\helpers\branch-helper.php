<?php

if (!function_exists('get_branch_pickup_shipping_name')) {
    /**
     * Get dynamic branch pickup shipping method name
     *
     * @param int|null $branchId
     * @param int|null $cityId
     * @return string
     */
    function get_branch_pickup_shipping_name($branchId = null, $cityId = null): string
    {
        $baseName = trans('plugins/branch-management::branch.pickup.title'); // "Branch Pickup"

        if (!$branchId) {
            return $baseName;
        }

        try {
            $branchRepository = app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class);
            $branch = $branchRepository->findById($branchId);

            if (!$branch) {
                return $baseName;
            }

            $branchName = $branch->name;
            $cityName = '';

            // Get city name from branch relationship or provided cityId
            if ($branch->city) {
                $cityName = $branch->city->name;
            } elseif ($cityId && class_exists('\Botble\Location\Models\City')) {
                $city = \Botble\Location\Models\City::find($cityId);
                $cityName = $city ? $city->name : '';
            }

            // Build dynamic name: "Branch Pickup: Branch Name -> City Name"
            $dynamicName = $baseName . ': ' . $branchName;
            if ($cityName) {
                $dynamicName .= ' -> ' . $cityName;
            }

            return $dynamicName;

        } catch (\Exception $e) {
            \Log::warning('Error building branch pickup shipping name: ' . $e->getMessage());
            return $baseName;
        }
    }
}

if (!function_exists('get_branch_pickup_info_for_order')) {
    /**
     * Get branch pickup information for order display
     *
     * @param \Botble\Ecommerce\Models\Order $order
     * @return array|null
     */
    function get_branch_pickup_info_for_order($order): ?array
    {
        // Use the new BranchPickupService to get branch pickup data from metadata
        $branchPickupService = app(\Botble\BranchManagement\Services\BranchPickupService::class);
        $branchInfo = $branchPickupService->getBranchPickupFromOrder($order);

        if (!$branchInfo) {
            return null;
        }

        // Add dynamic shipping name for backward compatibility
        $branchInfo['dynamic_shipping_name'] = trans('plugins/branch-management::branch.pickup.title') . ': ' . $branchInfo['branch_name'];
        if ($branchInfo['city_name']) {
            $branchInfo['dynamic_shipping_name'] .= ' -> ' . $branchInfo['city_name'];
        }

        return $branchInfo;
    }
}

if (!function_exists('update_order_shipping_method_name')) {
    /**
     * Update order's shipping method name with branch details
     *
     * @param \Botble\Ecommerce\Models\Order $order
     * @param int $branchId
     * @param int|null $cityId
     * @return bool
     */
    function update_order_shipping_method_name($order, $branchId, $cityId = null): bool
    {
        try {
            $dynamicName = get_branch_pickup_shipping_name($branchId, $cityId);

            $order->update([
                'shipping_method_name' => $dynamicName,
            ]);

            \Log::info('Order shipping method name updated', [
                'order_id' => $order->id,
                'new_shipping_method_name' => $dynamicName,
            ]);

            return true;

        } catch (\Exception $e) {
            \Log::error('Error updating order shipping method name: ' . $e->getMessage());
            return false;
        }
    }
}
