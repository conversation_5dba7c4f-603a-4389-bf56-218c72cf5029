<?php

namespace Bo<PERSON>ble\BranchManagement\Models;

use Botble\Ecommerce\Models\Order;
use Illuminate\Database\Eloquent\Casts\Attribute;

class OrderExtension extends Order
{
    /**
     * Override the shipping method name accessor to handle branch pickup
     */
    protected function shippingMethodName(): Attribute
    {
        return Attribute::get(function () {
            // Handle branch pickup orders
            if ($this->attributes['shipping_method'] === BRANCH_PICKUP_SHIPPING_METHOD_NAME) {
                $branchInfo = get_branch_pickup_info_for_order($this);
                if ($branchInfo) {
                    return $branchInfo['dynamic_shipping_name'];
                }
                
                // Fallback to base name
                return trans('plugins/branch-management::branch.pickup.title');
            }

            // For non-branch pickup orders, use default logic
            return app(\Botble\Ecommerce\Supports\OrderHelper::class)->getShippingMethod(
                $this->attributes['shipping_method'],
                $this->attributes['shipping_option']
            );
        });
    }
}
