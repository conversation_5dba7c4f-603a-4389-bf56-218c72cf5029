<?php

namespace Bo<PERSON>ble\BranchManagement\Models;

use Botble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Botble\Location\Models\City;
use Botble\Location\Models\Country;
use Botble\Location\Models\State;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Branch extends BaseModel
{

    protected $table = 'branches';

    protected $fillable = [
        'name',
        'description',
        'address',
        'phone',
        'email',
        'manager_name',
        'manager_phone',
        'manager_email',
        'city_id',
        'state_id',
        'country_id',
        'operating_hours',
        'zip_code',
        'latitude',
        'longitude',
        'image',
        'special_instructions',
        'status',
        'order',
        'is_featured',
        'is_pickup_available',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'address' => SafeContent::class,
        'manager_name' => SafeContent::class,
        'special_instructions' => SafeContent::class,
        'is_featured' => 'bool',
        'is_pickup_available' => 'bool',
        'order' => 'int',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];



    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class)->withDefault();
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class)->withDefault();
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class)->withDefault();
    }

    protected function fullAddress(): Attribute
    {
        return Attribute::get(function () {
            $address = $this->address;

            if ($this->city->name) {
                $address .= ', ' . $this->city->name;
            }

            if ($this->state->name) {
                $address .= ', ' . $this->state->name;
            }

            if ($this->country->name) {
                $address .= ', ' . $this->country->name;
            }

            if ($this->zip_code) {
                $address .= ' ' . $this->zip_code;
            }

            return $address;
        });
    }



    /**
     * Get operating hours as text
     */
    public function getOperatingHoursText(): string
    {
        return $this->operating_hours ?: '';
    }

    public function scopeActive($query)
    {
        return $query->where('status', BaseStatusEnum::PUBLISHED);
    }

    public function scopePickupAvailable($query)
    {
        return $query->where('is_pickup_available', true);
    }

    public function scopeByCity($query, $cityId)
    {
        return $query->where('city_id', $cityId);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
}
