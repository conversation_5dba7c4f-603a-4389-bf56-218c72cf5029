<?php

namespace Bo<PERSON><PERSON>\BranchManagement\Http\Controllers;

use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\BranchManagement\Forms\BranchForm;
use Botble\BranchManagement\Http\Requests\BranchRequest;
use Bo<PERSON>ble\BranchManagement\Models\Branch;
use Botble\BranchManagement\Tables\BranchTable;
use Exception;
use Illuminate\Http\Request;

class BranchController extends BaseController
{
    public function index(BranchTable $table)
    {
        PageTitle::setTitle(trans('plugins/branch-management::branch.name'));

        return $table->renderTable();
    }

    public function create()
    {
        PageTitle::setTitle(trans('plugins/branch-management::branch.create'));

        return BranchForm::create()->renderForm();
    }

    public function store(BranchRequest $request, BaseHttpResponse $response)
    {
        $form = BranchForm::create()->setRequest($request);
        $form->save();

        event(new CreatedContentEvent(BRANCH_MODULE_SCREEN_NAME, $request, $form->getModel()));

        return $response
            ->setPreviousUrl(route('branches.index'))
            ->setNextUrl(route('branches.edit', $form->getModel()->id))
            ->withCreatedSuccessMessage();
    }

    public function show(Branch $branch)
    {
        PageTitle::setTitle($branch->name);

        return view('plugins/branch-management::branches.show', compact('branch'));
    }

    public function edit(Branch $branch)
    {
        PageTitle::setTitle(trans('plugins/branch-management::branch.edit', ['name' => $branch->name]));

        return BranchForm::createFromModel($branch)->renderForm();
    }

    public function update(Branch $branch, BranchRequest $request, BaseHttpResponse $response)
    {
        $form = BranchForm::createFromModel($branch)->setRequest($request);
        $form->save();

        event(new UpdatedContentEvent(BRANCH_MODULE_SCREEN_NAME, $request, $branch));

        return $response
            ->setPreviousUrl(route('branches.index'))
            ->withUpdatedSuccessMessage();
    }

    public function destroy(Branch $branch, Request $request, BaseHttpResponse $response)
    {
        try {
            $branch->delete();

            event(new DeletedContentEvent(BRANCH_MODULE_SCREEN_NAME, $request, $branch));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}
