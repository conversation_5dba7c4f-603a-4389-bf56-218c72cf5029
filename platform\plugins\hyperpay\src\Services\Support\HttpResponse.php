<?php

namespace Shaqi\HyperPay\Services\Support;

use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use GuzzleHttp\Psr7\Response;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class HttpResponse
{
    /**
     * @var Response
     */
    protected $response;

    /**
     * @var Model|null
     */
    protected $transaction;

    /**
     * @var array
     */
    protected $parameters;

    /**
     * @var Model|null
     */
    protected $user;

    /**
     * @var array
     */
    protected $trackableData = [];

    /**
     * @var string
     */
    protected $scriptUrl;

    /**
     * @var string
     */
    protected $shopperResultUrl;

    /**
     * Create a new HTTP response instance.
     *
     * @param  Response  $response
     * @param  Model|null  $transaction
     * @param  array  $parameters
     */
    public function __construct(Response $response, ?Model $transaction = null, array $parameters = [])
    {
        $this->response = $response;
        $this->transaction = $transaction;
        $this->parameters = $parameters;
    }

    /**
     * Set the user for the response.
     *
     * @param  Model|null  $user
     * @return $this
     */
    public function setUser(?Model $user): self
    {
        $this->user = $user;
        return $this;
    }

    /**
     * Set trackable data.
     *
     * @param  array  $data
     * @return $this
     */
    public function setTrackableData(array $data): self
    {
        $this->trackableData = $data;
        return $this;
    }

    /**
     * Add script URL.
     *
     * @param  string  $gatewayUrl
     * @return $this
     */
    public function addScriptUrl(string $gatewayUrl): self
    {
        $this->scriptUrl = $gatewayUrl;
        return $this;
    }

    /**
     * Add shopper result URL.
     *
     * @param  string  $url
     * @return $this
     */
    public function addShopperResultUrl(string $url): self
    {
        $this->shopperResultUrl = $url;
        return $this;
    }

    /**
     * Prepare checkout response.
     *
     * @return array
     */
    public function prepareCheckout(): array
    {
        $responseData = json_decode($this->response->getBody()->getContents(), true);

        Log::info('HyperPay HTTP Response - Prepare Checkout', [
            'status_code' => $this->response->getStatusCode(),
            'response_data' => $responseData
        ]);

        if (!$responseData || !isset($responseData['result']['code'])) {
            return [
                'error' => true,
                'message' => 'Invalid response from HyperPay API',
                'response' => $responseData
            ];
        }

        $resultCode = $responseData['result']['code'];
        $resultDescription = $responseData['result']['description'] ?? 'Unknown error';

        // Check if checkout creation was successful
        if ($resultCode === '000.200.100') {
            $checkoutId = $responseData['id'] ?? null;

            if (!$checkoutId) {
                return [
                    'error' => true,
                    'message' => 'Checkout ID not found in response',
                    'response' => $responseData
                ];
            }

            // Store session data for later use
            session([
                'hyperpay_checkout_id' => $checkoutId,
                'hyperpay_order_id' => $this->trackableData['order_id'] ?? null,
                'hyperpay_amount' => $this->trackableData['amount'] ?? 0,
                'hyperpay_currency' => $this->parameters['currency'] ?? 'SAR',
                'hyperpay_customer_id' => $this->trackableData['customer_id'] ?? null,
                'hyperpay_customer_type' => $this->trackableData['customer_type'] ?? null,
                'hyperpay_payment_type' => $this->trackableData['payment_type'] ?? 'visa',
            ]);

            return [
                'success' => true,
                'checkout_id' => $checkoutId,
                'script_url' => $this->scriptUrl,
                'shopper_result_url' => $this->shopperResultUrl,
                'response' => $responseData
            ];
        }

        return [
            'error' => true,
            'message' => "Checkout creation failed: {$resultDescription} (Code: {$resultCode})",
            'response' => $responseData
        ];
    }

    /**
     * Process payment status response.
     *
     * @return array
     */
    public function paymentStatus(): array
    {
        $responseData = json_decode($this->response->getBody()->getContents(), true);

        Log::info('HyperPay HTTP Response - Payment Status', [
            'status_code' => $this->response->getStatusCode(),
            'response_data' => $responseData
        ]);

        if (!$responseData || !isset($responseData['result']['code'])) {
            return [
                'error' => true,
                'message' => 'Invalid payment status response',
                'response' => $responseData
            ];
        }

        $resultCode = $responseData['result']['code'];
        $resultDescription = $responseData['result']['description'] ?? 'Unknown status';
        $status = PaymentStatusEnum::FAILED;

        // Determine payment status based on result code
        if ($this->isSuccessfulPayment($resultCode)) {
            $status = PaymentStatusEnum::COMPLETED;
        } elseif ($this->isPendingPayment($resultCode)) {
            $status = PaymentStatusEnum::PENDING;
        }

        return [
            'success' => $status === PaymentStatusEnum::COMPLETED,
            'status' => $status,
            'result_code' => $resultCode,
            'result_description' => $resultDescription,
            'transaction_id' => $responseData['id'] ?? null,
            'response' => $responseData
        ];
    }

    /**
     * Process recurring payment response.
     *
     * @return array
     */
    public function recurringPayment(): array
    {
        $responseData = json_decode($this->response->getBody()->getContents(), true);

        Log::info('HyperPay HTTP Response - Recurring Payment', [
            'status_code' => $this->response->getStatusCode(),
            'response_data' => $responseData
        ]);

        if (!$responseData || !isset($responseData['result']['code'])) {
            return [
                'error' => true,
                'message' => 'Invalid recurring payment response',
                'response' => $responseData
            ];
        }

        $resultCode = $responseData['result']['code'];
        $resultDescription = $responseData['result']['description'] ?? 'Unknown status';
        $status = PaymentStatusEnum::FAILED;

        if ($this->isSuccessfulPayment($resultCode)) {
            $status = PaymentStatusEnum::COMPLETED;
        } elseif ($this->isPendingPayment($resultCode)) {
            $status = PaymentStatusEnum::PENDING;
        }

        return [
            'success' => $status === PaymentStatusEnum::COMPLETED,
            'status' => $status,
            'result_code' => $resultCode,
            'result_description' => $resultDescription,
            'payment_id' => $responseData['id'] ?? null,
            'registration_id' => $responseData['registrationId'] ?? null,
            'response' => $responseData
        ];
    }

    /**
     * Check if payment result code indicates success.
     *
     * @param  string  $resultCode
     * @return bool
     */
    protected function isSuccessfulPayment(string $resultCode): bool
    {
        // Success patterns from HyperPay documentation
        $successPatterns = [
            '/^(000\.000\.|000\.100\.1|000\.[36])/',  // Successful payments
        ];

        foreach ($successPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if payment result code indicates pending status.
     *
     * @param  string  $resultCode
     * @return bool
     */
    protected function isPendingPayment(string $resultCode): bool
    {
        // Pending patterns from HyperPay documentation
        $pendingPatterns = [
            '/^(000\.200)/',                          // Pending payments
            '/^(800\.400\.5|100\.400\.500)/',        // Pending v2 payments
        ];

        foreach ($pendingPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the raw response data.
     *
     * @return array
     */
    public function getRawResponse(): array
    {
        return json_decode($this->response->getBody()->getContents(), true) ?? [];
    }

    /**
     * Get the HTTP status code.
     *
     * @return int
     */
    public function getStatusCode(): int
    {
        return $this->response->getStatusCode();
    }
}
