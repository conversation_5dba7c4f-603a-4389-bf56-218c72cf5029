<?php

namespace Shaqi\Aramex\API\Classes;

use Shaqi\Aramex\API\Interfaces\Normalize;

/**
 *
 * Class MultiRateDetails
 * @package Shaqi\Aramex\API\Classes
 */
class MultiRateDetails implements Normalize
{
    private ?Dimension $dimensions;
    private Weight $actualWeight;
    private Weight $chargeableWeight;
    private string $descriptionOfGoods;
    private string $goodsOriginCountry;
    private int $numberOfPieces;
    private string $productGroup;
    private array $productTypes;
    private string $paymentType;
    private ?string $paymentOption;
    private ?Money $customsValueAmount;
    private ?Money $cashOnDeliveryAmount;
    private ?Money $insuranceAmount;
    private ?Money $cashAdditionalAmount;
    private ?string $services;
    private array $items;

    public function __construct($actualWeight, $chargeableWeight, $descriptionOfGoods, $goodsOriginCountry, $numberOfPieces, $productGroup, $productTypes, $paymentType, $items)
    {
        $this->actualWeight = $actualWeight;
        $this->chargeableWeight = $chargeableWeight;
        $this->descriptionOfGoods = $descriptionOfGoods;
        $this->goodsOriginCountry = $goodsOriginCountry;
        $this->numberOfPieces = $numberOfPieces;
        $this->productGroup = $productGroup;
        $this->productTypes = $productTypes;
        $this->paymentType = $paymentType;
        $this->items = $items;
        $this->dimensions = null;
        $this->paymentOption = null;
        $this->customsValueAmount = null;
        $this->cashOnDeliveryAmount = null;
        $this->insuranceAmount = null;
        $this->cashAdditionalAmount = null;
        $this->services = null;
    }

    /**
     * @return Dimension|null
     */
    public function getDimensions(): ?Dimension
    {
        return $this->dimensions;
    }

    /**
     * Measurements required in calculating the Chargeable Weight, If any of the dimensional values are filled then the rest must be filled.
     * @param Dimension $dimensions
     * @return MultiRateDetails
     */
    public function setDimensions(Dimension $dimensions): MultiRateDetails
    {
        $this->dimensions = $dimensions;
        return $this;
    }

    /**
     * @return Weight
     */
    public function getActualWeight(): Weight
    {
        return $this->actualWeight;
    }

    /**
     * @return Weight
     */
    public function getChargeableWeight(): Weight
    {
        return $this->chargeableWeight;
    }

    /**
     * Total actual shipment weight. If the Dimensions are filled, charging weight is compared to actual and the highest value is filled here.
     * @param Weight $actualWeight
     * @return MultiRateDetails
     */
    public function setActualWeight(Weight $actualWeight): MultiRateDetails
    {
        $this->actualWeight = $actualWeight;
        return $this;
    }

    /**
     * Total actual shipment weight. If the Dimensions are filled, charging weight is compared to actual and the highest value is filled here.
     * @param Weight $actualWeight
     * @return MultiRateDetails
     */
    public function setChargeableWeight(Weight $chargeableWeight): MultiRateDetails
    {
        $this->chargeableWeight = $chargeableWeight;
        return $this;
    }

    /**
     * @return string
     */
    public function getDescriptionOfGoods(): string
    {
        return $this->descriptionOfGoods;
    }

    /**
     * The Nature of Shipment Contents. Example: Clothes, Electronic
     * @param string $descriptionOfGoods
     * @return MultiRateDetails
     */
    public function setDescriptionOfGoods(string $descriptionOfGoods): MultiRateDetails
    {
        $this->descriptionOfGoods = $descriptionOfGoods;
        return $this;
    }

    /**
     * @return string
     */
    public function getGoodsOriginCountry(): string
    {
        return $this->goodsOriginCountry;
    }

    /**
     * The Origin of which the product in the shipment came from
     * @param string $goodsOriginCountry
     * @return MultiRateDetails
     */
    public function setGoodsOriginCountry(string $goodsOriginCountry): MultiRateDetails
    {
        $this->goodsOriginCountry = $goodsOriginCountry;
        return $this;
    }

    /**
     * @return int
     */
    public function getNumberOfPieces(): int
    {
        return $this->numberOfPieces;
    }

    /**
     * Number of shipment pieces
     * @param int $numberOfPieces
     * @return MultiRateDetails
     */
    public function setNumberOfPieces(int $numberOfPieces): MultiRateDetails
    {
        $this->numberOfPieces = $numberOfPieces;
        return $this;
    }

    /**
     * @return string
     */
    public function getProductGroup(): string
    {
        return $this->productGroup;
    }

    /**
     * EXP = Express DOM = Domestic
     * @param string $productGroup
     * @return MultiRateDetails
     */
    public function setProductGroup(string $productGroup): MultiRateDetails
    {
        $this->productGroup = $productGroup;
        return $this;
    }

    /**
     * @return MultiRateDetails
     */
    public function useExpressAsProductGroup(): MultiRateDetails
    {
        return $this->setProductGroup('EXP');

    }

    /**
     * @return MultiRateDetails
     */
    public function useDomesticAsProductGroup(): MultiRateDetails
    {
        return $this->setProductGroup('DOM');
    }

    /**
     * @return array
     */
    public function getProductTypes(): array
    {
        return $this->productTypes;
    }


    /**
     * Product Type involves the specification of certain features concerning the delivery of the product
     * @param array $productType
     * @return MultiRateDetails
     */
    public function setProductTypes(array $productTypes): MultiRateDetails
    {
        $this->productTypes = $productTypes;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPaymentOption(): ?string
    {
        return $this->paymentOption;
    }

    /**
     * @param string $paymentOption
     */
    public function setPaymentOption(string $paymentOption): void
    {
        $this->paymentOption = $paymentOption;
    }

    /**
     * @return Money|null
     */
    public function getCustomsValueAmount(): ?Money
    {
        return $this->customsValueAmount;
    }

    /**
     * @param Money $customsValueAmount
     * @return MultiRateDetails
     */
    public function setCustomsValueAmount(Money $customsValueAmount): MultiRateDetails
    {
        $this->customsValueAmount = $customsValueAmount;
        return $this;
    }

    /**
     * @return Money|null
     */
    public function getCashOnDeliveryAmount(): ?Money
    {
        return $this->cashOnDeliveryAmount;
    }

    /**
     * Amount of Cash that is paid by the receiver of the package. Conditional - Based on the Services "COD" being filled.
     * @param Money $cashOnDeliveryAmount
     * @return MultiRateDetails
     */
    public function setCashOnDeliveryAmount(Money $cashOnDeliveryAmount): MultiRateDetails
    {
        $this->cashOnDeliveryAmount = $cashOnDeliveryAmount;
        return $this;
    }

    /**
     * @return Money|null
     */
    public function getInsuranceAmount(): ?Money
    {
        return $this->insuranceAmount;
    }

    /**
     * Insurance Amount charged on shipment.
     * @param Money $insuranceAmount
     * @return MultiRateDetails
     */
    public function setInsuranceAmount(Money $insuranceAmount): MultiRateDetails
    {
        $this->insuranceAmount = $insuranceAmount;
        return $this;
    }

    /**
     * @return Money|null
     */
    public function getCashAdditionalAmount(): ?Money
    {
        return $this->cashAdditionalAmount;
    }

    /**
     * Additional Cash that can be required for miscellaneous purposes.
     * @param Money $cashAdditionalAmount
     * @return MultiRateDetails
     */
    public function setCashAdditionalAmount(Money $cashAdditionalAmount): MultiRateDetails
    {
        $this->cashAdditionalAmount = $cashAdditionalAmount;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getServices(): ?string
    {
        return $this->services;
    }

    /**
     * Additional Services used in shipping the package, Separate by comma when selecting multiple services.
     * @param string $services
     * @return MultiRateDetails
     */
    public function setServices(string $services): MultiRateDetails
    {
        $this->services = $services;
        return $this;
    }

    /**
     * Cash on Delivery.
     * Receiver pays the cost of the goods
     * @return MultiRateDetails
     */
    public function useCashOnDeliveryService(): MultiRateDetails
    {
        return $this->setServices('COD');
    }

    /**
     * First Delivery.
     * Committed delivery time at destination country.
     * @return $this
     */
    public function useFirstAsService(): MultiRateDetails
    {
        return $this->setServices('FIRST');
    }

    public function useFreeDomicileAsService(): MultiRateDetails
    {
        return $this->setServices('FRDOM');
    }

    public function useHoldForPickupAsService(): MultiRateDetails
    {
        return $this->setServices('HFPU');
    }

    public function useNoonDeliveryAsService(): MultiRateDetails
    {
        return $this->setServices('NOON');
    }

    public function useSignatureRequiredAsService(): MultiRateDetails
    {
        return $this->setServices('SIG');
    }

    /**
     * @return ShipmentItem[]
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * Details of the Items within a shipment. Several items can be added for a single shipment.
     * @param $items
     * @return MultiRateDetails
     */
    public function setItems($items): MultiRateDetails
    {
        $this->items = $items;
        return $this;
    }

    /**
     * @param ShipmentItem $item
     * @return MultiRateDetails
     */
    public function addItem(ShipmentItem $item): MultiRateDetails
    {
        $this->items[] = $item;
        return $this;
    }

    /**
     * @return string
     */
    public function getPaymentType(): string
    {
        return $this->paymentType;
    }

    /**
     * Method of payment for shipment.
     * @param string $paymentType
     * @return MultiRateDetails
     */
    public function setPaymentType(string $paymentType): MultiRateDetails
    {
        $this->paymentType = $paymentType;
        return $this;
    }

    /**
     * Prepaid.
     * Transportation Charges payable by shipper.
     * @return MultiRateDetails
     */
    public function usePrepaidAsPaymentType(): MultiRateDetails
    {
        return $this->setPaymentType('P');
    }

    /**
     * Collect.
     * Transportation Charges payable by consignee.
     * @return MultiRateDetails
     */
    public function useCollectAsPaymentType(): MultiRateDetails
    {
        return $this->setPaymentType('C');
    }

    /**
     * Third Party.
     * Transportation Charges payable by third party. Note: in case of 3rd Party all third party details must be filled including a valid Aramex Account Number for Billing Party.
     * @return MultiRateDetails
     */
    public function useThirdPartyAsPaymentType(): MultiRateDetails
    {
        return $this->setPaymentType('T');
    }

    public function normalize(): array
    {
        return [
            'Dimensions' => optional($this->getDimensions())->normalize(),
            'ActualWeight' => optional($this->getActualWeight())->normalize(),
            'ChargeableWeight' => optional($this->getChargeableWeight())->normalize(),
            'DescriptionOfGoods' => $this->getDescriptionOfGoods(),
            'GoodsOriginCountry' => $this->getGoodsOriginCountry(),
            'NumberOfPieces' => $this->getNumberOfPieces(),
            'ProductGroup' => $this->getProductGroup(),
            'ProductTypes' => $this->getProductTypes(),
            'PaymentType' => $this->getPaymentType(),
            'PaymentOption' => $this->getPaymentOption(),
            'CustomsValueAmount' => optional($this->getCustomsValueAmount())->normalize(),
            'CashOnDeliveryAmount' => optional($this->getCashOnDeliveryAmount())->normalize(),
            'InsuranceAmount' => optional($this->getInsuranceAmount())->normalize(),
            'CashAdditionalAmount' => optional($this->getCashAdditionalAmount())->normalize(),
            'CashAdditionalAmountDescription'=> null,
            'CollectAmount'=> null,
            'Services'=> null,
            'Services' => $this->getServices(),
            'Items' => $this->getItems() ? array_map(function ($item) {
                return $item->normalize();
            }, $this->getItems()) : [],
            'DeliveryInstructions'=> null,
            'AdditionalProperties'=>null,
            'ContainsDangerousGoods'=>null
        ];
    }
}