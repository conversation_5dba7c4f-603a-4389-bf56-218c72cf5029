<div class="branch-pickup-email-info" style="background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem; margin: 1rem 0;">
    <h4 style="color: #495057; margin-top: 0; margin-bottom: 0.75rem; font-size: 1.1rem;">
        🏪 {{ trans('plugins/branch-management::branch.pickup.title') }}
    </h4>
    
    <div style="margin-bottom: 0.75rem;">
        <strong style="color: #212529;">{{ trans('plugins/branch-management::branch.fields.name') }}:</strong><br>
        <span style="color: #0d6efd;">{{ $branch->name }}</span>
    </div>
    
    <div style="margin-bottom: 0.75rem;">
        <strong style="color: #212529;">{{ trans('plugins/branch-management::branch.fields.address') }}:</strong><br>
        <span style="color: #6c757d;">{{ $branch->full_address }}</span>
    </div>
    
    @if($branch->phone)
    <div style="margin-bottom: 0.75rem;">
        <strong style="color: #212529;">{{ trans('plugins/branch-management::branch.fields.phone') }}:</strong><br>
        <a href="tel:{{ $branch->phone }}" style="color: #0d6efd; text-decoration: none;">📞 {{ $branch->phone }}</a>
    </div>
    @endif
    
    @if($branch->pickup_fee > 0)
    <div style="margin-bottom: 0.75rem;">
        <strong style="color: #212529;">{{ trans('plugins/branch-management::branch.pickup.pickup_fee') }}:</strong><br>
        <span style="background-color: #d1ecf1; color: #0c5460; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
            {{ format_price($branch->pickup_fee) }}
        </span>
    </div>
    @endif
    
    @if($branch->manager_name || $branch->manager_phone)
    <div style="margin-bottom: 0.75rem;">
        <strong style="color: #212529;">{{ trans('plugins/branch-management::branch.manager_information') }}:</strong><br>
        @if($branch->manager_name)
            <span style="color: #6c757d;">{{ $branch->manager_name }}</span>
            @if($branch->manager_phone) - @endif
        @endif
        @if($branch->manager_phone)
            <a href="tel:{{ $branch->manager_phone }}" style="color: #0d6efd; text-decoration: none;">{{ $branch->manager_phone }}</a>
        @endif
    </div>
    @endif
    
    @if($branch->special_instructions)
    <div style="margin-bottom: 0.75rem;">
        <strong style="color: #212529;">{{ trans('plugins/branch-management::branch.special_instructions') }}:</strong><br>
        <span style="color: #6c757d; font-size: 0.875rem;">{{ $branch->special_instructions }}</span>
    </div>
    @endif
    
    <div style="margin-top: 1rem; padding-top: 0.75rem; border-top: 1px solid #dee2e6;">
        <small style="color: #6c757d;">
            ℹ️ {{ trans('plugins/branch-management::branch.pickup.email_note', ['branch_name' => $branch->name]) }}
        </small>
    </div>
</div>
