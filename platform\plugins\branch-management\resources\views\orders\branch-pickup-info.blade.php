@if($order->shippingAddress && $order->shippingAddress->is_branch_pickup && $order->shippingAddress->branch)
<x-core::card class="mb-3">
    <x-core::card.header>
        <x-core::card.title>
            <div class="d-flex align-items-center">
                <x-core::icon name="ti ti-building-store" class="me-2 text-primary" />
                {{ trans('plugins/branch-management::branch.pickup.title') }} {{ trans('plugins/branch-management::branch.information') }}
            </div>
        </x-core::card.title>
    </x-core::card.header>
    <x-core::card.body>
        <div class="alert alert-info mb-3">
            <div class="d-flex align-items-center">
                <x-core::icon name="ti ti-info-circle" class="me-2" />
                <strong>{{ trans('plugins/branch-management::branch.pickup.order_notice') }}</strong>
            </div>
        </div>

        <x-core::datagrid>
            <x-core::datagrid.item>
                <x-slot:title>
                    {{ trans('plugins/branch-management::branch.name') }}
                </x-slot:title>
                <div class="d-flex align-items-center">
                    <x-core::icon name="ti ti-building" class="me-2 text-muted" />
                    <strong class="text-primary">{{ $order->shippingAddress->branch->name }}</strong>
                </div>
            </x-core::datagrid.item>

            <x-core::datagrid.item>
                <x-slot:title>
                    {{ trans('plugins/branch-management::branch.address') }}
                </x-slot:title>
                <div class="d-flex align-items-center">
                    <x-core::icon name="ti ti-map-pin" class="me-2 text-muted" />
                    <span>{{ $order->shippingAddress->branch->full_address }}</span>
                </div>
                @if($order->shippingAddress->branch->full_address)
                    <div class="mt-2">
                        <a href="https://maps.google.com/?q={{ urlencode($order->shippingAddress->branch->full_address) }}"
                           target="_blank"
                           class="btn btn-sm btn-outline-primary">
                            <x-core::icon name="ti ti-map" class="me-1" />
                            {{ trans('plugins/ecommerce::order.see_on_maps') }}
                        </a>
                    </div>
                @endif
            </x-core::datagrid.item>

            @if($order->shippingAddress->branch->phone)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.phone') }}
                    </x-slot:title>
                    <div class="d-flex align-items-center">
                        <x-core::icon name="ti ti-phone" class="me-2 text-muted" />
                        <a href="tel:{{ $order->shippingAddress->branch->phone }}" class="text-success text-decoration-none">
                            {{ $order->shippingAddress->branch->phone }}
                        </a>
                    </div>
                </x-core::datagrid.item>
            @endif

            @if($order->shippingAddress->branch->email)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.email') }}
                    </x-slot:title>
                    <div class="d-flex align-items-center">
                        <x-core::icon name="ti ti-mail" class="me-2 text-muted" />
                        <a href="mailto:{{ $order->shippingAddress->branch->email }}" class="text-primary text-decoration-none">
                            {{ $order->shippingAddress->branch->email }}
                        </a>
                    </div>
                </x-core::datagrid.item>
            @endif

            @if($order->shippingAddress->branch->pickup_fee > 0)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.pickup_fee') }}
                    </x-slot:title>
                    <div class="d-flex align-items-center">
                        <x-core::icon name="ti ti-currency-dollar" class="me-2 text-muted" />
                        <span class="badge bg-info">{{ format_price($order->shippingAddress->branch->pickup_fee) }}</span>
                    </div>
                </x-core::datagrid.item>
            @endif

            @if($order->shippingAddress->branch->manager_name)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.manager_name') }}
                    </x-slot:title>
                    <div class="d-flex align-items-center">
                        <x-core::icon name="ti ti-user" class="me-2 text-muted" />
                        <span>{{ $order->shippingAddress->branch->manager_name }}</span>
                        @if($order->shippingAddress->branch->manager_phone)
                            <span class="text-muted ms-2">
                                (<a href="tel:{{ $order->shippingAddress->branch->manager_phone }}" class="text-decoration-none">{{ $order->shippingAddress->branch->manager_phone }}</a>)
                            </span>
                        @endif
                    </div>
                </x-core::datagrid.item>
            @endif


            @if($order->shippingAddress->branch->operating_hours)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.operating_hours') }}
                    </x-slot:title>
                    <div class="operating-hours">
                         {!! nl2br(e($order->shippingAddress->branch->operating_hours)) !!}
                    </div>
                </x-core::datagrid.item>
            @endif

            @if($order->shippingAddress->branch->special_instructions && is_string($order->shippingAddress->branch->special_instructions))
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.special_instructions') }}
                    </x-slot:title>
                    <div class="alert alert-warning mb-0">
                        <x-core::icon name="ti ti-alert-triangle" class="me-2" />
                        {!! BaseHelper::clean($order->shippingAddress->branch->special_instructions) !!}
                    </div>
                </x-core::datagrid.item>
            @endif
        </x-core::datagrid>
    </x-core::card.body>
</x-core::card>
@endif
