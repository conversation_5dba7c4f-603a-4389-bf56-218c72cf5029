/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/*!***************************************************************!*\
  !*** ./platform/plugins/aramex/resources/assets/js/aramex.js ***!
  \***************************************************************/


var Aramex = Aramex || {};
Aramex.init = function () {
  $(document).on('show.bs.modal', '#aramex-view-n-create-pickup', function (e) {
    var $self = $(e.currentTarget);
    var $related = $(e.relatedTarget);
    $self.find('.modal-body').html('');
    $.ajax({
      type: 'GET',
      url: $related.data('url'),
      beforeSend: function beforeSend() {
        $related.addClass('button-loading');
      },
      success: function success(res) {
        if (res.error) {
          Botble.showError(res.message);
        } else {
          $self.find('.modal-body').html(res.data.html);
        }
      },
      error: function error(res) {
        Botble.handleError(res);
      },
      complete: function complete() {
        $related.removeClass('button-loading');
      }
    });
  });
  $(document).on('click', '#aramex-view-n-create-pickup .create-pickup', function (e) {
    var $self = $(e.currentTarget);
    $.ajax({
      type: 'POST',
      url: $self.data('url'),
      beforeSend: function beforeSend() {
        $self.addClass('button-loading');
      },
      success: function success(res) {
        if (res.error) {
          Botble.showError(res.message);
        } else {
          $('[data-bs-target="#aramex-view-n-create-pickup"]').addClass('d-none');
          $('#aramex-view-n-create-pickup').modal('hide');
          Botble.showSuccess(res.message);
        }
      },
      error: function error(res) {
        Botble.handleError(res);
      },
      complete: function complete() {
        $self.removeClass('button-loading');
      }
    });
  });
  $(document).on('show.bs.modal', '#aramex-create-shipment', function (e) {
    var $self = $(e.currentTarget);
    var $related = $(e.relatedTarget);
    $self.find('.modal-body').html('');
    $.ajax({
      type: 'GET',
      url: $related.data('url'),
      beforeSend: function beforeSend() {
        $related.addClass('button-loading');
      },
      success: function success(res) {
        if (res.error) {
          Botble.showError(res.message);
        } else {
          $self.find('.modal-body').html(res.data.html);
        }
      },
      error: function error(res) {
        Botble.handleError(res);
      },
      complete: function complete() {
        $related.removeClass('button-loading');
      }
    });
  });
  $(document).on('click', '#aramex-create-shipment .create-shipment', function (e) {
    var $self = $(e.currentTarget);
    $.ajax({
      type: 'POST',
      url: $self.data('url'),
      beforeSend: function beforeSend() {
        $self.addClass('button-loading');
      },
      success: function success(res) {
        if (res.error) {
          Botble.showError(res.message);
        } else {
          $('[data-bs-target="#aramex-create-shipment"]').addClass('d-none');
          $('#aramex-create-shipment').modal('hide');
          Botble.showSuccess(res.message);
        }
      },
      error: function error(res) {
        Botble.handleError(res);
      },
      complete: function complete() {
        $self.removeClass('button-loading');
      }
    });
  });
};
$(function () {
  Aramex.init();
});
/******/ })()
;