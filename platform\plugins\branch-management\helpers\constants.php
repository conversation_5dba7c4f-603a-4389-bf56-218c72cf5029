<?php

if (!defined('BRANCH_MANAGEMENT_MODULE_SCREEN_NAME')) {
    define('BRANCH_MANAGEMENT_MODULE_SCREEN_NAME', 'branch-management');
}

if (!defined('BRANCH_MODULE_SCREEN_NAME')) {
    define('BRANCH_MODULE_SCREEN_NAME', 'branch');
}

// Action hooks
if (!defined('BRANCH_MANAGEMENT_ACTION_AFTER_CREATE')) {
    define('BRANCH_MANAGEMENT_ACTION_AFTER_CREATE', 'branch_management_after_create');
}

if (!defined('BRANCH_MANAGEMENT_ACTION_AFTER_UPDATE')) {
    define('BRANCH_MANAGEMENT_ACTION_AFTER_UPDATE', 'branch_management_after_update');
}

if (!defined('BRANCH_MANAGEMENT_ACTION_AFTER_DELETE')) {
    define('BRANCH_MANAGEMENT_ACTION_AFTER_DELETE', 'branch_management_after_delete');
}

// Filter hooks
if (!defined('BRANCH_MANAGEMENT_FILTER_CHECKOUT_OPTIONS')) {
    define('BRANCH_MANAGEMENT_FILTER_CHECKOUT_OPTIONS', 'branch_management_checkout_options');
}

if (!defined('BRANCH_MANAGEMENT_FILTER_SHIPPING_METHODS')) {
    define('BRANCH_MANAGEMENT_FILTER_SHIPPING_METHODS', 'branch_management_shipping_methods');
}

// Shipping method constant
if (!defined('BRANCH_PICKUP_SHIPPING_METHOD_NAME')) {
    define('BRANCH_PICKUP_SHIPPING_METHOD_NAME', 'branch_pickup');
}
