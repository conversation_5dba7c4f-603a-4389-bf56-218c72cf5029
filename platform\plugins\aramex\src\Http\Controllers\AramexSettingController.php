<?php

namespace Shaqi\Aramex\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Ecommerce\Services\HandleShippingFeeService;
use Bo<PERSON><PERSON>\Setting\Supports\SettingStore;
use Bo<PERSON>ble\Setting\Http\Controllers\SettingController;
use Shaqi\Aramex\Aramex;
use Botble\Support\Services\Cache\Cache;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Shaqi\Aramex\Http\Requests\Settings\AramexSettingRequest;

class AramexSettingController extends SettingController
{
    public function update(AramexSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate($request->validated()); 
    }
}
