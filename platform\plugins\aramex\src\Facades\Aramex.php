<?php

namespace Shaqi\Aramex\Facades;

use Shaqi\Aramex\API\Requests\Location\FetchCities;
use <PERSON><PERSON>qi\Aramex\API\Requests\Location\FetchCountries;
use Shaqi\Aramex\API\Requests\Location\FetchCountry;
use Shaqi\Aramex\API\Requests\Location\FetchDropOffLocations;
use Shaqi\Aramex\API\Requests\Location\FetchOffices;
use Shaqi\Aramex\API\Requests\Location\FetchStates;
use Shaqi\Aramex\API\Requests\Location\ValidateAddress;
use Shaqi\Aramex\API\Requests\Rate\CalculateRate;
use Shaqi\Aramex\API\Requests\Shipping\CancelPickup;
use Shaqi\Aramex\API\Requests\Shipping\CreatePickup;
use Shaqi\Aramex\API\Requests\Shipping\CreateShipments;
use Shaqi\Aramex\API\Requests\Shipping\GetLastShipmentsNumbersRange;
use <PERSON><PERSON>qi\Aramex\API\Requests\Shipping\PrintLabel;
use Shaqi\Aramex\API\Requests\Shipping\ReserveShipmentNumberRange;
use Shaqi\Aramex\API\Requests\Shipping\ScheduleDelivery;
use S<PERSON><PERSON>\Aramex\API\Requests\Tracking\TrackPickup;
use Shaqi\Aramex\API\Requests\Tracking\TrackShipments;
use Shaqi\Aramex\Aramex as AramexClass;
use Illuminate\Support\Facades\Facade;

/**
 * Class Aramex
 * @package Shaqi\Aramex
 *
 * @method static FetchCities fetchCities
 * @method static FetchCountries fetchCountries
 * @method static FetchCountry fetchCountry
 * @method static FetchDropOffLocations fetchDropOffLocations
 * @method static FetchOffices fetchOffices
 * @method static FetchStates fetchStates
 * @method static ValidateAddress validateAddress
 * @method static CalculateRate calculateRate
 * @method static CancelPickup cancelPickup
 * @method static CreatePickup createPickup
 * @method static CreateShipments createShipments
 * @method static GetLastShipmentsNumbersRange getLastShipmentsNumbersRange
 * @method static PrintLabel printLabel
 * @method static ReserveShipmentNumberRange reserveShipmentNumberRange
 * @method static ScheduleDelivery scheduleDelivery
 * @method static TrackPickup trackPickup
 * @method static TrackShipments trackShipments
 */
class Aramex extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return AramexClass::class;
    }
}