/**
 * Branch Management - Styles
 */

.product-branches-section {
    .city-selector {
        margin-bottom: 1rem;
    }

    .branch-selector {
        margin-bottom: 1rem;
    }

    .branch-item {
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        &.opacity-75 {
            opacity: 0.75;
        }
    }



    .branch-status {
        .badge {
            font-size: 0.8rem;
        }
    }
}

.branch-pickup-section {
    .branch-details {
        .card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
    }
}

// Loading spinner
.fa-spinner.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(359deg);
    }
}
