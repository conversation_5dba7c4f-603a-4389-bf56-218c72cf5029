<?php

namespace Shaqi\Aramex;

use Botble\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Location\Facades\Location;
use Botble\Location\Models\City;
use Botble\Location\Models\Country;
use Botble\Location\Models\State;
use Exception;
use Illuminate\Support\Arr;
use Shaqi\Aramex\API\Classes\Address;
use Shaqi\Aramex\API\Classes\Dimension;
use Shaqi\Aramex\API\Classes\Money;
use Shaqi\Aramex\API\Classes\MultiRateDetails;
use Shaqi\Aramex\API\Classes\ShipmentDetails;
use Shaqi\Aramex\API\Classes\ShipmentItem;
use Shaqi\Aramex\API\Classes\Weight;
use Shaqi\Aramex\Aramex;
use Throwable;

class AramexHelper
{

    public static function convertToKg($value, $unit)
    {
        switch ($unit) {
            case 'g':
                return $value * 0.001;
            case 'kg':
                return $value;
            case 'lb':
                return $value * 0.453592;
            case 'oz':
                return $value * 0.0283495;
            default:
                throw new Exception("Unsupported unit: $unit");
        }
    }


    public static function afterPrepareAddress(array $addr): array
    {
        if (EcommerceHelper::loadCountriesStatesCitiesFromPluginLocation()) {
            $cityId = $addr['city'];
            if (!EcommerceHelper::useCityFieldAsTextField()) {
                $city = Location::getCityById($cityId);
                if ($city) {
                    $addr['city'] = $city->name;
                    $addr['state'] = $city->state->abbreviation ?: $city->state->name;
                    $addr['country'] = $city->state->country->code;
                }
            } else {
                $state = State::query()->find($addr['state']);

                if ($state) {
                    $addr['state'] = $state->abbreviation ?: $state->name;
                    $addr['country'] = $state->country->code;
                }
            }
        }

        return $addr;
    }

    public static function prepareCountryCode($country)
    {
        if (EcommerceHelper::loadCountriesStatesCitiesFromPluginLocation()) {
            $country_object = Country::query()->find($country);
            if ($country_object) {
                $country = $country_object->code;
                return $country;
            }
        }
        return $country;
    }

    public static function checkIsCountryAllowed($data)
    {
        if (isset($data['address_to']['country'])) {
            $country = self::prepareCountryCode($data['address_to']['country']);
            $allowedCountries = json_decode(setting('shipping_aramex_available_countries') ?: '[]');
            return in_array($country, $allowedCountries);
        }
    }

    public static function getProductType($productGroup)
    {
        if ($productGroup == 'DOM') {
            $productType = json_decode(setting('shipping_aramex_allowed_domestic_method') ?: "['ONP']");
        }
        if ($productGroup == 'EXP') {
            $productType = json_decode(setting('shipping_aramex_allowed_international_method') ?: "['EPX']");
        }

        return $productType;
    }

    public static function cleanString($string)
    {
        // Remove special characters
        $cleaned = preg_replace('/[^\w\s]/u', '', $string);

        // Convert accented characters to their non-accented equivalents
        $cleaned = iconv('UTF-8', 'ASCII//TRANSLIT', $cleaned);

        // Remove any remaining non-alphanumeric characters
        $cleaned = preg_replace('/[^A-Za-z0-9\s]/', '', $cleaned);

        return $cleaned;
    }

    public static function getAramexRates($data)
    {
        //dd($data);

        $origin_address = self::afterPrepareAddress(EcommerceHelper::getOriginAddress());

        $origin = new Address('', $origin_address['country']);
        $origin->setLine1($origin_address['address']);
        $origin->setCity($origin_address['city']);
        $origin->setStateOrProvinceCode($origin_address['state']);
        $origin->setPostCode($origin_address['zip_code']);
        $origin->setCountryCode($origin_address['country']);



        $destination_address =  self::afterPrepareAddress($data['address_to']);

        $destination = new Address('', $destination_address['country']);
        $destination->setLine1($destination_address['address']);
        $destination->setCity($destination_address['city']);
        $destination->setStateOrProvinceCode($destination_address['state']);
        $destination->setPostCode($destination_address['zip_code']);
        $destination->setCountryCode($destination_address['country']);



        $store_weight_unit = get_ecommerce_setting('store_weight_unit') ?: 'kg';

        $weight =  self::convertToKg(Arr::get($data, 'weight'), $store_weight_unit);


        $items = Arr::get($data, 'items');

        $number_of_pieces = count($items);


        $items_details = [];
        $dimensions_lengths = [];
        $dimensions_widths = [];
        $dimensions_height = 0;
        $dimensions_unit = '';
        $descriptionOfGoods = '';

        foreach ($items as $item) {
            $item_weight_in_kg =  self::convertToKg(Arr::get($item, 'weight'), $store_weight_unit);
            $weight_obj = new Weight($item_weight_in_kg);
            $shipmentItem = new ShipmentItem(Arr::get($item, 'qty'), $weight_obj);
            $shipmentItem->setPackageType('item');
            $shipmentItem->setComments(Arr::get($item, 'name'));
            $shipmentItem->setReference('No barcode');

            $descriptionOfGoods .= trim($item['name'] . ' / ');

            array_push($items_details, $shipmentItem);
            array_push($dimensions_widths, $item['wide'] ?: 0);
            array_push($dimensions_lengths, $item['length'] ?: 0);
            $dimensions_height += $item['height'] ?: 0;
        }
        $descriptionOfGoods = substr($descriptionOfGoods, 0, 65);

        $allowed_methods = array();
        $international_methods = array();
        $domestic_methods = array();

        if ($origin->getCountryCode() == $destination->getCountryCode()) {
            $productGroup = 'DOM';
            $domestic_methods = aramexAllowedDomesticMethods();
            foreach ($domestic_methods as $cod => $title) {
                if (setting('shipping_aramex_allowed_domestic_method') != "") {
                    foreach (json_decode(setting('shipping_aramex_allowed_domestic_method')) as $value) {
                        if ($value == $cod) {
                            $allowed_methods[$cod] = $title;
                        }
                    }
                }
            }
        } else {
            $productGroup = 'EXP';
            $allowed_methods = array();
            $international_methods = aramexAllowedInternationalMethods();
            foreach ($international_methods as $cod => $title) {
                if (setting('shipping_aramex_allowed_international_method') != "") {
                    foreach (json_decode(setting('shipping_aramex_allowed_international_method')) as $value) {
                        if ($value == $cod) {
                            $allowed_methods[$cod] = $title;
                        }
                    }
                }
            }
        }

        $priceArr = array();
        foreach ($allowed_methods as $m_value => $m_title) {
            try {
                $paymentType = 'P';
                $paymentOption = 'CASH';

                $actualWeight = new Weight($weight);
                $chargeableWeight = new Weight($weight);

                $details = new ShipmentDetails($actualWeight, $descriptionOfGoods, $origin_address['country'], $number_of_pieces, $productGroup, $m_value, $paymentType, $items_details);

                $details->setPaymentOption($paymentOption);

                $dimensions_length = max($dimensions_lengths);
                $dimensions_width = max($dimensions_widths);

                $dimension = new Dimension();
                $dimension->useCentimeterAsUnit();
                $dimension->setWidth($dimensions_width);
                $dimension->setLength($dimensions_length);
                $dimension->setHeight($dimensions_height);

                $details->setDimensions($dimension);

                // dd($details);

                $rateCalculatorResponse = Aramex::calculateRate()
                    ->setOriginalAddress($origin)
                    ->setDestinationAddress($destination)
                    ->setShipmentDetails($details)
                    ->setPreferredCurrencyCode(get_application_currency()->title)
                    ->run();

                if ($rateCalculatorResponse->isSuccessful()) {
                    $totalAmount = $rateCalculatorResponse->getTotalAmount();
                    $priceArr[$m_value] = array(
                        'name' => 'Aramex - ' . $m_title,
                        'price' => $totalAmount->getValue(),
                        'currency' => $totalAmount->getCurrencyCode()
                    );
                } else {
                    if ($rateCalculatorResponse->isFail()) {
                        foreach ($rateCalculatorResponse->getNotificationMessages() as $message) {
                            $priceArr[$m_value] = array(
                                'name' => ('Aramex: ' . $message['code'] . ' - ' . $message['message']) . ' ',
                                'price' => 0,
                            );
                        }
                    }
                }
            } catch (Throwable $th) {
                // $errors[] = $th->getMessage();
                $resp['status'] = false;
                $priceArr[$m_value] = $th->getMessage();
            }
        }

        return $priceArr;
    }
}
