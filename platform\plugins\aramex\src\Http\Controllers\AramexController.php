<?php

namespace Shaqi\Aramex\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Ecommerce\Enums\OrderHistoryActionEnum;
use Bo<PERSON>ble\Ecommerce\Enums\ShippingStatusEnum;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Models\Order;
use Botble\Ecommerce\Models\OrderHistory;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\Shipment;
use Botble\Ecommerce\Repositories\Interfaces\ShipmentHistoryInterface;
use Botble\Ecommerce\Repositories\Interfaces\ShipmentInterface;
use Botble\Payment\Enums\PaymentMethodEnum;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Shaqi\Aramex\API\Classes\Address;
use Shaqi\Aramex\API\Classes\Contact;
use Shaqi\Aramex\API\Classes\DateTime;
use Shaqi\Aramex\API\Classes\Dimension;
use Shaqi\Aramex\API\Classes\LabelInfo;
use Shaqi\Aramex\API\Classes\Money;
use Shaqi\Aramex\API\Classes\Party;
use Shaqi\Aramex\API\Classes\Pickup;
use Shaqi\Aramex\API\Classes\PickupItem;
use Shaqi\Aramex\API\Classes\Shipment as AramexShipment;
use Shaqi\Aramex\API\Classes\ShipmentDetails;
use Shaqi\Aramex\API\Classes\ShipmentItem;
use Shaqi\Aramex\API\Classes\Volume;
use Shaqi\Aramex\API\Classes\Weight;
use Shaqi\Aramex\Aramex;
use Shaqi\Aramex\AramexHelper;
use Throwable;

class AramexController extends BaseController
{
    protected string|int|null $userId = 0;

    public function __construct(
        protected ShipmentInterface $shipmentRepository,
        protected ShipmentHistoryInterface $shipmentHistoryRepository,
        protected Aramex $aramex
    ) {
        if (is_in_admin(true) && Auth::check()) {
            $this->userId = Auth::id();
        }
    }

    public function createShipment(int $id, Request $request, BaseHttpResponse $response)
    {
         
        $shipment = Shipment::query()->findOrFail($id);

        $order = $shipment->order;
        $user = $shipment->order->user;
        $shipping_address = $order->shippingAddress->toArray();
        $products = $shipment->order->products;

        $number_of_pieces =  $request->input('number_of_pieces');



        $weight = 0;
        foreach ($products as $product) {
            $weight = $weight + $product->weight;
        }
        $errors = [];

        try {

            $shipperAddress = [
                'name'    => $request->input('shipper_name'),
                'phone'   => $request->input('shipper_phone'),
                'email'   => $request->input('shipper_email'),
                'company'   => $request->input('shipper_company'),
                'country' => $request->input('shipper_country'),
                'state'   => $request->input('shipper_state'),
                'city'    => $request->input('shipper_city'),
                'address' => $request->input('shipper_address'),
                'zip_code' => $request->input('shipper_zip_code'),
            ];

            $receiverAddress = [
                'name'     => $request->input('receiver_name'),
                'phone'    => $request->input('receiver_phone'),
                'email'    => $request->input('receiver_email'),
                'company'  => $request->input('receiver_company'),
                'country'  => $request->input('receiver_country'),
                'state'    => $request->input('receiver_state'),
                'city'     => $request->input('receiver_city'),
                'address'  => $request->input('receiver_address'),
                'zip_code' => $request->input('receiver_zip_code'),
            ];

            $originAddress = $shipperAddress;
            $origin_address = AramexHelper::afterPrepareAddress($originAddress);
             

            $shipper_address = new Address('', $origin_address['country']);
            $shipper_address->setLine1($origin_address['address']);
            $shipper_address->setCity($origin_address['city']);
            $shipper_address->setStateOrProvinceCode($origin_address['state']);
            $shipper_address->setPostCode($origin_address['zip_code']);
            $shipper_address->setCountryCode($origin_address['country']);

            $shipper_contact = new Contact($origin_address['name'] ?: 'N\A', $origin_address['phone'] ?: 'N\A', $origin_address['phone'] ?: 'N\A', $origin_address['email'] ?: '<EMAIL>', $origin_address['company'] ?: $origin_address['name']);


            $shipper = new Party($shipper_address, $shipper_contact);
            $shipper->setReference1($order->id);
            $shipper->setAccountNumber(setting('shipping_aramex_account_number'));


            $receiver_address = AramexHelper::afterPrepareAddress($receiverAddress);

            $consignee_address = new Address('', $receiver_address['country']);
            $consignee_address->setLine1($receiver_address['address']);
            $consignee_address->setCity($receiver_address['city']);
            $consignee_address->setStateOrProvinceCode($receiver_address['state']);
            $consignee_address->setPostCode($receiver_address['zip_code']);
            $consignee_address->setCountryCode($receiver_address['country']);


            // $consignee_contact = new Contact($user->name, $user->phone ?: '***********', $user->phone ?: '***********', $user->email ?: '<EMAIL>', $user->name);
            $consignee_contact = new Contact($receiver_address['name'] ?: 'N\A', $receiver_address['phone'] ?: 'N\A', $receiver_address['phone'] ?: 'N\A', $receiver_address['email'] ?: '<EMAIL>', $receiver_address['company'] ?: $receiver_address['name']);
            // dd($consignee_contact);

            $consignee = new Party($consignee_address, $consignee_contact);
            $consignee->setReference1($order->id);



            $store_weight_unit = get_ecommerce_setting('store_weight_unit') ?: 'kg';
            $weight =  AramexHelper::convertToKg($weight, $store_weight_unit);

             
           

            $items_details = [];
            $dimensions_lengths = [];
            $dimensions_widths = [];
            $dimensions_height = 0;
            $dimensions_unit = '';
            $descriptionOfGoods = '';

            //dd($products);

            foreach ($products as $item) {
                $item_weight_in_kg =  AramexHelper::convertToKg($item->weight, $store_weight_unit);
                $weight_obj = new Weight($item_weight_in_kg);
                $shipmentItem = new ShipmentItem($item->qty, $weight_obj);
                $shipmentItem->setPackageType('item');
                $shipmentItem->setComments($item->product_name);
                $shipmentItem->setReference('No barcode');

                $descriptionOfGoods .= $item->product_id . ' - ' . trim($item->product_name . ' ');

                array_push($items_details, $shipmentItem);
                array_push($dimensions_widths, $item->width ?: 0);
                array_push($dimensions_lengths, $item->length ?: 0);
                $dimensions_height += $item->height ?: 0;
            }

            $descriptionOfGoods = substr($descriptionOfGoods, 0, 65);
            $service = '';
            $productType = '';
            $productGroup = $request->input('product_group');
            $paymentType = $request->input('payment_type');
            $paymentOption = $request->input('payment_option');

            if ( $productGroup == 'DOM') {
                $productType =  $request->input('allowed_domestic_methods');
                $service =  $request->input('allowed_domestic_additional_service');
            } else {
                $productType =  $request->input('allowed_international_methods');
                $service =  $request->input('allowed_international_additional_service');
            }

            $weight = new Weight($weight);

            $details = new ShipmentDetails($weight,  $descriptionOfGoods, $origin_address['country'], $number_of_pieces, $productGroup, $productType, $paymentType, $items_details);

            $details->setPaymentOption($paymentOption);
            //$details->setServices($service);

            $dimensions_length = max($dimensions_lengths);
            $dimensions_width = max($dimensions_widths);

            $dimension = new Dimension();
            $dimension->useCentimeterAsUnit();
            $dimension->setWidth($dimensions_width);
            $dimension->setLength($dimensions_length);
            $dimension->setHeight($dimensions_height);

            $details->setDimensions($dimension);

            $customAmount = $request->input('custom_amount') ?: 0;

            $customAmount = new Money($customAmount, get_application_currency()->title);
            $details->setCustomsValueAmount($customAmount);


            $shippingDateTime = Carbon::now()->format('Y-m-d\TH:i:s');

            $aramexShipment = new AramexShipment($shipper, $consignee, $shippingDateTime, $details);
            // $aramexShipment->setDueDate(time() + (7 * 24 * 60 * 60));
            $aramexShipment->setPickupLocation('Reception');

            $labelInfo = new LabelInfo();
            $labelInfo->setReportId(9729);
            $labelInfo->setReportType('URL');


            // dd($aramexShipment);



            $aramexResponse = $this->aramex::createShipments()->addShipment($aramexShipment)->setLabelInfo($labelInfo)->run();
            $shipments = $aramexResponse->getShipments();

            // dd($aramexResponse);

            if ($aramexResponse->isSuccessful()) {
                $firstShipment = $shipments[0];

                $shipmentID = $firstShipment->ID;
                $labelURL = $firstShipment->ShipmentLabel->LabelURL;


                $shipment->shipment_id = $shipmentID;
                $shipment->tracking_id = $shipmentID;
                $shipment->shipping_company_name = 'Aramex';
                $shipment->tracking_link = 'http://www.aramex.com/express/track.aspx';
                $shipment->label_url = $labelURL;
                $shipment->save();

                $this->shipmentHistoryRepository->createOrUpdate([
                    'action' => 'create_transaction',
                    'description' => trans('plugins/aramex::aramex.transaction.created_shipment', ['id' => $shipmentID,]),
                    'order_id' => $shipment->order_id,
                    'user_id' => $this->userId,
                    'shipment_id' => $shipment->id,
                ]);

                OrderHistory::query()->create([
                    'action' => OrderHistoryActionEnum::CREATE_SHIPMENT,
                    'description' => trans('plugins/aramex::aramex.transaction.created_shipment', ['id' => $shipmentID,]),
                    'order_id' => $order->getKey(),
                    'user_id' => $this->userId,
                ]);
            } else {

                $messages = $aramexResponse->getNotificationMessages();
                $msg = implode('<br>', $messages);


                return $response->setError(true)
                    ->setData(['errors' => $messages])
                    ->setMessage($msg);
            }
        } catch (Throwable $th) {
            return $response->setError(true)
                ->setData(['errors' => $errors])
                ->setMessage($th->getMessage());
        }
        return $this
            ->httpResponse()
            ->setPreviousUrl(route('orders.edit', ['order' => $order->id]))
            ->withUpdatedSuccessMessage();

        //return $response->setError((bool) $errors)->setMessage($errors ? Arr::first($errors) : '');

    }
    public function createPickup(int $id, Request $request, BaseHttpResponse $response)
    {
        // dd($request->all());
        // dd(Carbon::parse($request->date)->format('d/m/Y'));

        $shipment = Shipment::query()->findOrFail($id);
        //dd($shipment);

        $order = $shipment->order;
        $user = $shipment->order->user;
        $shipping_address = $order->shippingAddress->toArray();
        $products = $shipment->order->products;

        $number_of_pieces =  $request->number_of_pieces;


       
        $errors = [];

        try {

             // Ensure the date format matches the input format
            $pickupDate = Carbon::createFromFormat('m/d/Y', Carbon::parse($request->date)->format('m/d/Y'));

            // dd($pickupDate->year);

            $readyTimeH = $request->ready_hour;
            $readyTimeM = $request->ready_minute;
            // Create a Carbon instance for the ready time, subtract 2 hours, and get the timestamp
            $readyTime = Carbon::create(
                $pickupDate->year,
                $pickupDate->month,
                $pickupDate->day,
                $readyTimeH,
                $readyTimeM,
                0
            )->subHours(2)->timestamp;

            $closingTimeH = $request->latest_hour;
            $closingTimeM = $request->latest_minute;

            $closingTime = Carbon::create(
                $pickupDate->year,
                $pickupDate->month,
                $pickupDate->day,
                $closingTimeH,
                $closingTimeM,
                0
            )->subHours(2)->timestamp;

            $weight = 0;
            foreach ($products as $product) {
                $weight = $weight + $product->weight;
            }


            $originAddress = EcommerceHelper::getOriginAddress();
            $origin_address = AramexHelper::afterPrepareAddress($originAddress);

            // dd($origin_address);

            $source_address = new Address('', $origin_address['country']);
            $source_address->setLine1($origin_address['address']);
            $source_address->setCity($origin_address['city']);
            $source_address->setStateOrProvinceCode($origin_address['state']);
            $source_address->setPostCode($origin_address['zip_code']);
            $source_address->setCountryCode($origin_address['country']);

            $source_contact = new Contact($origin_address['name'] ?: 'N\A', $origin_address['phone'] ?: 'N\A', $origin_address['phone'] ?: 'N\A', $origin_address['email'] ?: '<EMAIL>', $origin_address['company'] ?: $origin_address['email']);


            $store_weight_unit = get_ecommerce_setting('store_weight_unit');
            $weight =  AramexHelper::convertToKg($weight, $store_weight_unit);

            $productType = '';
            $productGroup = $request->product_group;
            $paymentType = $request->payment_type;
            $paymentOption = $request->payment_option;
            if ( $productGroup == 'DOM') {
                $productType =  $request->allowed_domestic_methods;
            } else {
                $productType =  $request->allowed_international_methods;
            }

            $pickupItems = [];
            $dimensions_lengths = [];
            $dimensions_widths = [];
            $dimensions_height = 0;
            $dimensions_unit = '';
            $descriptionOfGoods = '';

            //dd($products);

            foreach ($products as $item) {
                $original_product =  Product::findOrFail($item->product_id);

                $item_weight_in_kg =  AramexHelper::convertToKg($item->weight, $store_weight_unit);
                $weight_obj = new Weight($item_weight_in_kg);

                $pickupItem = new PickupItem();
                $pickupItem->setProductGroup($productGroup);
                $pickupItem->setProductType($productType);
                $pickupItem->setPayment($paymentType);
                $pickupItem->setNumberOfShipments(1);
                $pickupItem->setPackageType('Item');
                $pickupItem->setShipmentWeight($weight_obj);
                $pickupItem->setNumberOfPieces($item->qty);
                $pickupItem->setCashAmount(new Money());
                $pickupItem->setExtraCharges(new Money());
                $pickupItem->setComments('Item of order #' . $order->id);

                $dimension = new Dimension();
                $dimension->useCentimeterAsUnit();
                $dimension->setWidth($original_product->wide ?: 0);
                $dimension->setLength($original_product->length ?: 0);
                $dimension->setHeight($original_product->height ?: 0);

                $pickupItem->setShipmentDimensions($dimension);


                $volume = new Volume();
                $volume->setUnit('cm');
                $volume->setValue(0);
                $pickupItem->setShipmentVolume($volume);



                $descriptionOfGoods .= $item->product_id . ' - ' . trim($item->product_name . ' ');

                array_push($pickupItems, $pickupItem);
                array_push($dimensions_widths, $original_product->wide ?: 0);
                array_push($dimensions_lengths, $original_product->length ?: 0);
                $dimensions_height += $original_product->height ?: 0;
            }

            $descriptionOfGoods = substr($descriptionOfGoods, 0, 65);

            $pickup = new Pickup();
            $pickup->setPickupAddress($source_address);
            $pickup->setPickupContact($source_contact);
            $pickup->setPickupLocation('Reception');
            $pickup->setComments($request->comments);
            $pickup->setPickupDate($readyTime);
            $pickup->setReadyTime($readyTime);
            $pickup->setLastPickupTime($closingTime);
            $pickup->setClosingTime($closingTime);
            $pickup->setStatus($request->status);
            $pickup->setVehicle($request->vehicle);
            $pickup->setReference1($order->id);
            $pickup->setReference2('Pickup created from the website.');
            $pickup->setShipments([]);
            foreach ($pickupItems as $item) {
                $pickup->addPickupItem($item);
            }
            $pickup->setExistingShipments(['Number' => $shipment->shipment_id, 'OriginEntity' => $productType, 'ProductGroup' => $productGroup]);
            $labelInfo = new LabelInfo();
            $labelInfo->setReportId(9729);
            $labelInfo->setReportType('URL');



            $aramexResponse =  $this->aramex::createPickup()
                ->setLabelInfo($labelInfo)
                ->setPickup($pickup)
                ->run();



            if ($aramexResponse->isSuccessful()) {

                $precessedPickup = $aramexResponse->getPrecessedPickup();
                // dd($precessedPickup);
                $this->shipmentHistoryRepository->createOrUpdate([
                    'action' => 'create_transaction',
                    'description' => trans('plugins/aramex::aramex.transaction.created_pickup', ['id' => $precessedPickup->getId(),]),
                    'order_id' => $shipment->order_id,
                    'user_id' => $this->userId,
                    'shipment_id' => $shipment->id,
                ]);

                OrderHistory::query()->create([
                    'action' => OrderHistoryActionEnum::UPDATE_SHIPPING_STATUS,
                    'description' => trans('plugins/aramex::aramex.transaction.created_pickup', ['id' => $precessedPickup->getId(),]),
                    'order_id' => $order->getKey(),
                    'user_id' => $this->userId,
                ]);
            } else {

                $messages = $aramexResponse->getNotificationMessages();
                $msg = implode('<br>', $messages);


                return $response->setError(true)
                    ->setData(['errors' => $errors])
                    ->setMessage($msg);
            }
        } catch (Exception $e) {
            // dd($e->getMessage());
            return $response->setError(true)
                ->setData(['errors' => $errors])
                ->setMessage($e->getMessage());
        }
        return $this
            ->httpResponse()
            ->setPreviousUrl(route('orders.edit', ['order' => $order->id]))
            ->withUpdatedSuccessMessage();

        //return $response->setError((bool) $errors)->setMessage($errors ? Arr::first($errors) : '');

    }

    public function showPickup(int $id, BaseHttpResponse $response)
    {
        $shipment = Shipment::query()->findOrFail($id);
        $this->check($shipment);

        $order = $shipment->order;
        $receiver_address = $order->shippingAddress;
        
        $products = $shipment->order->products;

        $number_of_pieces = count($products->toArray());

        $shipper_address = (object) EcommerceHelper::getOriginAddress();

        $current_date = Carbon::now()->format('Y-m-d');

        $hours = [];
        for ($i = 7; $i < 20; $i++) {
            $hh = str_pad($i, 2, '0', STR_PAD_LEFT);
            $hours[$hh] = $hh;
        }

        $minutes = [];
        for ($i = 0; $i <= 55; $i = $i + 5) {
            $mm = str_pad($i, 2, '0', STR_PAD_LEFT);
            $minutes[$mm] = $mm;
        }

        $content = '';
        $errors = [];
        $product_group = '';
        if ($receiver_address->country == $shipper_address->country) {
            $product_group = 'DOM';
        } else {
            $product_group = 'EXP';
        }

        
        $allowed_domestic_methods_all = aramexAllowedDomesticMethods();
        $settings_allowed_domestic_methods = json_decode(setting('shipping_aramex_allowed_domestic_method') ?: '[]');
        $allowed_domestic_methods = array();
        if (isset($settings_allowed_domestic_methods) && !empty($settings_allowed_domestic_methods)) {
            foreach ($settings_allowed_domestic_methods as $domestic_method) {
                $allowed_domestic_methods[$domestic_method] = $allowed_domestic_methods_all[$domestic_method];
            }
        }

        $allowed_international_methods_all = aramexAllowedInternationalMethods();
        $settings_allowed_international_methods = json_decode(setting('shipping_aramex_allowed_international_method') ?: '[]');
        $allowed_international_methods = array();
        if (isset($settings_allowed_international_methods) && !empty($settings_allowed_international_methods)) {
            foreach ($settings_allowed_international_methods as $international_method) {
                $allowed_international_methods[$international_method] = $allowed_international_methods_all[$international_method];
            }
        }

        $allowed_domestic_additional_services_all = aramexAllowedDomesticAdditionalServices();
        $settings_allowed_domestic_additional_services = json_decode(setting('shipping_aramex_allowed_domestic_additional_services') ?:'[]');
        $allowed_domestic_additional_service = array();
        if (isset($settings_allowed_domestic_additional_services) && !empty($settings_allowed_domestic_additional_services)) {
            foreach ($settings_allowed_domestic_additional_services as $domestic_additional_services) {
                $allowed_domestic_additional_service[$domestic_additional_services] = $allowed_domestic_additional_services_all[$domestic_additional_services];
            }
        }

        $allowed_international_additional_services_all = aramexAllowedInternationalAdditionalServices();
        $settings_allowed_international_additional_services = json_decode(setting('shipping_aramex_allowed_international_additional_services') ?: '[]');
        $allowed_international_additional_service = array();
        if (isset($settings_allowed_international_additional_services) && !empty($settings_allowed_international_additional_services)) {
            foreach ($settings_allowed_international_additional_services as $international_additional_services) {
                $allowed_international_additional_service[$international_additional_services] = $allowed_international_additional_services_all[$international_additional_services];
            }
        }
        $data  = [
            'shipment' => $shipment,
            'order' => $order,
            'current_date' => $current_date,
            'number_of_pieces' => $number_of_pieces,
            'hours' => $hours,
            'minutes' => $minutes,
            'receiver_address' => $receiver_address,
            'shipper_address' => $shipper_address,
            'product_group' => $product_group,
            'allowed_domestic_methods' => $allowed_domestic_methods,
            'allowed_international_methods' => $allowed_international_methods,
            'allowed_domestic_additional_service' => $allowed_domestic_additional_service,
            'allowed_international_additional_service' => $allowed_international_additional_service,
        ];



        $content = view('plugins/aramex::pickup-form')->with($data)->render();

        return $response->setError((bool) $errors)
            ->setData([
                'html' => $content,
                'errors' => $errors,
            ])
            ->setMessage($errors ? Arr::first($errors) : '');
    }

    public function showShipment(int $id, BaseHttpResponse $response)
    {
        $shipment = Shipment::query()->findOrFail($id);
        $this->check($shipment);

        $order = $shipment->order;
        $receiver_address = $order->shippingAddress;

        $products = $shipment->order->products;

        $number_of_pieces = count($products->toArray());

        // dd($receiver_address);
        $shipper_address = (object) EcommerceHelper::getOriginAddress();

        $current_date = Carbon::now()->format('m/d/Y');

        $hours = [];
        for ($i = 7; $i < 20; $i++) {
            $hh = str_pad($i, 2, '0', STR_PAD_LEFT);
            $hours[$hh] = $hh;
        }

        $minutes = [];
        for ($i = 0; $i <= 55; $i = $i + 5) {
            $mm = str_pad($i, 2, '0', STR_PAD_LEFT);
            $minutes[$mm] = $mm;
        }

        $content = '';
        $errors = [];

        $product_group = '';
        if ($receiver_address->country == $shipper_address->country) {
            $product_group = 'DOM';
        } else {
            $product_group = 'EXP';
        }

        $allowed_domestic_methods_all = aramexAllowedDomesticMethods();
        $settings_allowed_domestic_methods = json_decode(setting('shipping_aramex_allowed_domestic_method') ?: '[]');
        $allowed_domestic_methods = array();
        if (isset($settings_allowed_domestic_methods) && !empty($settings_allowed_domestic_methods)) {
            foreach ($settings_allowed_domestic_methods as $domestic_method) {
                $allowed_domestic_methods[$domestic_method] = $allowed_domestic_methods_all[$domestic_method];
            }
        }

        $allowed_international_methods_all = aramexAllowedInternationalMethods();
        $settings_allowed_international_methods = json_decode(setting('shipping_aramex_allowed_international_method') ?: '[]');
        $allowed_international_methods = array();
        if (isset($settings_allowed_international_methods) && !empty($settings_allowed_international_methods)) {
            foreach ($settings_allowed_international_methods as $international_method) {
                $allowed_international_methods[$international_method] = $allowed_international_methods_all[$international_method];
            }
        }

        $allowed_domestic_additional_services_all = aramexAllowedDomesticAdditionalServices();
        $settings_allowed_domestic_additional_services = json_decode(setting('shipping_aramex_allowed_domestic_additional_services') ?:'[]');
        $allowed_domestic_additional_service = array();
        if (isset($settings_allowed_domestic_additional_services) && !empty($settings_allowed_domestic_additional_services)) {
            foreach ($settings_allowed_domestic_additional_services as $domestic_additional_services) {
                $allowed_domestic_additional_service[$domestic_additional_services] = $allowed_domestic_additional_services_all[$domestic_additional_services];
            }
        }

        $allowed_international_additional_services_all = aramexAllowedInternationalAdditionalServices();
        $settings_allowed_international_additional_services = json_decode(setting('shipping_aramex_allowed_international_additional_services') ?: '[]');
        $allowed_international_additional_service = array();
        if (isset($settings_allowed_international_additional_services) && !empty($settings_allowed_international_additional_services)) {
            foreach ($settings_allowed_international_additional_services as $international_additional_services) {
                $allowed_international_additional_service[$international_additional_services] = $allowed_international_additional_services_all[$international_additional_services];
            }
        }
        $data  = [
            'shipment' => $shipment,
            'order' => $order,
            'number_of_pieces' => $number_of_pieces,
            'receiver_address' => $receiver_address,
            'shipper_address' => $shipper_address,
            'product_group' => $product_group,
            'allowed_domestic_methods' => $allowed_domestic_methods,
            'allowed_international_methods' => $allowed_international_methods,
            'allowed_domestic_additional_service' => $allowed_domestic_additional_service,
            'allowed_international_additional_service' => $allowed_international_additional_service,
        ];

        $content = view('plugins/aramex::shipment-form')->with($data)->render();

        return $response->setError((bool) $errors)
            ->setData([
                'html' => $content,
                'errors' => $errors,
            ])
            ->setMessage($errors ? Arr::first($errors) : '');
    }

    
 
    protected function check(Shipment $shipment): bool
    {
        // $order = $shipment->order;

        // if (! is_in_admin(true) && is_plugin_active('marketplace')) {
        //     $vendor = auth('customer')->user();
        //     $store = $vendor->store;

        //     if ($store->id != $order->store_id) {
        //         abort(403);
        //     }
        // }

        // if (! $order
        //     || ! $order->id
        //     || $order->shipping_method->getValue() != SHIPPO_SHIPPING_METHOD_NAME
        //     || ! $shipment->shipment_id) {
        //     abort(404);
        // }

        return true;
    }
 
}
