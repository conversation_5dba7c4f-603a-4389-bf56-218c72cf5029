<?php

namespace Shaqi\Aramex\Http\Requests\Settings;

use Botble\Base\Rules\OnOffRule;
use Bo<PERSON>ble\Base\Supports\Helper;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class AramexSettingRequest extends Request
{
    public function rules(): array
    {
        return [
            'shipping_aramex_status' => 'required|boolean',
            'shipping_aramex_account_country_code' => 'nullable|string|max:2',
            'shipping_aramex_account_entity' => 'nullable|string|max:10',
            'shipping_aramex_account_number' => 'nullable|string|max:20',
            'shipping_aramex_account_pin' => 'nullable|string|max:10',
            'shipping_aramex_username' => 'nullable|string|max:250',
            'shipping_aramex_password' => 'nullable|string|max:250',
            'shipping_aramex_test' => 'required|boolean',
            'shipping_aramex_custom_amount' => 'required|numeric|min:0',
            'shipping_aramex_available_countries_all' => 'nullable',
            'shipping_aramex_available_countries' => 'sometimes|array',
            'shipping_aramex_available_countries.*' => ['nullable', Rule::in(array_keys(Helper::countries()))],
            'shipping_aramex_allowed_domestic_method' => 'sometimes|array',
            'shipping_aramex_allowed_domestic_additional_services' => 'sometimes|array',
            'shipping_aramex_allowed_international_method' => 'sometimes|array',
            'shipping_aramex_allowed_international_additional_services' => 'sometimes|array',
        ];
    }
}
