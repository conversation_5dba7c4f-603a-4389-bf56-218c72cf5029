<?php

namespace Bo<PERSON><PERSON>\BranchManagement\Providers;

use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Facades\PanelSectionManager;
use Bo<PERSON>ble\Base\PanelSections\PanelSectionItem;
use Botble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Bo<PERSON>ble\BranchManagement\Models\Branch;
use Botble\BranchManagement\Repositories\Eloquent\BranchRepository;
use Bo<PERSON>ble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Bo<PERSON>ble\BranchManagement\Services\OrderAddressExtensionService;
use Bo<PERSON>ble\Ecommerce\Enums\ShippingMethodEnum;
use Bo<PERSON>ble\Ecommerce\PanelSections\SettingEcommercePanelSection;
use Illuminate\Routing\Events\RouteMatched;

use Botble\BranchManagement\Providers\EventServiceProvider;

class BranchManagementServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(BranchInterface::class, function () {
            return new BranchRepository(new Branch());
        });


    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/branch-management')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'email'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes();

        $this->app['events']->listen(RouteMatched::class, function (): void {
            DashboardMenu::registerItem([
                'id' => 'cms-plugins-branch-management',
                'priority' => 5,
                'parent_id' => null,
                'name' => 'plugins/branch-management::branch.name',
                'icon' => 'ti ti-building-store',
                'url' => route('branches.index'),
                'permissions' => ['branches.index'],
            ]);
        });

        $this->app->booted(function (): void {
            $this->registerShortcodes();
            $this->extendOrderAddress();

            $this->app->register(HookServiceProvider::class);
            $this->app->register(EventServiceProvider::class);
        });

        // Register settings in Ecommerce panel section
        PanelSectionManager::beforeRendering(function (): void {
            if (is_plugin_active('ecommerce')) {
                PanelSectionManager::default()->registerItem(
                    SettingEcommercePanelSection::class,
                    fn () => PanelSectionItem::make('settings.ecommerce.branch_management')
                        ->setTitle(trans('plugins/branch-management::branch.name'))
                        ->withIcon('ti ti-building-store')
                        ->withDescription(trans('plugins/branch-management::settings.description'))
                        ->withPriority(160)
                        ->withRoute('branch-management.settings.edit')
                );
            }
        });
    }

    protected function registerShortcodes(): void
    {
        if (! is_plugin_active('shortcode')) {
            return;
        }

        add_shortcode('branches-list', __('Branches List'), __('Display all branches grouped by city'), function ($shortcode) {
            return view('plugins/branch-management::shortcodes.branches-list', [
                'city_id' => $shortcode->city_id ?? null,
                'limit' => $shortcode->limit ?? 10,
            ])->render();
        });
    }


    protected function extendOrderAddress(): void
    {
        if (is_plugin_active('ecommerce') && OrderAddressExtensionService::checkDatabaseColumns()) {
            OrderAddressExtensionService::extendOrderAddressModel();
        }
    }
}
