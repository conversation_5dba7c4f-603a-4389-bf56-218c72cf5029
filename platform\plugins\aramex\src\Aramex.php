<?php

namespace Shaqi\Aramex;

use Shaqi\Aramex\API\Requests\Location\FetchCities;
use Shaqi\Aramex\API\Requests\Location\FetchCountries;
use Shaqi\Aramex\API\Requests\Location\FetchCountry;
use Shaqi\Aramex\API\Requests\Location\FetchDropOffLocations;
use Shaqi\Aramex\API\Requests\Location\FetchOffices;
use Shaqi\Aramex\API\Requests\Location\FetchStates;
use Shaqi\Aramex\API\Requests\Location\ValidateAddress;
use Shaqi\Aramex\API\Requests\Rate\CalculateRate;
use Shaqi\Aramex\API\Requests\Shipping\CancelPickup;
use Shaqi\Aramex\API\Requests\Shipping\CreatePickup;
use Shaqi\Aramex\API\Requests\Shipping\CreateShipments;
use Shaqi\Aramex\API\Requests\Shipping\GetLastShipmentsNumbersRange;
use Shaqi\Aramex\API\Requests\Shipping\PrintLabel;
use Shaqi\Aramex\API\Requests\Shipping\ReserveShipmentNumberRange;
use Shaqi\Aramex\API\Requests\Shipping\ScheduleDelivery;
use Shaqi\Aramex\API\Requests\Tracking\TrackPickup;
use Shaqi\Aramex\API\Requests\Tracking\TrackShipments;

class Aramex
{
    // Location
    public static function fetchCities(): FetchCities
    {
        return new FetchCities();
    }

    public static function fetchCountries(): FetchCountries
    {
        return new FetchCountries();
    }

    public static function fetchCountry(): FetchCountry
    {
        return new FetchCountry();
    }

    public static function fetchDropOffLocations(): FetchDropOffLocations
    {
        return new FetchDropOffLocations();
    }

    public static function fetchOffices(): FetchOffices
    {
        return new FetchOffices();
    }

    public static function fetchStates(): FetchStates
    {
        return new FetchStates();
    }

    public static function validateAddress(): ValidateAddress
    {
        return new ValidateAddress();
    }

    // Rate
    public static function calculateRate(): CalculateRate
    {
        return new CalculateRate();
    }

    // Shipping
    public static function cancelPickup(): CancelPickup
    {
        return new CancelPickup();
    }

    public static function createPickup(): CreatePickup
    {
        return new CreatePickup();
    }

    public static function createShipments(): CreateShipments
    {
        return new CreateShipments();
    }

    public static function getLastShipmentsNumbersRange(): GetLastShipmentsNumbersRange
    {
        return new GetLastShipmentsNumbersRange();
    }

    public static function printLabel(): PrintLabel
    {
        return new PrintLabel();
    }

    public static function reserveShipmentNumberRange(): ReserveShipmentNumberRange
    {
        return new ReserveShipmentNumberRange();
    }

    public static function scheduleDelivery(): ScheduleDelivery
    {
        return new ScheduleDelivery();
    }

    // Tracking
    public static function trackPickup(): TrackPickup
    {
        return new TrackPickup();
    }

    public static function trackShipments(): TrackShipments
    {
        return new TrackShipments();
    }

    public function retrieveShipment(string $shipmentId)
    {
     

         
    }
}