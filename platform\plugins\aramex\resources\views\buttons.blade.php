@if (!$shipment->shipment_id)
@php
$shipment_url = route('ecommerce.shipments.aramex.shipment.show', $shipment->id);
// if (!is_in_admin(true) && is_plugin_active('marketplace')) {
//     $url = route('marketplace.vendor.orders.aramex.show', $shipment->id);
// }
@endphp

   {{-- route('ecommerce.shipments.aramex.shipment.create', $shipment->id) --}}
        <button class="btn btn-default" data-bs-toggle="modal" data-bs-target="#aramex-create-shipment" data-url="{{ $shipment_url }}" type="button">
            <img src="{{ url('vendor/core/plugins/aramex/images/icon.svg') }}" alt="aramex" height="16" class="me-1">
            <span>{{ trans('Create Aramex Shipment') }}</span>
        </button>

        <div class="modal fade" id="aramex-create-shipment" aria-labelledby="aramex-aramex-create-shipment"
        aria-hidden="true" tabindex="-1">
        <div class="modal-dialog modal-dialog-scrollable modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="aramex-create-shipment">
                        {{ trans('Create Aramex Shipment') }}</h5>
                    <button class="btn-close" data-bs-dismiss="modal" type="button" aria-label="Close"></button>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>


@endif

@php
$pickup_url = route('ecommerce.shipments.aramex.shipment.pickup.show', $shipment->id);
// if (!is_in_admin(true) && is_plugin_active('marketplace')) {
//     $url = route('marketplace.vendor.orders.aramex.show', $shipment->id);
// }
@endphp

@if ($shipment->shipment_id)
    <button class="btn btn-default" data-bs-toggle="modal" data-bs-target="#aramex-view-n-create-pickup" data-url="{{ $pickup_url }}" type="button">
        <img src="{{ url('vendor/core/plugins/aramex/images/icon.svg') }}" alt="aramex" height="16" class="me-1">
        <span>{{ trans('Schedule Pickup') }}</span>
    </button>

    <div class="modal fade" id="aramex-view-n-create-pickup" aria-labelledby="aramex-view-n-create-pickup-label"
        aria-hidden="true" tabindex="-1">
        <div class="modal-dialog modal-dialog-scrollable modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="aramex-view-n-create-pickup-label">
                        {{ trans('Schedule Pickup') }}</h5>
                    <button class="btn-close" data-bs-dismiss="modal" type="button" aria-label="Close"></button>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>
@endif

@if ($shipment->label_url)
    <a
        class="btn btn-success"
        href="{{ $shipment->label_url }}"
        target="_blank"
        rel="noopener noreferrer"
    >
        <i class="fa fa-print"></i>
        <span>{{ trans('plugins/aramex::aramex.print_label') }}</span>
    </a>
@endif

@push('footer')

 <script>



$(document).on('change', '#product_group', function () {
    var selectedGroup = $(this).val();

    if (selectedGroup === 'DOM') {
        $('#allowed_domestic_methods').show();
        $('#allowed_international_methods').hide();

        $('#allowed_domestic_additional_service').show();
        $('#allowed_international_additional_service').hide();

    } else if (selectedGroup === 'EXP') {
        $('#allowed_domestic_methods').hide();
        $('#allowed_international_methods').show();


        $('#allowed_domestic_additional_service').hide();
        $('#allowed_international_additional_service').show();
    }

});

 

</script>
@endpush