class BranchManagement {
    constructor() {
        this.init();
    }

    init() {
        this.handleOperatingHours();
        this.handleCityChange();
        this.handleBranchSelection();
    }

    handleOperatingHours() {
        // Handle operating hours toggle in admin form
        document.querySelectorAll('.day-toggle').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const day = this.name.match(/\[(\w+)\]/)[1];
                const openTime = document.getElementById(`day_${day}_open_time`);
                const closeTime = document.getElementById(`day_${day}_close_time`);
                
                if (this.checked) {
                    openTime.disabled = false;
                    closeTime.disabled = false;
                    openTime.required = true;
                    closeTime.required = true;
                } else {
                    openTime.disabled = true;
                    closeTime.disabled = true;
                    openTime.required = false;
                    closeTime.required = false;
                }
            });
        });
    }

    handleCityChange() {
        // Handle city change in branch form to auto-populate state and country
        const citySelect = document.querySelector('select[name="city_id"]');
        if (citySelect) {
            citySelect.addEventListener('change', function() {
                // This could be enhanced to auto-populate state and country
                // based on the selected city
            });
        }
    }

    handleBranchSelection() {
        // Handle branch selection in checkout
        const branchSelect = document.getElementById('pickup_branch_id');
        if (branchSelect) {
            branchSelect.addEventListener('change', function() {
                const branchDetails = document.getElementById('selected-branch-details');
                if (this.value) {
                    branchDetails.style.display = 'block';
                } else {
                    branchDetails.style.display = 'none';
                }
            });
        }
    }

    static loadBranchesByCity(cityId, callback) {
        if (!cityId) {
            callback([]);
            return;
        }

        fetch(`/ajax/branches-by-city?city_id=${cityId}&pickup_only=1`)
            .then(response => response.json())
            .then(branches => callback(branches))
            .catch(error => {
                console.error('Error loading branches:', error);
                callback([]);
            });
    }

    static formatOperatingHours(operatingHours) {
        if (!operatingHours) return 'Hours not specified';

        const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const todayHours = operatingHours[today];

        if (!todayHours || !todayHours.is_open) {
            return 'Closed today';
        }

        return `Today: ${todayHours.open} - ${todayHours.close}`;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new BranchManagement();
});

// Export for use in other scripts
window.BranchManagement = BranchManagement;
