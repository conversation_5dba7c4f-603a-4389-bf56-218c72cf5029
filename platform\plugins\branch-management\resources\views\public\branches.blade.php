@extends(Theme::getThemeNamespace('layouts.base'))

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="page-title">{{ trans('plugins/branch-management::branch.name') }}</h1>
            <p class="page-description">{{ __('Find our store locations and pickup points') }}</p>
        </div>
    </div>
    
    @if($branches->isNotEmpty())
        @foreach($branches as $cityName => $cityBranches)
        <div class="city-section mb-5">
            <h2 class="city-title border-bottom pb-2 mb-4">{{ $cityName }}</h2>
            
            <div class="row">
                @foreach($cityBranches as $branch)
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="branch-card card h-100">
                        @if($branch->image)
                        <img src="{{ RvMedia::getImageUrl($branch->image) }}" class="card-img-top" alt="{{ $branch->name }}" style="height: 200px; object-fit: cover;">
                        @endif
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ $branch->name }}</h5>
                            
                            @if($branch->description)
                            <p class="card-text text-muted">{{ Str::limit($branch->description, 100) }}</p>
                            @endif
                            
                            <div class="branch-info">
                                <p class="mb-2">
                                    <i class="fa fa-map-marker-alt text-primary"></i>
                                    <small>{{ $branch->full_address }}</small>
                                </p>
                                
                                @if($branch->phone)
                                <p class="mb-2">
                                    <i class="fa fa-phone text-primary"></i>
                                    <small><a href="tel:{{ $branch->phone }}">{{ $branch->phone }}</a></small>
                                </p>
                                @endif
                                
                                @if($branch->email)
                                <p class="mb-2">
                                    <i class="fa fa-envelope text-primary"></i>
                                    <small><a href="mailto:{{ $branch->email }}">{{ $branch->email }}</a></small>
                                </p>
                                @endif
                            </div>
                            
                            <div class="branch-status mt-3">
                                @if($branch->is_pickup_available)
                                    <span class="badge bg-success">{{ trans('plugins/branch-management::branch.pickup.title') }}</span>
                                    @if($branch->pickup_fee > 0)
                                    <span class="badge bg-info">{{ format_price($branch->pickup_fee) }}</span>
                                    @else
                                    <span class="badge bg-success">{{ __('Free Pickup') }}</span>
                                    @endif
                                @endif
                                
                                @if($branch->is_featured)
                                    <span class="badge bg-warning">{{ __('Featured') }}</span>
                                @endif
                                
                                @if($branch->is_open)
                                    <span class="badge bg-success">{{ __('Open') }}</span>
                                @else
                                    <span class="badge bg-warning">{{ __('Closed') }}</span>
                                @endif
                            </div>
                            
                            @if($branch->operating_hours)
                            <div class="operating-hours mt-2">
                                <small class="text-muted">
                                    @php
                                        $today = strtolower(now()->format('l'));
                                        $todayHours = $branch->operating_hours[$today] ?? null;
                                    @endphp
                                    @if($todayHours && $todayHours['is_open'])
                                        <i class="fa fa-clock"></i> {{ __('Today') }}: {{ $todayHours['open'] }} - {{ $todayHours['close'] }}
                                    @else
                                        <i class="fa fa-clock"></i> {{ __('Closed today') }}
                                    @endif
                                </small>
                            </div>
                            @endif
                        </div>
                        
                        <div class="card-footer">
                            <a href="{{ route('public.branch.detail', $branch->slug) }}" class="btn btn-outline-primary btn-sm">
                                {{ __('View Details') }}
                            </a>
                            
                            @if($branch->latitude && $branch->longitude)
                            <a href="https://maps.google.com/?q={{ $branch->latitude }},{{ $branch->longitude }}" target="_blank" class="btn btn-outline-secondary btn-sm">
                                {{ __('Get Directions') }}
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endforeach
    @else
        <div class="alert alert-info">
            <p class="mb-0">{{ __('No branches available at the moment.') }}</p>
        </div>
    @endif
</div>
@endsection
