'use strict'

var Aramex = Aramex || {};

Aramex.init = () => {
    $(document).on('show.bs.modal', '#aramex-view-n-create-pickup', function (e) {
        const $self = $(e.currentTarget)
        const $related = $(e.relatedTarget)
        $self.find('.modal-body').html('')

        $.ajax({
            type: 'GET',
            url: $related.data('url'),
            beforeSend: () => {
                $related.addClass('button-loading')
            },
            success: (res) => {
                if (res.error) {
                    Botble.showError(res.message)
                } else {
                    $self.find('.modal-body').html(res.data.html)
                }
            },
            error: (res) => {
                Botble.handleError(res)
            },
            complete: () => {
                $related.removeClass('button-loading')
            },
        })
    })

    $(document).on('click', '#aramex-view-n-create-pickup .create-pickup', function (e) {
        const $self = $(e.currentTarget)

        $.ajax({
            type: 'POST',
            url: $self.data('url'),
            beforeSend: () => {
                $self.addClass('button-loading')
            },
            success: (res) => {
                if (res.error) {
                    Botble.showError(res.message)
                } else {
                    $('[data-bs-target="#aramex-view-n-create-pickup"]').addClass('d-none')
                    $('#aramex-view-n-create-pickup').modal('hide')
                    Botble.showSuccess(res.message)
                }
            },
            error: (res) => {
                Botble.handleError(res)
            },
            complete: () => {
                $self.removeClass('button-loading')
            },
        })
    })


    $(document).on('show.bs.modal', '#aramex-create-shipment', function (e) {
        const $self = $(e.currentTarget)
        const $related = $(e.relatedTarget)
        $self.find('.modal-body').html('')

        $.ajax({
            type: 'GET',
            url: $related.data('url'),
            beforeSend: () => {
                $related.addClass('button-loading')
            },
            success: (res) => {
                if (res.error) {
                    Botble.showError(res.message)
                } else {
                    $self.find('.modal-body').html(res.data.html)
                }
            },
            error: (res) => {
                Botble.handleError(res)
            },
            complete: () => {
                $related.removeClass('button-loading')
            },
        })
    })

    $(document).on('click', '#aramex-create-shipment .create-shipment', function (e) {
        const $self = $(e.currentTarget)

        $.ajax({
            type: 'POST',
            url: $self.data('url'),
            beforeSend: () => {
                $self.addClass('button-loading')
            },
            success: (res) => {
                if (res.error) {
                    Botble.showError(res.message)
                } else {
                    $('[data-bs-target="#aramex-create-shipment"]').addClass('d-none')
                    $('#aramex-create-shipment').modal('hide')
                    Botble.showSuccess(res.message)
                }
            },
            error: (res) => {
                Botble.handleError(res)
            },
            complete: () => {
                $self.removeClass('button-loading')
            },
        })
    })
}

$(() => {
    Aramex.init()
})
