<?php

namespace Bo<PERSON>ble\BranchManagement;

use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use Bo<PERSON>ble\Setting\Facades\Setting;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function activate(): void
    {
        // Set default ecommerce settings when plugin is activated
        $prefix = 'plugins_ecommerce_';

        Setting::set([
            $prefix . 'branch_management_enable_pickup' => true,
            $prefix . 'branch_management_show_on_product_page' => true,
        ])->save();
    }

    public static function deactivate(): void
    {
        // Logic to run when the plugin is deactivated
        // We don't remove data, just disable functionality
    }

    public static function remove(): void
    {
        // Clean up when the plugin is removed
        Schema::dropIfExists('branches');

        // Remove plugin settings from ecommerce settings
        $prefix = 'plugins_ecommerce_';

        Setting::delete([
            $prefix . 'branch_management_enable_pickup',
            $prefix . 'branch_management_show_on_product_page',
        ]);
    }
}
