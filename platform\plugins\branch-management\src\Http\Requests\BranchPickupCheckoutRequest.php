<?php

namespace Bo<PERSON>ble\BranchManagement\Http\Requests;

use Botble\Ecommerce\Http\Requests\CheckoutRequest;
use Bo<PERSON>ble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Illuminate\Validation\Rule;

class BranchPickupCheckoutRequest extends CheckoutRequest
{
    public function rules(): array
    {
        $rules = parent::rules();

        // Add branch pickup validation rules
        $this->addBranchPickupValidationRules($rules);

        return $rules;
    }

    protected function addBranchPickupValidationRules(array &$rules): void
    {
        // Check if branch pickup is selected
        if ($this->isBranchPickupSelected()) {
            $rules['pickup_city_id'] = [
                'required',
                'integer',
                'exists:cities,id',
            ];

            $rules['pickup_branch_id'] = [
                'required',
                'integer',
                'exists:branches,id',
                function ($attribute, $value, $fail) {
                    $branchRepository = app(BranchInterface::class);
                    $branch = $branchRepository->findById($value);
                    
                    if (!$branch) {
                        $fail('The selected branch does not exist.');
                        return;
                    }

                    if (!$branch->is_pickup_available) {
                        $fail('The selected branch is not available for pickup.');
                        return;
                    }

                    // Validate that the branch belongs to the selected city
                    $cityId = $this->input('pickup_city_id');
                    if ($cityId && $branch->city_id != $cityId) {
                        $fail('The selected branch does not belong to the selected city.');
                    }
                },
            ];
        }
    }

    protected function isBranchPickupSelected(): bool
    {
        $shippingMethod = $this->input('shipping_method');
        $shippingOption = $this->input('shipping_option');

        // Check shipping method
        if ($shippingMethod) {
            $methodValue = strtolower($shippingMethod);
            if ($methodValue === 'branch_pickup' || 
                str_contains($methodValue, 'pickup') || 
                str_contains($methodValue, 'branch')) {
                return true;
            }
        }

        // Check shipping option
        if ($shippingOption) {
            $optionValue = strtolower($shippingOption);
            if ($optionValue === 'pickup_branch' || 
                str_contains($optionValue, 'pickup') || 
                str_contains($optionValue, 'branch')) {
                return true;
            }
        }

        return false;
    }

    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'pickup_city_id.required' => 'Please select a city for branch pickup.',
            'pickup_city_id.exists' => 'The selected city is invalid.',
            'pickup_branch_id.required' => 'Please select a branch for pickup.',
            'pickup_branch_id.exists' => 'The selected branch is invalid.',
        ]);
    }
}
