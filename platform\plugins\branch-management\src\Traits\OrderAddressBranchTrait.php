<?php

namespace Botble\BranchManagement\Traits;

use Botble\BranchManagement\Models\Branch;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait OrderAddressBranchTrait
{
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class)->withDefault();
    }

    public function getIsBranchPickupAttribute(): bool
    {
        return (bool) $this->attributes['is_branch_pickup'] ?? false;
    }

    public function getBranchNameAttribute(): ?string
    {
        return $this->branch->name ?? null;
    }

    public function getBranchFullAddressAttribute(): ?string
    {
        return $this->branch->full_address ?? null;
    }

    public function getBranchPhoneAttribute(): ?string
    {
        return $this->branch->phone ?? null;
    }


}
