<?php

namespace Bo<PERSON>ble\BranchManagement\Listeners;

use <PERSON><PERSON>ble\Ecommerce\Events\OrderPlacedEvent;
use Bo<PERSON>ble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProcessBranchPickupOrder
{
    public function handle(OrderPlacedEvent $event): void
    {
        $order = $event->order;

        Log::info('Branch pickup order processing started', [
            'order_id' => $order->id,
            'shipping_method' => $order->shipping_method,
            'shipping_method_string' => (string) $order->shipping_method,
            'constant_value' => BRANCH_PICKUP_SHIPPING_METHOD_NAME,
            'are_equal' => ((string) $order->shipping_method) === BRANCH_PICKUP_SHIPPING_METHOD_NAME,
        ]);

        // Get request data from session or global request
        $request = request();

        $shippingMethod = $request->input('shipping_method');
        $shippingOption = $request->input('shipping_option');

        Log::info('Branch pickup order inputs', [
            'shipping_method' => $shippingMethod,
            'shipping_option' => $shippingOption,
            'pickup_branch_id' => $request->input('pickup_branch_id'),
            'pickup_city_id' => $request->input('pickup_city_id'),
        ]);

        // Check if this is a branch pickup order
        $isBranchPickup = false;

        // Handle shipping method (could be string or array)
        if (is_array($shippingMethod)) {
            foreach ($shippingMethod as $method) {
                if ($method === BRANCH_PICKUP_SHIPPING_METHOD_NAME ||
                    (is_string($method) && strpos($method, 'pickup') !== false)) {
                    $isBranchPickup = true;
                    break;
                }
            }
        } elseif (is_string($shippingMethod)) {
            $isBranchPickup = $shippingMethod === BRANCH_PICKUP_SHIPPING_METHOD_NAME ||
                             strpos($shippingMethod, 'pickup') !== false;
        }

        // Handle shipping option (could be string or array)
        if (!$isBranchPickup && is_array($shippingOption)) {
            foreach ($shippingOption as $option) {
                if ($option === 'pickup_branch' ||
                    (is_string($option) && strpos($option, 'pickup') !== false)) {
                    $isBranchPickup = true;
                    break;
                }
            }
        } elseif (!$isBranchPickup && is_string($shippingOption)) {
            $isBranchPickup = $shippingOption === 'pickup_branch' ||
                             strpos($shippingOption, 'pickup') !== false;
        }

        // Check order's shipping method as fallback
        if (!$isBranchPickup && is_string($order->shipping_method)) {
            $isBranchPickup = strpos($order->shipping_method, 'pickup') !== false ||
                             strpos($order->shipping_method, 'branch') !== false;
        }

        Log::info('Branch pickup check result', [
            'is_branch_pickup' => $isBranchPickup,
            'order_shipping_method' => $order->shipping_method,
        ]);

        if (!$isBranchPickup) {
            return;
        }

        // Get branch ID from different possible sources
        $branchId = null;

        // Method 1: From pickup_branch_id field (new checkout form)
        $branchId = $request->input('pickup_branch_id');

        // Method 2: From shipping option (format: branch_123) - legacy support
        if (!$branchId && $shippingOption) {
            if (is_array($shippingOption)) {
                foreach ($shippingOption as $option) {
                    if (is_string($option) && preg_match('/^branch_(\d+)$/', $option, $matches)) {
                        $branchId = $matches[1];
                        break;
                    }
                }
            } elseif (is_string($shippingOption) && preg_match('/^branch_(\d+)$/', $shippingOption, $matches)) {
                $branchId = $matches[1];
            }
        }

        Log::info('Branch ID extraction result', [
            'order_id' => $order->id,
            'branch_id' => $branchId,
            'pickup_branch_id_input' => $request->input('pickup_branch_id'),
            'shipping_option' => $shippingOption,
        ]);

        if (!$branchId) {
            Log::warning('No branch ID found for pickup order', [
                'order_id' => $order->id,
                'all_request_data' => $request->all(),
            ]);
            return;
        }

        // Validate that the branch exists and is available for pickup
        $branchRepository = app(BranchInterface::class);
        $branch = $branchRepository->findById($branchId);

        if (!$branch || !$branch->is_pickup_available) {
            Log::warning('Branch not found or not available for pickup', [
                'order_id' => $order->id,
                'branch_id' => $branchId,
                'branch_exists' => $branch ? 'yes' : 'no',
                'pickup_available' => $branch ? $branch->is_pickup_available : 'n/a',
            ]);
            return;
        }

        // Update the shipping address with branch information
        $cityId = $request->input('pickup_city_id');

        Log::info('Shipping address update attempt', [
            'order_id' => $order->id,
            'has_shipping_address' => $order->shippingAddress ? 'yes' : 'no',
            'shipping_address_id' => $order->shippingAddress ? $order->shippingAddress->id : 'null',
            'branch_id_to_set' => $branchId,
            'city_id_to_set' => $cityId,
        ]);

        if ($order->shippingAddress) {
            $updateData = [
                'branch_id' => (int) $branchId,  // Ensure integer
                'is_branch_pickup' => 1,         // Explicit 1 instead of true
            ];

            // Note: city_id already exists in ec_order_addresses, no need to update it

            Log::info('Before shipping address update', [
                'address_id' => $order->shippingAddress->id,
                'current_branch_id' => $order->shippingAddress->branch_id,
                'current_is_branch_pickup' => $order->shippingAddress->is_branch_pickup,
                'update_data' => $updateData,
            ]);

            try {
                // Method 1: Direct update
                $result = $order->shippingAddress->update($updateData);

                Log::info('Direct update result', [
                    'result' => $result,
                    'fresh_branch_id' => $order->shippingAddress->fresh()->branch_id,
                    'fresh_is_branch_pickup' => $order->shippingAddress->fresh()->is_branch_pickup,
                ]);

                // Method 2: If direct update fails, try DB query
                if (!$result || !$order->shippingAddress->fresh()->branch_id) {
                    Log::warning('Direct update failed, trying DB query');

                    $dbResult = DB::table('ec_order_addresses')
                        ->where('id', $order->shippingAddress->id)
                        ->update($updateData);

                    Log::info('DB query update result', [
                        'affected_rows' => $dbResult,
                        'query_data' => $updateData,
                    ]);
                }

            } catch (\Exception $e) {
                Log::error('Shipping address update failed', [
                    'error' => $e->getMessage(),
                    'order_id' => $order->id,
                    'address_id' => $order->shippingAddress->id,
                    'update_data' => $updateData,
                ]);
            }
        } else {
            Log::warning('Order has no shipping address to update', ['order_id' => $order->id]);
        }

        // Update the order's shipping method name using helper function
        $cityId = $request->input('pickup_city_id');
        
        // Store additional branch pickup information
        $this->storeBranchPickupDetails($order, $request, $branch);
    }

    protected function storeBranchPickupDetails($order, $request, $branch): void
    {
        // Store pickup city information if provided
        $pickupCityId = $request->input('pickup_city_id');
        if ($pickupCityId) {

            // For now, we rely on the branch relationship to get city info
        }

        // Log the branch pickup for debugging/tracking
        Log::info('Branch pickup order created', [
            'order_id' => $order->id,
            'branch_id' => $branch->id,
            'branch_name' => $branch->name,
            'pickup_fee' => $branch->pickup_fee,
        ]);
    }
}
