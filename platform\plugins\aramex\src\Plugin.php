<?php

namespace Shaqi\Aramex;

use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON><PERSON>\Setting\Facades\Setting;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Setting::delete([
            'shipping_aramex_status',
            'shipping_aramex_account_country_code',
            'shipping_aramex_account_entity',
            'shipping_aramex_account_number',
            'shipping_aramex_account_pin',
            'shipping_aramex_username',
            'shipping_aramex_password',
            'shipping_aramex_test',
            'shipping_aramex_logging',
            'shipping_aramex_cache_response',
            'shipping_aramex_webhooks',
            'shipping_aramex_custom_amount',
            'shipping_aramex_available_countries_all',
            'shipping_aramex_available_countries',
            'shipping_aramex_product_group',
            'shipping_aramex_allowed_domestic_method',
            'shipping_aramex_allowed_domestic_additional_services',
            'shipping_aramex_allowed_international_method',
            'shipping_aramex_allowed_international_additional_services',
            'shipping_aramex_payment_type',
            'shipping_aramex_payment_option',
        ]);
    }
}
