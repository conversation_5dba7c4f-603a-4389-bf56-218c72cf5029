<?php if(get_ecommerce_setting('branch_pickup_status', 1)): ?>
<div id="branch-pickup-section" class="branch-pickup-section mt-3" style="display: none;" data-ajax-url="<?php echo e(route('public.ajax.branches-by-city')); ?>">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fa fa-store"></i> <?php echo e(trans('plugins/branch-management::branch.pickup.title')); ?>

            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3"><?php echo e(trans('plugins/branch-management::branch.pickup.checkout_description')); ?></p>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="pickup_city_id" class="form-label required"><?php echo e(trans('plugins/branch-management::branch.pickup.select_city_label')); ?></label>
                        <select id="pickup_city_id" name="pickup_city_id" class="form-control" required>
                            <option value=""><?php echo e(trans('plugins/branch-management::branch.pickup.select_city')); ?></option>
                            <?php if(isset($citiesWithBranches) && $citiesWithBranches->isNotEmpty()): ?>
                                <?php $__currentLoopData = $citiesWithBranches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($city->id); ?>"><?php echo e($city->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </select>
                        <small class="text-muted"><?php echo e(trans('plugins/branch-management::branch.pickup.city_help')); ?></small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="pickup_branch_id" class="form-label required"><?php echo e(trans('plugins/branch-management::branch.pickup.select_branch_label')); ?></label>
                        <select id="pickup_branch_id" name="pickup_branch_id" class="form-control" disabled required>
                            <option value=""><?php echo e(trans('plugins/branch-management::branch.pickup.select_city_first')); ?></option>
                        </select>
                        <small class="text-muted"><?php echo e(trans('plugins/branch-management::branch.pickup.branch_help')); ?></small>
                    </div>
                </div>
            </div>

            <div id="selected-branch-details" class="selected-branch-details mt-3" style="display: none;">
                <!-- Branch details will be loaded here via JavaScript -->
            </div>

            <div id="branch-loading" class="text-center mt-3" style="display: none;">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2"><?php echo e(trans('plugins/branch-management::branch.loading_branches')); ?></span>
            </div>

            <!-- Hidden fields to store selected values for form submission -->
            <input type="hidden" name="pickup_city_id" id="pickup_city_id_hidden" value="">
            <input type="hidden" name="pickup_branch_id" id="pickup_branch_id_hidden" value="">
        </div>
    </div>
</div>







<!-- Branch pickup functionality is now handled by the external branch-checkout.js file -->
<script>
// Simple initialization to ensure the external script can find the elements
document.addEventListener('DOMContentLoaded', function() {
    console.log('Branch selection template loaded - external script will handle functionality');

    // Add a test validation button for debugging
    const testButton = document.createElement('button');
    testButton.textContent = 'TEST VALIDATION';
    testButton.type = 'button';
    testButton.style.cssText = 'position: fixed; top: 50px; right: 10px; z-index: 9999; background: orange; color: white; padding: 8px; border: none; cursor: pointer; font-size: 12px;';
    testButton.onclick = function() {
        if (window.branchCheckoutManager) {
            const isValid = window.branchCheckoutManager.validateBranchPickup();
            alert('Validation Result: ' + (isValid ? 'PASSED' : 'FAILED'));
        } else {
            alert('Branch checkout manager not found!');
        }
    };
    document.body.appendChild(testButton);

    // Add a test form submit button
    const testSubmitButton = document.createElement('button');
    testSubmitButton.textContent = 'TEST FORM SUBMIT';
    testSubmitButton.type = 'button';
    testSubmitButton.style.cssText = 'position: fixed; top: 90px; right: 10px; z-index: 9999; background: red; color: white; padding: 8px; border: none; cursor: pointer; font-size: 12px;';
    testSubmitButton.onclick = function() {
        const form = document.querySelector('form.checkout-form') || document.querySelector('form');
        if (form) {
            console.log('Manually triggering form submit...');
            form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
        } else {
            alert('No form found!');
        }
    };
    document.body.appendChild(testSubmitButton);
});
</script>
<?php endif; ?>
<?php /**PATH D:\laragon\www\martfury\platform/plugins/branch-management/resources/views/checkout/branch-selection.blade.php ENDPATH**/ ?>