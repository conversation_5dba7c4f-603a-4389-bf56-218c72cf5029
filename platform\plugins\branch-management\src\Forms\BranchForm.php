<?php

namespace Bo<PERSON>ble\BranchManagement\Forms;

use Botble\Base\Forms\FieldOptions\DescriptionFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\MediaImageFieldOption;
use Botble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NumberFieldOption;
use Botble\Base\Forms\FieldOptions\OnOffFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\SortOrderFieldOption;
use Botble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\FieldOptions\TextareaFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\MediaImageField;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\OnOffField;
use Botble\Base\Forms\Fields\SelectField;
use Bo<PERSON>ble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\BranchManagement\Http\Requests\BranchRequest;
use Bo<PERSON><PERSON>\BranchManagement\Models\Branch;

class BranchForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(Branch::class)
            ->setValidatorClass(BranchRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required())
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add(
                'address',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(__('Address'))
                    ->required()
                    ->rows(3)
            )
            ->add(
                'phone',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Phone'))
                    ->placeholder(__('Branch phone number'))
            )
            ->add(
                'email',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Email'))
                    ->placeholder(__('Branch email address'))
            )
            ->add(
                'manager_name',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Manager Name'))
                    ->placeholder(__('Branch manager name'))
            )
            ->add(
                'manager_phone',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Manager Phone'))
                    ->placeholder(__('Manager phone number'))
            )
            ->add(
                'manager_email',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Manager Email'))
                    ->placeholder(__('Manager email address'))
            )
            ->add(
                'city_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(__('City'))
                    ->required()
                    ->choices($this->getCities())
                    ->searchable()
            )
            ->add(
                'zip_code',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Zip Code'))
                    ->placeholder(__('Postal/Zip code'))
            )
            ->add(
                'latitude',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(__('Latitude'))
                    ->placeholder(__('GPS Latitude'))
                    ->step(0.00000001)
            )
            ->add(
                'longitude',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(__('Longitude'))
                    ->placeholder(__('GPS Longitude'))
                    ->step(0.00000001)
            )

            ->add('order', NumberField::class, SortOrderFieldOption::make())
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->add(
                'is_featured',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(__('Is Featured'))
            )
            ->add(
                'is_pickup_available',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(__('Pickup Available'))
                    ->defaultValue(true)
            )
            ->add(
                'image',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(__('Branch Image'))
            )
            ->add(
                'operating_hours',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(__('Operating Hours'))
                    ->placeholder(__('Enter operating hours (e.g., Mon-Fri: 9:00 AM - 6:00 PM, Sat: 9:00 AM - 2:00 PM, Sun: Closed)'))
                    ->rows(3)
            )
            ->add(
                'special_instructions',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(__('Special Instructions'))
                    ->placeholder(__('Any special instructions for customers'))
                    ->rows(3)
            )
            ->setBreakFieldPoint('status');
    }

    protected function getCities(): array
    {
        if (!is_plugin_active('location')) {
            return [];
        }

        return \Botble\Location\Models\City::query()
            ->wherePublished()
            ->oldest('order')
            ->oldest('name')
            ->pluck('name', 'id')
            ->all();
    }
}
