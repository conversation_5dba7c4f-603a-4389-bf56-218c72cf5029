<?php

namespace Bo<PERSON>ble\BranchManagement\Http\Requests;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Rules\OnOffRule;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class BranchRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'address' => ['required', 'string', 'max:500'],
            'phone' => ['nullable', 'string', 'max:20'],
            'email' => ['nullable', 'email', 'max:100'],
            'manager_name' => ['nullable', 'string', 'max:100'],
            'manager_phone' => ['nullable', 'string', 'max:20'],
            'manager_email' => ['nullable', 'email', 'max:100'],
            'city_id' => ['required', 'integer', 'exists:cities,id'],
            'zip_code' => ['nullable', 'string', 'max:20'],
            'latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'longitude' => ['nullable', 'numeric', 'between:-180,180'],
            'order' => ['required', 'integer', 'min:0', 'max:127'],
            'status' => [Rule::in(BaseStatusEnum::values())],
            'is_featured' => [new OnOffRule()],
            'is_pickup_available' => [new OnOffRule()],
            'operating_hours' => ['nullable', 'string'],
            'special_instructions' => ['nullable', 'string'],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('Name'),
            'description' => __('Description'),
            'address' => __('Address'),
            'phone' => __('Phone'),
            'email' => __('Email'),
            'manager_name' => __('Manager Name'),
            'manager_phone' => __('Manager Phone'),
            'manager_email' => __('Manager Email'),
            'city_id' => __('City'),
            'zip_code' => __('Zip Code'),
            'latitude' => __('Latitude'),
            'longitude' => __('Longitude'),
            'pickup_fee' => __('Pickup Fee'),
            'order' => __('Order'),
            'status' => __('Status'),
            'is_featured' => __('Is Featured'),
            'is_pickup_available' => __('Pickup Available'),
        ];
    }
}
