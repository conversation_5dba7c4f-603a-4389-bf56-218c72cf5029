<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ trans('plugins/hyperpay::hyperpay.checkout_title') }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .checkout-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 30px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        .payment-widget {
            margin: 20px 0;
        }
        .loading-spinner {
            text-align: center;
            padding: 40px;
        }
        .error-message {
            color: #dc3545;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="checkout-container">
            <div class="text-center mb-4">
                <h2>{{ trans('plugins/hyperpay::hyperpay.secure_payment') }}</h2>
                <p class="text-muted">{{ trans('plugins/hyperpay::hyperpay.secure_payment_description') }}</p>
            </div>

            @if(isset($checkoutId) && isset($scriptUrl))
                <div class="payment-widget">
                    <script src="{{ $scriptUrl }}"></script>
                    <form
                        action="{{ route('payments.hyperpay.callback', ['checkout_id' => $checkoutId, 'order_id' => $orderId ?? '']) }}"
                        class="paymentWidgets"
                        data-brands="{{ $paymentBrands ?? 'VISA MASTER' }}"
                    ></form>
                </div>

                <div class="text-center mt-4">
                    <small class="text-muted">
                        {{ trans('plugins/hyperpay::hyperpay.powered_by') }}
                        <img src="{{ url('vendor/core/plugins/hyperpay/images/hyperpay-logo.png') }}"
                             alt="HyperPay"
                             style="margin-left: 5px;margin-top: -10px;height: 25px;">
                    </small>
                </div>
            @else
                <div class="error-message text-center">
                    <h4>{{ trans('plugins/hyperpay::hyperpay.checkout_error') }}</h4>
                    <p>{{ trans('plugins/hyperpay::hyperpay.checkout_error_description') }}</p>
                    <a href="{{ url()->previous() }}" class="btn btn-primary">
                        {{ trans('plugins/hyperpay::hyperpay.go_back') }}
                    </a>
                </div>
            @endif
        </div>
    </div>

    <script>
        // Handle payment form submission
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.paymentWidgets');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Show loading state
                    const submitButton = form.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = '{{ trans('plugins/hyperpay::hyperpay.processing') }}...';
                    }
                });
            }
        });
    </script>
</body>
</html>
