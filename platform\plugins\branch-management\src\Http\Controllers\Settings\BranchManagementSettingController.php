<?php

namespace Bo<PERSON>ble\BranchManagement\Http\Controllers\Settings;

use Botble\BranchManagement\Forms\Settings\BranchManagementSettingForm;
use Bo<PERSON>ble\BranchManagement\Http\Requests\Settings\BranchManagementSettingRequest;
use Bo<PERSON>ble\Ecommerce\Http\Controllers\Settings\SettingController;

class BranchManagementSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/branch-management::settings.title'));

        return BranchManagementSettingForm::create()->renderForm();
    }

    public function update(BranchManagementSettingRequest $request)
    {
        return $this->performUpdate($request->validated());
    }
}
