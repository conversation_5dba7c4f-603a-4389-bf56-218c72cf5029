<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use Bo<PERSON>ble\BranchManagement\Http\Controllers\BranchController;
use Botble\BranchManagement\Http\Controllers\Settings\BranchManagementSettingController;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function (): void {
    Route::group(['namespace' => 'Botble\BranchManagement\Http\Controllers'], function (): void {
        Route::group(['prefix' => 'branches', 'as' => 'branches.'], function (): void {
            Route::resource('', BranchController::class)->parameters(['' => 'branch']);
        });

        Route::group(['prefix' => 'ecommerce/settings', 'as' => 'branch-management.settings.'], function (): void {
            Route::get('branch-management', [BranchManagementSettingController::class, 'edit'])->name('edit');
            Route::put('branch-management', [BranchManagementSettingController::class, 'update'])->name('update');
        });
    });
});

// Public routes
Route::group([
    'namespace' => 'Botble\BranchManagement\Http\Controllers\Fronts',
    'middleware' => ['web', 'core'],
], function (): void {
    Route::get('ajax/branches-by-city', 'PublicBranchController@getBranchesByCity')->name('public.ajax.branches-by-city');
    Route::get('ajax/branch-details', 'PublicBranchController@getBranchDetails')->name('public.ajax.branch-details');
    Route::get('ajax/cities-with-branches', 'PublicBranchController@getCitiesWithBranches')->name('public.ajax.cities-with-branches');
    Route::get('branches', 'PublicBranchController@index')->name('public.branches');
});
