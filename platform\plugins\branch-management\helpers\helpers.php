<?php

if (!function_exists('branch_management_get_branches_by_city')) {
    /**
     * Get branches by city ID
     *
     * @param int $cityId
     * @param bool $pickupOnly
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function branch_management_get_branches_by_city(int $cityId, bool $pickupOnly = false)
    {
        return app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class)
            ->getBranchesByCity($cityId, $pickupOnly);
    }
}

if (!function_exists('branch_management_get_featured_branches')) {
    /**
     * Get featured branches
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function branch_management_get_featured_branches(int $limit = 10)
    {
        return app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class)
            ->getFeaturedBranches($limit);
    }
}

if (!function_exists('branch_management_get_pickup_available_branches')) {
    /**
     * Get branches with pickup available
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function branch_management_get_pickup_available_branches()
    {
        return app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class)
            ->getPickupAvailableBranches();
    }
}

if (!function_exists('branch_management_get_branches_grouped_by_city')) {
    /**
     * Get branches grouped by city
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function branch_management_get_branches_grouped_by_city()
    {
        return app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class)
            ->getBranchesGroupedByCity();
    }
}

if (!function_exists('branch_management_is_pickup_enabled')) {
    /**
     * Check if branch pickup is enabled
     *
     * @return bool
     */
    function branch_management_is_pickup_enabled(): bool
    {
        return (bool) get_ecommerce_setting('branch_management_enable_pickup', true);
    }
}

if (!function_exists('branch_management_get_branch_by_id')) {
    /**
     * Get branch by ID
     *
     * @param int $branchId
     * @return \Botble\BranchManagement\Models\Branch|null
     */
    function branch_management_get_branch_by_id(int $branchId)
    {
        return app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class)
            ->findById($branchId);
    }
}
