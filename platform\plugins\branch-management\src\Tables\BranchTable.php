<?php

namespace Bo<PERSON><PERSON>\BranchManagement\Tables;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\BranchManagement\Models\Branch;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class BranchTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Branch::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('branches.create'))
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('branches.edit'),
                Column::make('city_name')
                    ->title(__('City'))
                    ->orderable(false)
                    ->searchable(false),
                Column::make('address')
                    ->title(__('Address'))
                    ->orderable(false)
                    ->limit(50),
                Column::make('phone')
                    ->title(__('Phone'))
                    ->orderable(false),
                Column::make('is_pickup_available')
                    ->title(__('Pickup Available'))
                    ->orderable(false)
                    ->searchable(false),
                StatusColumn::make(),
                CreatedAtColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('branches.edit'),
                DeleteAction::make()->route('branches.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('branches.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'address',
                        'phone',
                        'city_id',
                        'is_pickup_available',
                        'status',
                        'created_at',
                    ])
                    ->with(['city:id,name']);
            });
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('city_name', function (Branch $item) {
                return $item->city->name ?? '--';
            })
            ->editColumn('is_pickup_available', function (Branch $item) {
                return $item->is_pickup_available 
                    ? Html::tag('span', __('Yes'), ['class' => 'text-success'])
                    : Html::tag('span', __('No'), ['class' => 'text-danger']);
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'address',
                'phone',
                'city_id',
                'is_pickup_available',
                'status',
                'created_at',
            ])
            ->with(['city:id,name']);

        return $this->applyScopes($query);
    }
}
