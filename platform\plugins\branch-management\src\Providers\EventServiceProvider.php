<?php

namespace Bo<PERSON><PERSON>\BranchManagement\Providers;

use Bo<PERSON>ble\Ecommerce\Events\OrderPlacedEvent;
use Bo<PERSON>ble\BranchManagement\Listeners\ProcessBranchPickupOrder;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        OrderPlacedEvent::class => [
            ProcessBranchPickupOrder::class,
        ],
    ];
}
