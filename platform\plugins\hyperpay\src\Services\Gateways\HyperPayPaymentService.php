<?php

namespace <PERSON>haqi\HyperPay\Services\Gateways;

use Shaqi\HyperPay\Services\Abstracts\HyperPayPaymentAbstract;
use <PERSON>haqi\HyperPay\Services\HyperPayApiService;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HyperPayPaymentService extends HyperPayPaymentAbstract
{
    /**
     * @var HyperPayApiService
     */
    protected $apiService;

    public function __construct()
    {
        $this->apiService = app(HyperPayApiService::class);
    }
    public function makePayment(array $data): ?string
    {
        $this->amount = $data['amount'];
        $this->currency = strtoupper($data['currency']);

        try {
            // Log the incoming data for debugging
            Log::info('HyperPay makePayment called with data:', [
                'amount' => $this->amount,
                'currency' => $this->currency,
                'order_id' => $data['order_id'] ?? 'not_set',
                'payment_type' => $data['payment_type'] ?? 'visa',
                'data_keys' => array_keys($data),
                'address_data' => Arr::get($data, 'address', 'no_address_data')
            ]);

            // Fix order_id - ensure it's a string, not an array
            $orderId = $data['order_id'] ?? uniqid('order_');

            // Handle array case safely
            if (is_array($orderId)) {
                if (!empty($orderId) && count($orderId) > 0) {
                    $firstElement = reset($orderId); // Get first element safely
                    $orderId = is_numeric($firstElement) ? (string) $firstElement : uniqid('order_');
                } else {
                    $orderId = uniqid('order_');
                }
            } else {
                $orderId = (string) $orderId;
            }

            $checkoutData = $this->createCheckout([
                'amount' => $this->amount,
                'currency' => $this->currency,
                'order_id' => $orderId,
                'payment_type' => $data['payment_type'] ?? 'visa',
                // Try multiple possible locations for customer data
                'customer_email' => Arr::get($data, 'address.email')
                    ?: Arr::get($data, 'customer_email')
                    ?: Arr::get($data, 'email'),
                'customer_name' => Arr::get($data, 'address.name')
                    ?: Arr::get($data, 'customer_name')
                    ?: Arr::get($data, 'name')
                    ?: 'Customer',
                'billing_address' => Arr::get($data, 'address.address')
                    ?: Arr::get($data, 'address')
                    ?: 'N/A',
                'billing_city' => Arr::get($data, 'address.city')
                    ?: Arr::get($data, 'city')
                    ?: 'Riyadh',
                'billing_country' => Arr::get($data, 'address.country')
                    ?: Arr::get($data, 'country')
                    ?: 'SA',
                'billing_postcode' => Arr::get($data, 'address.zip')
                    ?: Arr::get($data, 'postcode')
                    ?: '11111',
            ]);

            if (isset($checkoutData['id'])) {
                $this->checkoutId = $checkoutData['id'];
                return $this->checkoutId;
            }

            throw new Exception('Failed to create HyperPay checkout');
        } catch (Exception $e) {
            Log::error('HyperPay Payment Error', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            $this->setErrorMessage($e->getMessage());
            return null;
        }
    }

    public function afterMakePayment(array $data): ?string
    {
        $request = request();
        $resourcePath = $request->get('resourcePath');
        $checkoutId = $request->get('id');

        if (!$resourcePath || !$checkoutId) {
            $this->setErrorMessage('Missing payment result parameters');
            return null;
        }

        try {
            $paymentResult = $this->getPaymentStatus($resourcePath, $checkoutId);

            // Check if there's an error in the response
            if (isset($paymentResult['error']) && $paymentResult['error']) {
                $this->setErrorMessage($paymentResult['message'] ?? 'Payment status check failed');
                return null;
            }

            if (!isset($paymentResult['result']['code'])) {
                $this->setErrorMessage('Invalid payment result response');
                return null;
            }

            $resultCode = $paymentResult['result']['code'];
            $resultDescription = $paymentResult['result']['description'] ?? 'Unknown error';
            $status = PaymentStatusEnum::FAILED;
            $chargeId = $paymentResult['id'] ?? $checkoutId;

            Log::info('HyperPay Payment Result Analysis', [
                'result_code' => $resultCode,
                'result_description' => $resultDescription,
                'checkout_id' => $checkoutId,
                'charge_id' => $chargeId
            ]);

            if ($this->isSuccessfulPayment($resultCode)) {
                $status = PaymentStatusEnum::COMPLETED;
            } elseif ($this->isPendingPayment($resultCode)) {
                $status = PaymentStatusEnum::PENDING;
                 return null;
            } else {
                // Set a more descriptive error message for failed payments
                $this->setErrorMessage("Payment failed: {$resultDescription} (Code: {$resultCode})");
                 return null;
            }

            // Get order information from session
            $orderId = session('hyperpay_order_id');
            $amount = session('hyperpay_amount', 0);
            $currency = session('hyperpay_currency', 'SAR');
            $customerId = session('hyperpay_customer_id');
            $customerType = session('hyperpay_customer_type');

            // Ensure order_id is in array format for the hook
            $orderIds = is_array($orderId) ? $orderId : [$orderId];

            Log::info('HyperPay Processing Payment Hook', [
                'order_id' => $orderId,
                'order_ids' => $orderIds,
                'amount' => $amount,
                'currency' => $currency,
                'status' => $status,
                'charge_id' => $chargeId,
                'customer_id' => $customerId,
                'customer_type' => $customerType
            ]);

            // Process the payment - this will save transaction and update order status
            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'amount' => $amount,
                'currency' => $currency,
                'charge_id' => $chargeId,
                'order_id' => $orderIds,
                'customer_id' => $customerId,
                'customer_type' => $customerType,
                'payment_channel' => HYPERPAY_PAYMENT_METHOD_NAME,
                'status' => $status,
                'payment_response' => $paymentResult,
            ]);

            // Only return charge ID for successful payments
            // Session will be cleared by the controller after redirect
            if ($status === PaymentStatusEnum::COMPLETED) {
                return $chargeId;
            }

            // For failed or pending payments, return null
            return null;
        } catch (Exception $e) {
            Log::error('HyperPay After Payment Error', [
                'error' => $e->getMessage(),
                'checkout_id' => $checkoutId,
                'resource_path' => $resourcePath
            ]);
            $this->setErrorMessage($e->getMessage());
            return null;
        }
    }

    public function getPaymentDetails(string $chargeId): ?array
    {
        try {
            // For HyperPay, we would need to implement a payment details retrieval
            // This would typically involve calling their API with the transaction ID
            $url = $this->getApiUrl() . '/payments/' . $chargeId;
            $accessToken = $this->getAccessToken();

            if (!$accessToken) {
                return null;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get($url);

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (Exception $e) {
            Log::error('HyperPay Get Payment Details Error', [
                'error' => $e->getMessage(),
                'charge_id' => $chargeId
            ]);
            return null;
        }
    }

    public function isValidToProcessCheckout(): bool
    {
        return apply_filters('hyperpay_is_valid_to_process_checkout', $this->isAvailable());
    }

    public function getOrderNotes(): array
    {
        return apply_filters('hyperpay_order_notes', []);
    }

    public function isAvailable(): bool
    {
        $accessToken = get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
        $visaEntityId = get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);

        return !empty($accessToken) && !empty($visaEntityId);
    }

    public function getCheckoutUrl(string $checkoutId): string
    {
        $isSandbox = get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);

        $baseUrl = $isSandbox
            ? 'https://eu-test.oppwa.com/v1/paymentWidgets.js'
            : 'https://eu-prod.oppwa.com/v1/paymentWidgets.js';

        return $baseUrl . '?checkoutId=' . $checkoutId;
    }

    public function getShopperResultUrl(): string
    {
        return PaymentHelper::getRedirectURL();
    }

    public function getSupportedPaymentTypes(): array
    {
        return [
            'visa' => 'Visa/Mastercard',
            'mada' => 'Mada',
            'amex' => 'American Express',
            'applepay' => 'Apple Pay',
        ];
    }

    public function getAvailablePaymentTypes(): array
    {
        $availableTypes = [];
        $supportedTypes = $this->getSupportedPaymentTypes();

        foreach ($supportedTypes as $type => $label) {
            $entityId = $this->getEntityId($type);
            if (!empty($entityId)) {
                $availableTypes[$type] = $label;
            }
        }

        return $availableTypes;
    }

    /**
     * Create checkout using the new API service directly.
     *
     * @param  array  $data
     * @return array
     */
    public function createCheckoutWithApiService(array $data): array
    {
        try {
            // Extract customer information from address data (like other plugins)
            $customerEmail = $data['customer_email'] ??
                            (isset($data['address']['email']) ? $data['address']['email'] : null);

            $customerName = $data['customer_name'] ??
                           (isset($data['address']['name']) ? $data['address']['name'] : null);

            // Prepare trackable data with customer information
            $trackableData = [
                'amount' => $data['amount'],
                'order_id' => $data['order_id'] ?? uniqid('order_'),
                'payment_type' => $data['payment_type'] ?? 'visa',
                'customer_id' => $data['customer_id'] ?? null,
                'customer_type' => $data['customer_type'] ?? null,
                'customer_email' => $customerEmail,
                'customer_name' => $customerName,
            ];

            // Set redirect URL
            $this->apiService->addRedirectUrl(route('payments.hyperpay.callback'));

            // Create checkout using the new API service (no user model needed)
            $result = $this->apiService->checkout(
                $trackableData,
                null, // Don't pass user model - use customer data from trackableData
                $data['amount'],
                $data['payment_type'] ?? 'visa',
                request()
            );

            Log::info('HyperPay API Service Checkout Result', [
                'result' => $result,
                'data' => $data
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('HyperPay API Service Checkout Error', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Check payment status using the new API service.
     *
     * @param  string  $resourcePath
     * @param  string  $checkoutId
     * @return array
     */
    public function checkPaymentStatusWithApiService(string $resourcePath, string $checkoutId): array
    {
        try {
            $result = $this->apiService->paymentStatus($resourcePath, $checkoutId);

            Log::info('HyperPay API Service Payment Status Result', [
                'result' => $result,
                'resource_path' => $resourcePath,
                'checkout_id' => $checkoutId
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('HyperPay API Service Payment Status Error', [
                'error' => $e->getMessage(),
                'resource_path' => $resourcePath,
                'checkout_id' => $checkoutId
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
}
