/**
 * Branch Management - Checkout Page JavaScript
 */

window.BranchCheckoutManager = class BranchCheckoutManager {
    constructor() {
        this.branchPickupSection = document.getElementById('branch-pickup-section');
        this.citySelect = document.getElementById('pickup_city_id');
        this.branchSelect = document.getElementById('pickup_branch_id');
        this.branchDetails = document.getElementById('selected-branch-details');
        this.branchLoading = document.getElementById('branch-loading');
        this.ajaxUrl = this.branchPickupSection?.dataset.ajaxUrl;
        this.citiesLoaded = false; // Flag to prevent repeated city loading
        this.isLoadingBranches = false; // Flag to prevent duplicate AJAX requests
        this.lastCityId = null; // Track last city ID to prevent duplicate requests

        this.init();
    }

    init() {
        this.setupShippingMethodListeners();
        this.setupCitySelectListener();
        this.setupBranchSelectListener();
        this.setupFormValidation();
        this.initialToggle();
    }

    setupShippingMethodListeners() {
        // Listen for shipping method changes - enhanced to catch all variations
        document.addEventListener('change', (e) => {
            if (e.target && (
                e.target.name === 'shipping_method' ||
                e.target.name === 'shipping_option' ||
                e.target.name.startsWith('shipping_method') ||
                e.target.name.startsWith('shipping_option')
            )) {
                setTimeout(() => {
                    this.toggleBranchPickupSection();
                }, 100);
            }
        });

        // Also listen for click events on shipping method labels and radio buttons
        document.addEventListener('click', (e) => {
            if (e.target) {
                // Direct radio button click
                if (e.target.type === 'radio' && (
                    e.target.name === 'shipping_method' ||
                    e.target.name === 'shipping_option' ||
                    e.target.name.startsWith('shipping_method') ||
                    e.target.name.startsWith('shipping_option')
                )) {
                    setTimeout(() => {
                        this.toggleBranchPickupSection();
                    }, 150);
                }

                // Label click that might contain radio button
                if (e.target.closest('label')) {
                    const input = e.target.closest('label').querySelector('input[name*="shipping"]');
                    if (input) {
                        setTimeout(() => {
                            this.toggleBranchPickupSection();
                        }, 200);
                    }
                }
            }
        });

        // Listen for any changes in the shipping methods area
        const shippingArea = document.querySelector('[data-bb-toggle="checkout-shipping-methods-area"]');
        if (shippingArea) {
            console.log('Found shipping area, setting up mutation observer...');
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' || mutation.type === 'attributes') {
                        console.log('Shipping area mutation detected');
                        setTimeout(() => {
                            this.toggleBranchPickupSection();
                        }, 300);
                    }
                });
            });

            observer.observe(shippingArea, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['checked']
            });
        }
    }

    setupCitySelectListener() {
        if (this.citySelect) {
            this.citySelect.addEventListener('change', (e) => {
                this.handleCitySelection(e.target.value);
            });
        }
    }

    setupBranchSelectListener() {
        if (this.branchSelect) {
            this.branchSelect.addEventListener('change', (e) => {
                this.handleBranchSelection(e.target.value);
            });
        }
    }

    initialToggle() {
        console.log('Running initial toggle check...');

        // Multiple initial checks with increasing delays to catch dynamic content
        setTimeout(() => {
            console.log('Initial check 1 (500ms)');
            this.toggleBranchPickupSection();
        }, 500);

        setTimeout(() => {
            console.log('Initial check 2 (1000ms)');
            this.toggleBranchPickupSection();
        }, 1000);

        setTimeout(() => {
            console.log('Initial check 3 (2000ms)');
            this.toggleBranchPickupSection();
        }, 2000);

        // Also check every 3 seconds for the first 15 seconds (fallback for dynamic loading)
        let checkCount = 0;
        const intervalCheck = setInterval(() => {
            checkCount++;
            console.log(`Interval check #${checkCount} (${checkCount * 3}s)`);
            this.toggleBranchPickupSection();

            if (checkCount >= 5) {
                clearInterval(intervalCheck);
                console.log('Stopped interval checks');
            }
        }, 3000);
    }

    toggleBranchPickupSection() {
        // Try multiple selectors to find shipping method inputs
        const selectedShippingMethod = document.querySelector('input[name="shipping_method"]:checked') ||
                                      document.querySelector('input[name^="shipping_method"]:checked');
        const selectedShippingOption = document.querySelector('input[name="shipping_option"]:checked') ||
                                      document.querySelector('input[name^="shipping_option"]:checked');

        // Debug logging
        console.log('Branch pickup detection - Selected method:', selectedShippingMethod?.value);

        // Check if branch pickup is selected (multiple possible formats)
        let isBranchPickup = false;

        if (selectedShippingMethod) {
            const methodValue = selectedShippingMethod.value.toLowerCase();
            isBranchPickup = methodValue === 'branch_pickup' ||
                           methodValue === 'pickup_branch' ||
                           methodValue.includes('pickup') ||
                           methodValue.includes('branch') ||
                           methodValue === 'branch-pickup' ||
                           methodValue === 'pickup-branch';
        }

        if (!isBranchPickup && selectedShippingOption) {
            const optionValue = selectedShippingOption.value.toLowerCase();
            isBranchPickup = optionValue === 'pickup_branch' ||
                           optionValue === 'branch_pickup' ||
                           optionValue.includes('pickup') ||
                           optionValue.includes('branch') ||
                           optionValue === 'branch-pickup' ||
                           optionValue === 'pickup-branch';
        }

        console.log('Is branch pickup:', isBranchPickup);

        if (isBranchPickup) {
            this.showBranchPickupSection();
        } else {
            this.hideBranchPickupSection();
        }
    }

    showBranchPickupSection() {
        if (this.branchPickupSection) {
            this.branchPickupSection.style.display = 'block';
            // Only load cities if not already loaded
            if (!this.citiesLoaded) {
                this.loadCitiesWithBranches();
            }
        }
    }

    hideBranchPickupSection() {
        if (this.branchPickupSection) {
            this.branchPickupSection.style.display = 'none';
        }
        if (this.branchDetails) {
            this.branchDetails.style.display = 'none';
        }
    }

    async loadCitiesWithBranches() {
        if (!this.citySelect) return;

        if (this.citiesLoaded) {
            console.log('Cities already loaded, skipping...');
            return;
        }

        // Check if cities are already pre-loaded server-side
        const existingOptions = Array.from(this.citySelect.querySelectorAll('option')).filter(option => option.value !== '');
        if (existingOptions.length > 0) {
            console.log('Cities already pre-loaded server-side:', existingOptions.length);
            this.citiesLoaded = true;
            return;
        }

        // Fallback: load cities via AJAX if server-side pre-loading failed
        try {
            console.log('No cities pre-loaded, attempting AJAX fallback...');
            const citiesUrl = this.ajaxUrl.replace('branches-by-city', 'cities-with-branches');
            const response = await fetch(citiesUrl);
            const cities = await response.json();

            this.populateCitySelect(cities);
            this.citiesLoaded = true;
        } catch (error) {
            console.error('Error loading cities via AJAX:', error);
            // Final fallback: try to get cities from the existing address form
            this.loadCitiesFromAddressForm();
            this.citiesLoaded = true;
        }
    }

    populateCitySelect(cities) {
        if (!this.citySelect) return;

        this.citySelect.innerHTML = '<option value="">Select a city...</option>';

        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.name;
            this.citySelect.appendChild(option);
        });
    }

    loadCitiesFromAddressForm() {
        // Fallback: try to get cities from the main address form
        const addressCitySelect = document.querySelector('select[name="address[city]"], select[name="city_id"]');
        if (addressCitySelect && this.citySelect) {
            // Copy options from address form city select
            const options = Array.from(addressCitySelect.options);
            this.citySelect.innerHTML = '';

            options.forEach(option => {
                const newOption = document.createElement('option');
                newOption.value = option.value;
                newOption.textContent = option.textContent;
                this.citySelect.appendChild(newOption);
            });
        }
    }

    handleCitySelection(cityId) {
        // Update hidden field
        const hiddenCityField = document.getElementById('pickup_city_id_hidden');
        if (hiddenCityField) {
            hiddenCityField.value = cityId || '';
        }

        if (!cityId) {
            this.resetBranchSelect();
            this.hideBranchDetails();
            this.lastCityId = null;
            return;
        }

        // Prevent duplicate requests for the same city
        if (this.lastCityId === cityId || this.isLoadingBranches) {
            console.log('Skipping duplicate city selection request for city:', cityId);
            return;
        }

        this.loadBranchesByCity(cityId);
    }

    async loadBranchesByCity(cityId = null) {
        // Get city ID from parameter or from the city select
        if (!cityId && this.citySelect) {
            cityId = this.citySelect.value;
        }

        if (!cityId || !this.branchSelect) {
            this.resetBranchSelect();
            return;
        }

        // Set loading flags to prevent duplicate requests
        this.isLoadingBranches = true;
        this.lastCityId = cityId;
        this.showLoading();

        try {
            console.log('Loading branches for city:', cityId);
            const response = await fetch(`${this.ajaxUrl}?city_id=${cityId}&pickup_only=1`);
            const branches = await response.json();

            this.populateBranchSelect(branches);
        } catch (error) {
            console.error('Error loading branches:', error);
            this.showError('Failed to load branches. Please try again.');
        } finally {
            this.hideLoading();
            this.isLoadingBranches = false;
        }
    }

    resetBranchSelect() {
        if (this.branchSelect) {
            this.branchSelect.innerHTML = '<option value="">Please select a city first</option>';
            this.branchSelect.disabled = true;
        }
    }

    populateBranchSelect(branches) {
        if (!this.branchSelect) return;

        // Clear existing options
        this.branchSelect.innerHTML = '<option value="">Select a branch...</option>';

        if (branches.length === 0) {
            this.branchSelect.innerHTML = '<option value="">No branches available in this city</option>';
            this.branchSelect.disabled = true;
            return;
        }

        this.branchSelect.disabled = false;

        branches.forEach(branch => {
            if (branch.is_pickup_available) {
                const option = document.createElement('option');
                option.value = branch.id;
                option.textContent = `${branch.name}`;
                this.branchSelect.appendChild(option);
            }
        });
    }

    showLoading() {
        if (this.branchLoading) {
            this.branchLoading.style.display = 'block';
        }
        if (this.branchSelect) {
            this.branchSelect.disabled = true;
        }
    }

    hideLoading() {
        if (this.branchLoading) {
            this.branchLoading.style.display = 'none';
        }
    }

    showError(message) {
        if (this.branchSelect) {
            this.branchSelect.innerHTML = `<option value="">${message}</option>`;
            this.branchSelect.disabled = true;
        }
    }

    async handleBranchSelection(branchId) {
        // Update hidden field
        const hiddenBranchField = document.getElementById('pickup_branch_id_hidden');
        if (hiddenBranchField) {
            hiddenBranchField.value = branchId || '';
        }

        if (!branchId) {
            this.hideBranchDetails();
            return;
        }

        try {
            const detailsUrl = this.ajaxUrl.replace('branches-by-city', 'branch-details');
            const response = await fetch(`${detailsUrl}?branch_id=${branchId}`);
            const branch = await response.json();

            if (branch.error) {
                this.showError(branch.error);
                return;
            }

            this.showBranchDetails(branch);
        } catch (error) {
            console.error('Error loading branch details:', error);
            this.showError('Failed to load branch details. Please try again.');
        }
    }

    showBranchDetails(branch) {
        if (!this.branchDetails) return;

        const detailsHtml = `
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fa fa-store"></i> ${branch.name}
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Address:</strong><br>${branch.full_address}</p>
                            ${branch.phone ? `<p><strong>Phone:</strong><br>${branch.phone}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                        </div>
                    </div>
                    ${branch.special_instructions ? `
                        <div class="mt-2">
                            <strong>Special Instructions:</strong>
                            <p class="text-muted">${branch.special_instructions}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        this.branchDetails.innerHTML = detailsHtml;
        this.branchDetails.style.display = 'block';
    }

    hideBranchDetails() {
        if (this.branchDetails) {
            this.branchDetails.style.display = 'none';
        }
    }

    setupFormValidation() {
        console.log('Setting up form validation...');

        // Try multiple form selectors to catch the checkout form
        const possibleSelectors = [
            'form.checkout-form',
            'form.payment-checkout-form',
            'form#checkout-form',
            'form[id*="checkout"]',
            'form[class*="checkout"]'
        ];

        let checkoutForm = null;
        for (const selector of possibleSelectors) {
            checkoutForm = document.querySelector(selector);
            if (checkoutForm) {
                console.log('Found checkout form with selector:', selector);
                break;
            }
        }

        if (checkoutForm) {
            // Add validation event listener with high priority
            checkoutForm.addEventListener('submit', (e) => {
                console.log('Form submit detected, validating branch pickup...');
                if (!this.validateBranchPickup()) {
                    console.log('Validation failed, preventing form submission');
                    e.preventDefault();
                    e.stopImmediatePropagation();
                    return false;
                }
                console.log('Validation passed, allowing form submission');
            }, true); // Use capture phase for higher priority

            console.log('Form validation listener attached successfully');
        }

        // ALWAYS attach document-level listener as backup
        document.addEventListener('submit', (e) => {
            if (e.target && (
                e.target.classList.contains('checkout-form') ||
                e.target.classList.contains('payment-checkout-form') ||
                e.target.id.includes('checkout') ||
                e.target.tagName === 'FORM'
            )) {
                console.log('Form submit detected via document listener, validating...');
                if (!this.validateBranchPickup()) {
                    console.log('Validation failed, preventing form submission via document listener');
                    e.preventDefault();
                    e.stopImmediatePropagation();
                    return false;
                }
            }
        }, true); // Use capture phase

        // Also attach to all submit buttons with more aggressive selectors
        const submitSelectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            '.btn-checkout',
            '.checkout-btn',
            '.btn-place-order',
            '.place-order-btn',
            'button:contains("Place Order")',
            'button:contains("Complete Order")',
            'button:contains("Checkout")',
            '[data-bb-toggle="place-order"]',
            '.payment-checkout-btn'
        ];

        submitSelectors.forEach(selector => {
            try {
                const buttons = document.querySelectorAll(selector);
                buttons.forEach(button => {
                    console.log('Attaching validation to button:', button.textContent?.trim());
                    button.addEventListener('click', (e) => {
                        console.log('Submit button clicked:', button.textContent?.trim());
                        if (!this.validateBranchPickup()) {
                            console.log('Validation failed, preventing button click');
                            e.preventDefault();
                            e.stopImmediatePropagation();
                            return false;
                        }
                    }, true);
                });
            } catch (e) {
                // Ignore invalid selectors
            }
        });

        // Also use MutationObserver to catch dynamically added buttons
        const buttonObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        // Check if the node itself is a button
                        if (node.tagName === 'BUTTON' || node.type === 'submit') {
                            this.attachValidationToButton(node);
                        }
                        // Check for buttons within the node
                        const buttons = node.querySelectorAll && node.querySelectorAll('button, input[type="submit"]');
                        if (buttons) {
                            buttons.forEach(button => this.attachValidationToButton(button));
                        }
                    }
                });
            });
        });
        buttonObserver.observe(document.body, { childList: true, subtree: true });

        console.log('All validation listeners attached');

        // Hook into jQuery if available
        if (window.jQuery) {
            console.log('jQuery detected, hooking into jQuery submit events...');
            window.jQuery(document).on('submit', 'form', (e) => {
                const form = e.target;
                if (form && (
                    form.classList.contains('checkout-form') ||
                    form.classList.contains('payment-checkout-form') ||
                    form.id.includes('checkout')
                )) {
                    console.log('jQuery form submit detected, validating...');
                    if (!this.validateBranchPickup()) {
                        console.log('Validation failed, preventing jQuery form submission');
                        e.preventDefault();
                        e.stopImmediatePropagation();
                        return false;
                    }
                }
            });
        }

        // Monitor for new forms being added dynamically
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1 && node.tagName === 'FORM') {
                            console.log('New form detected, attaching validation...');
                            this.attachValidationToForm(node);
                        }
                    });
                }
            });
        });
        observer.observe(document.body, { childList: true, subtree: true });

        // Hook into any AJAX form submissions
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('checkout')) {
                console.log('AJAX checkout request detected, validating...');
                if (!window.branchCheckoutManager.validateBranchPickup()) {
                    console.log('Validation failed, blocking AJAX request');
                    return Promise.reject(new Error('Branch pickup validation failed'));
                }
            }
            return originalFetch.apply(this, args);
        };
    }

    attachValidationToForm(form) {
        form.addEventListener('submit', (e) => {
            console.log('Dynamic form submit detected, validating...');
            if (!this.validateBranchPickup()) {
                console.log('Validation failed, preventing dynamic form submission');
                e.preventDefault();
                e.stopImmediatePropagation();
                return false;
            }
        }, true);
    }

    attachValidationToButton(button) {
        console.log('Attaching validation to dynamic button:', button.textContent?.trim());
        button.addEventListener('click', (e) => {
            console.log('Dynamic button clicked:', button.textContent?.trim());
            if (!this.validateBranchPickup()) {
                console.log('Validation failed, preventing dynamic button click');
                e.preventDefault();
                e.stopImmediatePropagation();
                return false;
            }
        }, true);
    }

    validateBranchPickup() {
        console.log('=== BRANCH PICKUP VALIDATION ===');

        // Try multiple selectors for shipping method
        const selectedShippingMethod = document.querySelector('input[name="shipping_method"]:checked') ||
                                      document.querySelector('input[name^="shipping_method"]:checked');
        const selectedShippingOption = document.querySelector('input[name="shipping_option"]:checked') ||
                                      document.querySelector('input[name^="shipping_option"]:checked');

        console.log('Selected shipping method for validation:', selectedShippingMethod?.value);
        console.log('Selected shipping option for validation:', selectedShippingOption?.value);

        // Check if branch pickup is selected using the same logic as toggleBranchPickupSection
        let isBranchPickup = false;

        if (selectedShippingMethod) {
            const methodValue = selectedShippingMethod.value.toLowerCase();
            isBranchPickup = methodValue === 'branch_pickup' ||
                           methodValue === 'pickup_branch' ||
                           methodValue.includes('pickup') ||
                           methodValue.includes('branch') ||
                           methodValue === 'branch-pickup' ||
                           methodValue === 'pickup-branch';
        }

        if (!isBranchPickup && selectedShippingOption) {
            const optionValue = selectedShippingOption.value.toLowerCase();
            isBranchPickup = optionValue === 'pickup_branch' ||
                           optionValue === 'branch_pickup' ||
                           optionValue.includes('pickup') ||
                           optionValue.includes('branch') ||
                           optionValue === 'branch-pickup' ||
                           optionValue === 'pickup-branch';
        }

        console.log('Is branch pickup selected for validation:', isBranchPickup);

        if (isBranchPickup) {
            console.log('Branch pickup detected, checking city and branch selection...');

            // Check both visible and hidden fields
            const branchId = document.getElementById('pickup_branch_id_hidden')?.value ||
                           document.getElementById('pickup_branch_id')?.value;
            const cityId = document.getElementById('pickup_city_id_hidden')?.value ||
                         document.getElementById('pickup_city_id')?.value;

            console.log('City ID:', cityId);
            console.log('Branch ID:', branchId);

            if (!cityId || cityId === '') {
                console.log('City validation failed');
                this.showValidationError('آپ کو برانچ پک اپ کے لیے شہر منتخب کرنا ضروری ہے۔ / Please select a city for branch pickup.');
                this.focusElement('pickup_city_id');
                return false;
            }

            if (!branchId || branchId === '') {
                console.log('Branch validation failed');
                this.showValidationError('آپ کو پک اپ کے لیے برانچ منتخب کرنا ضروری ہے۔ / Please select a branch for pickup.');
                this.focusElement('pickup_branch_id');
                return false;
            }

            console.log('Branch pickup validation passed');
        } else {
            console.log('Branch pickup not selected, skipping validation');
        }

        return true;
    }

    showValidationError(message) {
        // Remove any existing error messages
        const existingError = document.querySelector('.branch-validation-error');
        if (existingError) {
            existingError.remove();
        }

        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger branch-validation-error mt-2';
        errorDiv.innerHTML = `<i class="fa fa-exclamation-triangle"></i> ${message}`;

        if (this.branchPickupSection) {
            this.branchPickupSection.appendChild(errorDiv);

            // Scroll to the error
            errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        } else {
            // Fallback to alert if section not found
            alert(message);
        }
    }

    focusElement(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.focus();
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}

// Auto-initialize when DOM is ready (single initialization to prevent duplicates)
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('branch-pickup-section') && !window.branchCheckoutManager) {
        console.log('Initializing BranchCheckoutManager...');
        window.branchCheckoutManager = new BranchCheckoutManager();
    }
});
