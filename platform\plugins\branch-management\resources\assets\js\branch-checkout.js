/**
 * Branch Management - Checkout Page JavaScript
 */

window.BranchCheckoutManager = class BranchCheckoutManager {
    constructor() {
        this.branchPickupSection = document.getElementById('branch-pickup-section');
        this.citySelect = document.getElementById('pickup_city_id');
        this.branchSelect = document.getElementById('pickup_branch_id');
        this.branchDetails = document.getElementById('selected-branch-details');
        this.branchLoading = document.getElementById('branch-loading');
        this.ajaxUrl = this.branchPickupSection?.dataset.ajaxUrl;
        this.citiesLoaded = false; // Flag to prevent repeated city loading
        this.isLoadingBranches = false; // Flag to prevent duplicate AJAX requests
        this.lastCityId = null; // Track last city ID to prevent duplicate requests

        this.init();
    }

    init() {
        this.setupShippingMethodListeners();
        this.setupCitySelectListener();
        this.setupBranchSelectListener();
        this.setupFormValidation();
        this.initialToggle();
    }

    setupShippingMethodListeners() {
        // Listen for shipping method changes
        document.addEventListener('change', (e) => {
            if (e.target && (e.target.name === 'shipping_method' || e.target.name === 'shipping_option')) {
                this.toggleBranchPickupSection();
            }
        });

        // Also listen for click events on shipping method labels
        document.addEventListener('click', (e) => {
            if (e.target && e.target.closest('label')) {
                const input = e.target.closest('label').querySelector('input[name="shipping_method"], input[name="shipping_option"]');
                if (input) {
                    setTimeout(() => {
                        this.toggleBranchPickupSection();
                    }, 100);
                }
            }
        });
    }

    setupCitySelectListener() {
        if (this.citySelect) {
            this.citySelect.addEventListener('change', (e) => {
                this.handleCitySelection(e.target.value);
            });
        }
    }

    setupBranchSelectListener() {
        if (this.branchSelect) {
            this.branchSelect.addEventListener('change', (e) => {
                this.handleBranchSelection(e.target.value);
            });
        }
    }

    initialToggle() {
        this.toggleBranchPickupSection();
    }

    toggleBranchPickupSection() {
        const selectedShippingMethod = document.querySelector('input[name="shipping_method"]:checked');
        const selectedShippingOption = document.querySelector('input[name="shipping_option"]:checked');

        // Debug logging
        console.log('Selected shipping method:', selectedShippingMethod?.value);
        console.log('Selected shipping option:', selectedShippingOption?.value);

        // Check if branch pickup is selected (multiple possible formats)
        let isBranchPickup = false;

        if (selectedShippingMethod) {
            const methodValue = selectedShippingMethod.value.toLowerCase();
            isBranchPickup = methodValue === 'branch_pickup' ||
                           methodValue.includes('pickup') ||
                           methodValue.includes('branch');
        }

        if (!isBranchPickup && selectedShippingOption) {
            const optionValue = selectedShippingOption.value.toLowerCase();
            isBranchPickup = optionValue === 'pickup_branch' ||
                           optionValue.includes('pickup') ||
                           optionValue.includes('branch');
        }

        console.log('Is branch pickup:', isBranchPickup);

        if (isBranchPickup) {
            this.showBranchPickupSection();
        } else {
            this.hideBranchPickupSection();
        }
    }

    showBranchPickupSection() {
        if (this.branchPickupSection) {
            this.branchPickupSection.style.display = 'block';
            // Only load cities if not already loaded
            if (!this.citiesLoaded) {
                this.loadCitiesWithBranches();
            }
        }
    }

    hideBranchPickupSection() {
        if (this.branchPickupSection) {
            this.branchPickupSection.style.display = 'none';
        }
        if (this.branchDetails) {
            this.branchDetails.style.display = 'none';
        }
    }

    async loadCitiesWithBranches() {
        if (!this.citySelect) return;

        if (this.citiesLoaded) {
            console.log('Cities already loaded, skipping...');
            return;
        }

        // Check if cities are already pre-loaded server-side
        const existingOptions = this.citySelect.querySelectorAll('option[value!=""]');
        if (existingOptions.length > 0) {
            console.log('Cities already pre-loaded server-side:', existingOptions.length);
            this.citiesLoaded = true;
            return;
        }

        // Fallback: load cities via AJAX if server-side pre-loading failed
        try {
            console.log('No cities pre-loaded, attempting AJAX fallback...');
            const citiesUrl = this.ajaxUrl.replace('branches-by-city', 'cities-with-branches');
            const response = await fetch(citiesUrl);
            const cities = await response.json();

            this.populateCitySelect(cities);
            this.citiesLoaded = true;
        } catch (error) {
            console.error('Error loading cities via AJAX:', error);
            // Final fallback: try to get cities from the existing address form
            this.loadCitiesFromAddressForm();
            this.citiesLoaded = true;
        }
    }

    populateCitySelect(cities) {
        if (!this.citySelect) return;

        this.citySelect.innerHTML = '<option value="">Select a city...</option>';

        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.name;
            this.citySelect.appendChild(option);
        });
    }

    loadCitiesFromAddressForm() {
        // Fallback: try to get cities from the main address form
        const addressCitySelect = document.querySelector('select[name="address[city]"], select[name="city_id"]');
        if (addressCitySelect && this.citySelect) {
            // Copy options from address form city select
            const options = Array.from(addressCitySelect.options);
            this.citySelect.innerHTML = '';

            options.forEach(option => {
                const newOption = document.createElement('option');
                newOption.value = option.value;
                newOption.textContent = option.textContent;
                this.citySelect.appendChild(newOption);
            });
        }
    }

    handleCitySelection(cityId) {
        // Update hidden field
        const hiddenCityField = document.getElementById('pickup_city_id_hidden');
        if (hiddenCityField) {
            hiddenCityField.value = cityId || '';
        }

        if (!cityId) {
            this.resetBranchSelect();
            this.hideBranchDetails();
            this.lastCityId = null;
            return;
        }

        // Prevent duplicate requests for the same city
        if (this.lastCityId === cityId || this.isLoadingBranches) {
            console.log('Skipping duplicate city selection request for city:', cityId);
            return;
        }

        this.loadBranchesByCity(cityId);
    }

    async loadBranchesByCity(cityId = null) {
        // Get city ID from parameter or from the city select
        if (!cityId && this.citySelect) {
            cityId = this.citySelect.value;
        }

        if (!cityId || !this.branchSelect) {
            this.resetBranchSelect();
            return;
        }

        // Set loading flags to prevent duplicate requests
        this.isLoadingBranches = true;
        this.lastCityId = cityId;
        this.showLoading();

        try {
            console.log('Loading branches for city:', cityId);
            const response = await fetch(`${this.ajaxUrl}?city_id=${cityId}&pickup_only=1`);
            const branches = await response.json();

            this.populateBranchSelect(branches);
        } catch (error) {
            console.error('Error loading branches:', error);
            this.showError('Failed to load branches. Please try again.');
        } finally {
            this.hideLoading();
            this.isLoadingBranches = false;
        }
    }

    resetBranchSelect() {
        if (this.branchSelect) {
            this.branchSelect.innerHTML = '<option value="">Please select a city first</option>';
            this.branchSelect.disabled = true;
        }
    }

    populateBranchSelect(branches) {
        if (!this.branchSelect) return;

        // Clear existing options
        this.branchSelect.innerHTML = '<option value="">Select a branch...</option>';

        if (branches.length === 0) {
            this.branchSelect.innerHTML = '<option value="">No branches available in this city</option>';
            this.branchSelect.disabled = true;
            return;
        }

        this.branchSelect.disabled = false;

        branches.forEach(branch => {
            if (branch.is_pickup_available) {
                const option = document.createElement('option');
                option.value = branch.id;
                option.textContent = `${branch.name}`;
                this.branchSelect.appendChild(option);
            }
        });
    }

    showLoading() {
        if (this.branchLoading) {
            this.branchLoading.style.display = 'block';
        }
        if (this.branchSelect) {
            this.branchSelect.disabled = true;
        }
    }

    hideLoading() {
        if (this.branchLoading) {
            this.branchLoading.style.display = 'none';
        }
    }

    showError(message) {
        if (this.branchSelect) {
            this.branchSelect.innerHTML = `<option value="">${message}</option>`;
            this.branchSelect.disabled = true;
        }
    }

    async handleBranchSelection(branchId) {
        // Update hidden field
        const hiddenBranchField = document.getElementById('pickup_branch_id_hidden');
        if (hiddenBranchField) {
            hiddenBranchField.value = branchId || '';
        }

        if (!branchId) {
            this.hideBranchDetails();
            return;
        }

        try {
            const detailsUrl = this.ajaxUrl.replace('branches-by-city', 'branch-details');
            const response = await fetch(`${detailsUrl}?branch_id=${branchId}`);
            const branch = await response.json();

            if (branch.error) {
                this.showError(branch.error);
                return;
            }

            this.showBranchDetails(branch);
        } catch (error) {
            console.error('Error loading branch details:', error);
            this.showError('Failed to load branch details. Please try again.');
        }
    }

    showBranchDetails(branch) {
        if (!this.branchDetails) return;

        const detailsHtml = `
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fa fa-store"></i> ${branch.name}
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Address:</strong><br>${branch.full_address}</p>
                            ${branch.phone ? `<p><strong>Phone:</strong><br>${branch.phone}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                        </div>
                    </div>
                    ${branch.special_instructions ? `
                        <div class="mt-2">
                            <strong>Special Instructions:</strong>
                            <p class="text-muted">${branch.special_instructions}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        this.branchDetails.innerHTML = detailsHtml;
        this.branchDetails.style.display = 'block';
    }

    hideBranchDetails() {
        if (this.branchDetails) {
            this.branchDetails.style.display = 'none';
        }
    }

    setupFormValidation() {
        // Add validation when form is submitted
        const checkoutForm = document.querySelector('form.checkout-form');
        if (checkoutForm) {
            checkoutForm.addEventListener('submit', (e) => {
                if (!this.validateBranchPickup()) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    }

    validateBranchPickup() {
        const selectedShippingMethod = document.querySelector('input[name="shipping_method"]:checked');
        const selectedShippingOption = document.querySelector('input[name="shipping_option"]:checked');

        // Check if branch pickup is selected using the same logic as toggleBranchPickupSection
        let isBranchPickup = false;

        if (selectedShippingMethod) {
            const methodValue = selectedShippingMethod.value.toLowerCase();
            isBranchPickup = methodValue === 'branch_pickup' ||
                           methodValue.includes('pickup') ||
                           methodValue.includes('branch');
        }

        if (!isBranchPickup && selectedShippingOption) {
            const optionValue = selectedShippingOption.value.toLowerCase();
            isBranchPickup = optionValue === 'pickup_branch' ||
                           optionValue.includes('pickup') ||
                           optionValue.includes('branch');
        }

        if (isBranchPickup) {
            const branchId = document.getElementById('pickup_branch_id_hidden')?.value;
            const cityId = document.getElementById('pickup_city_id_hidden')?.value;

            if (!cityId) {
                this.showValidationError('Please select a city for branch pickup.');
                this.focusElement('pickup_city_id');
                return false;
            }

            if (!branchId) {
                this.showValidationError('Please select a branch for pickup.');
                this.focusElement('pickup_branch_id');
                return false;
            }
        }

        return true;
    }

    showValidationError(message) {
        // Remove any existing error messages
        const existingError = document.querySelector('.branch-validation-error');
        if (existingError) {
            existingError.remove();
        }

        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger branch-validation-error mt-2';
        errorDiv.innerHTML = `<i class="fa fa-exclamation-triangle"></i> ${message}`;

        if (this.branchPickupSection) {
            this.branchPickupSection.appendChild(errorDiv);

            // Scroll to the error
            errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        } else {
            // Fallback to alert if section not found
            alert(message);
        }
    }

    focusElement(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.focus();
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}

// Auto-initialize when DOM is ready (single initialization to prevent duplicates)
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('branch-pickup-section') && !window.branchCheckoutManager) {
        console.log('Initializing BranchCheckoutManager...');
        window.branchCheckoutManager = new BranchCheckoutManager();
    }
});
