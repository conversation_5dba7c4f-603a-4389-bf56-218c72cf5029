@if(get_ecommerce_setting('branch_pickup_status', 1))
<div id="branch-pickup-section" class="branch-pickup-section mt-3" style="display: none;" data-ajax-url="{{ route('public.ajax.branches-by-city') }}">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fa fa-store"></i> {{ trans('plugins/branch-management::branch.pickup.title') }}
            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3">{{ trans('plugins/branch-management::branch.pickup.checkout_description') }}</p>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="pickup_city_id" class="form-label required">{{ trans('plugins/branch-management::branch.pickup.select_city_label') }}</label>
                        <select id="pickup_city_id" name="pickup_city_id" class="form-control" required>
                            <option value="">{{ trans('plugins/branch-management::branch.pickup.select_city') }}</option>
                            @if(isset($citiesWithBranches) && $citiesWithBranches->isNotEmpty())
                                @foreach($citiesWithBranches as $city)
                                    <option value="{{ $city->id }}">{{ $city->name }}</option>
                                @endforeach
                            @endif
                        </select>
                        <small class="text-muted">{{ trans('plugins/branch-management::branch.pickup.city_help') }}</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="pickup_branch_id" class="form-label required">{{ trans('plugins/branch-management::branch.pickup.select_branch_label') }}</label>
                        <select id="pickup_branch_id" name="pickup_branch_id" class="form-control" disabled required>
                            <option value="">{{ trans('plugins/branch-management::branch.pickup.select_city_first') }}</option>
                        </select>
                        <small class="text-muted">{{ trans('plugins/branch-management::branch.pickup.branch_help') }}</small>
                    </div>
                </div>
            </div>

            <div id="selected-branch-details" class="selected-branch-details mt-3" style="display: none;">
                <!-- Branch details will be loaded here via JavaScript -->
            </div>

            <div id="branch-loading" class="text-center mt-3" style="display: none;">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">{{ trans('plugins/branch-management::branch.loading_branches') }}</span>
            </div>

            <!-- Hidden fields to store selected values for form submission -->
            <input type="hidden" name="pickup_city_id" id="pickup_city_id_hidden" value="">
            <input type="hidden" name="pickup_branch_id" id="pickup_branch_id_hidden" value="">
        </div>
    </div>
</div>







<!-- Branch pickup functionality is now handled by the external branch-checkout.js file -->
<script>
// Simple initialization to ensure the external script can find the elements
document.addEventListener('DOMContentLoaded', function() {
    console.log('Branch selection template loaded - external script will handle functionality');

    // Add a temporary debug button for testing
    const debugButton = document.createElement('button');
    debugButton.textContent = 'DEBUG: Show Branch Section';
    debugButton.type = 'button';
    debugButton.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; background: red; color: white; padding: 10px; border: none; cursor: pointer;';
    debugButton.onclick = function() {
        const section = document.getElementById('branch-pickup-section');
        if (section) {
            section.style.display = section.style.display === 'none' ? 'block' : 'none';
            console.log('Branch section toggled. Current display:', section.style.display);

            // Also trigger the manager if it exists
            if (window.branchCheckoutManager) {
                console.log('Triggering branch checkout manager...');
                window.branchCheckoutManager.toggleBranchPickupSection();
            } else {
                console.log('Branch checkout manager not found');
            }
        } else {
            console.log('Branch pickup section not found!');
        }
    };
    document.body.appendChild(debugButton);
});
</script>
@endif
