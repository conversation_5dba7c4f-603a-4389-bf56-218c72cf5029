@if(get_ecommerce_setting('branch_pickup_status', 1))
<div id="branch-pickup-section" class="branch-pickup-section mt-3" style="display: none;" data-ajax-url="{{ route('public.ajax.branches-by-city') }}">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fa fa-store"></i> {{ trans('plugins/branch-management::branch.pickup.title') }}
            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3">{{ trans('plugins/branch-management::branch.pickup.checkout_description') }}</p>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="pickup_city_id" class="form-label required">{{ trans('plugins/branch-management::branch.pickup.select_city_label') }}</label>
                        <select id="pickup_city_id" name="pickup_city_id" class="form-control" required>
                            <option value="">{{ trans('plugins/branch-management::branch.pickup.select_city') }}</option>
                            @if(isset($citiesWithBranches) && $citiesWithBranches->isNotEmpty())
                                @foreach($citiesWithBranches as $city)
                                    <option value="{{ $city->id }}">{{ $city->name }}</option>
                                @endforeach
                            @endif
                        </select>
                        <small class="text-muted">{{ trans('plugins/branch-management::branch.pickup.city_help') }}</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="pickup_branch_id" class="form-label required">{{ trans('plugins/branch-management::branch.pickup.select_branch_label') }}</label>
                        <select id="pickup_branch_id" name="pickup_branch_id" class="form-control" disabled required>
                            <option value="">{{ trans('plugins/branch-management::branch.pickup.select_city_first') }}</option>
                        </select>
                        <small class="text-muted">{{ trans('plugins/branch-management::branch.pickup.branch_help') }}</small>
                    </div>
                </div>
            </div>

            <div id="selected-branch-details" class="selected-branch-details mt-3" style="display: none;">
                <!-- Branch details will be loaded here via JavaScript -->
            </div>

            <div id="branch-loading" class="text-center mt-3" style="display: none;">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">{{ trans('plugins/branch-management::branch.loading_branches') }}</span>
            </div>

            <!-- Hidden fields to store selected values for form submission -->
            <input type="hidden" name="pickup_city_id" id="pickup_city_id_hidden" value="">
            <input type="hidden" name="pickup_branch_id" id="pickup_branch_id_hidden" value="">
        </div>
    </div>
</div>

<script>
// Inline branch pickup functionality for immediate execution
document.addEventListener('DOMContentLoaded', function() {
    const branchPickupSection = document.getElementById('branch-pickup-section');
    const citySelect = document.getElementById('pickup_city_id');
    const branchSelect = document.getElementById('pickup_branch_id');

    if (!branchPickupSection) {
        return;
    }

    // Listen for shipping method changes
    function toggleBranchPickupSection() {
        const allShippingMethods = document.querySelectorAll('input[name="shipping_method"], input[name^="shipping_method"]');
        const allShippingOptions = document.querySelectorAll('input[name="shipping_option"], input[name^="shipping_option"]');

        // Handle different shipping method name formats
        const selectedShippingMethod = document.querySelector('input[name="shipping_method"]:checked') ||
                                      document.querySelector('input[name^="shipping_method"]:checked');
        const selectedShippingOption = document.querySelector('input[name="shipping_option"]:checked') ||
                                      document.querySelector('input[name^="shipping_option"]:checked');

        console.log('=== DEBUG: Selected values ===');
        console.log('Selected shipping method:', selectedShippingMethod?.value);
        console.log('Selected shipping option:', selectedShippingOption?.value);

        let isBranchPickup = false;

        // Check all possible combinations
        if (selectedShippingMethod) {
            const methodValue = selectedShippingMethod.value.toLowerCase();
            console.log('Checking method value:', methodValue);
            isBranchPickup = methodValue === 'branch_pickup' ||
                           methodValue.includes('pickup') ||
                           methodValue.includes('branch') ||
                           methodValue === 'pickup_branch';
        }

        if (!isBranchPickup && selectedShippingOption) {
            const optionValue = selectedShippingOption.value.toLowerCase();
            console.log('Checking option value:', optionValue);
            isBranchPickup = optionValue === 'pickup_branch' ||
                           optionValue.includes('pickup') ||
                           optionValue.includes('branch') ||
                           optionValue === 'branch_pickup';
        }

        console.log('=== DEBUG: Final result ===');
        console.log('Is branch pickup:', isBranchPickup);

        if (isBranchPickup) {
            branchPickupSection.style.display = 'block';
            console.log('Branch pickup section shown');
            ensureCitiesAreLoaded();
        } else {
            branchPickupSection.style.display = 'none';
            console.log('Branch pickup section hidden');
        }
    }

    // Cities are now pre-loaded server-side, no need for AJAX loading
    function ensureCitiesAreLoaded() {
        if (!citySelect) {
            console.log('City select element not found');
            return;
        }

        // Check if cities are already loaded (server-side pre-loading)
        const existingOptions = citySelect.querySelectorAll('option[value!=""]');
        if (existingOptions.length > 0) {
            console.log('Cities already pre-loaded server-side:', existingOptions.length);
            return;
        }

        // Fallback: if no cities were pre-loaded, try to load them via AJAX
        console.log('No cities pre-loaded, attempting AJAX fallback...');
        loadCitiesViaAjax();
    }

    // Fallback function to load cities via AJAX if server-side pre-loading failed
    function loadCitiesViaAjax() {
        const ajaxUrl = branchPickupSection.dataset.ajaxUrl;
        if (!ajaxUrl) {
            console.log('No AJAX URL found for fallback');
            return;
        }

        const citiesUrl = ajaxUrl.replace('branches-by-city', 'cities-with-branches');
        console.log('Loading cities via AJAX fallback:', citiesUrl);

        // Show loading state
        citySelect.innerHTML = '<option value="">Loading cities...</option>';

        fetch(citiesUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(cities => {
                console.log('Cities loaded via AJAX:', cities);
                citySelect.innerHTML = '<option value="">Select a city...</option>';

                if (cities && cities.length > 0) {
                    cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.id;
                        option.textContent = city.name;
                        citySelect.appendChild(option);
                    });
                    console.log('Added', cities.length, 'cities via AJAX');
                } else {
                    console.log('No cities found via AJAX');
                    citySelect.innerHTML = '<option value="">No cities available</option>';
                }
            })
            .catch(error => {
                console.error('Error loading cities via AJAX:', error);
                citySelect.innerHTML = '<option value="">Unable to load cities</option>';
            });
    }

    // Handle city selection
    if (citySelect) {
        citySelect.addEventListener('change', function() {
            const cityId = this.value;
            const hiddenCityField = document.getElementById('pickup_city_id_hidden');
            if (hiddenCityField) {
                hiddenCityField.value = cityId || '';
            }



            if (!cityId || !branchSelect) {
                if (branchSelect) {
                    branchSelect.innerHTML = '<option value="">Please select a city first</option>';
                    branchSelect.disabled = true;
                }
                return;
            }

            const ajaxUrl = branchPickupSection.dataset.ajaxUrl;
            fetch(`${ajaxUrl}?city_id=${cityId}&pickup_only=1`)
                .then(response => response.json())
                .then(branches => {
                    branchSelect.innerHTML = '<option value="">Select a branch...</option>';

                    if (branches.length === 0) {
                        branchSelect.innerHTML = '<option value="">No branches available in this city</option>';
                        branchSelect.disabled = true;
                        return;
                    }

                    branchSelect.disabled = false;
                    branches.forEach(branch => {
                        if (branch.is_pickup_available) {
                            const feeText = branch.pickup_fee > 0 ? ` (Fee: $${branch.pickup_fee})` : ' (Free)';
                            const option = document.createElement('option');
                            option.value = branch.id;
                            option.textContent = `${branch.name}${feeText}`;
                            branchSelect.appendChild(option);
                        }
                    });
                })
                .catch(error => {
                    console.error('Error loading branches:', error);
                    branchSelect.innerHTML = '<option value="">Error loading branches</option>';
                });
        });
    }

    // Handle branch selection
    if (branchSelect) {
        branchSelect.addEventListener('change', function() {
            const branchId = this.value;
            const hiddenBranchField = document.getElementById('pickup_branch_id_hidden');
            if (hiddenBranchField) {
                hiddenBranchField.value = branchId || '';
            }


        });
    }



    // Set up shipping method listeners - multiple approaches
    document.addEventListener('change', function(e) {
        console.log('Change event detected on:', e.target.name, 'value:', e.target.value);
        if (e.target && (e.target.name === 'shipping_method' ||
                        e.target.name === 'shipping_option' ||
                        e.target.name.startsWith('shipping_method') ||
                        e.target.name.startsWith('shipping_option'))) {
            console.log('Shipping method changed, toggling section...');
            setTimeout(toggleBranchPickupSection, 100);
        }
    });

    // Also listen for click events on radio buttons
    document.addEventListener('click', function(e) {
        if (e.target && e.target.type === 'radio' &&
            (e.target.name === 'shipping_method' ||
             e.target.name === 'shipping_option' ||
             e.target.name.startsWith('shipping_method') ||
             e.target.name.startsWith('shipping_option'))) {
            console.log('Radio button clicked:', e.target.name, 'value:', e.target.value);
            setTimeout(toggleBranchPickupSection, 200);
        }
    });

    // Listen for any input changes in shipping area
    const shippingArea = document.querySelector('[data-bb-toggle="checkout-shipping-methods-area"]');
    if (shippingArea) {
        console.log('Found shipping area, adding observer...');
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    console.log('Shipping area changed, checking methods...');
                    setTimeout(toggleBranchPickupSection, 300);
                }
            });
        });

        observer.observe(shippingArea, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['checked']
        });
    }

    // Initial toggle with delay
    setTimeout(toggleBranchPickupSection, 500);

    // Also check every 2 seconds for the first 10 seconds (fallback)
    let checkCount = 0;
    const intervalCheck = setInterval(function() {
        console.log('Interval check #' + (checkCount + 1));
        toggleBranchPickupSection();
        checkCount++;
        if (checkCount >= 5) {
            clearInterval(intervalCheck);
        }
    }, 2000);
});
</script>
@endif
