@php
$status = setting('shipping_aramex_status', 0);
$countryCode = setting('shipping_aramex_account_country_code') ?: '';
$accountEntity = setting('shipping_aramex_account_entity') ?: '';
$accountNumber = setting('shipping_aramex_account_number') ?: '';
$accountPin = setting('shipping_aramex_account_pin') ?: '';
$username = setting('shipping_aramex_username') ?: '';
$password = setting('shipping_aramex_password') ?: '';
$test = setting('shipping_aramex_test', 1) ?: 0;
$customAmount = setting('shipping_aramex_custom_amount', 10) ?: 0;
$availableCountriesAll = setting('shipping_aramex_available_countries_all') ?: '';
$availableCountries = json_decode(setting('shipping_aramex_available_countries') ?: '[]');
$allowedDomesticMethods = json_decode(setting('shipping_aramex_allowed_domestic_method') ?: '[]');
$allowedDomesticAdditionalServices = json_decode(setting('shipping_aramex_allowed_domestic_additional_services') ?: '[]');
$allowedInternationalMethods = json_decode(setting('shipping_aramex_allowed_international_method') ?: '[]');
$allowedInternationalAdditionalServices = json_decode(setting('shipping_aramex_allowed_international_additional_services') ?: '[]');
$countries = Botble\Base\Supports\Helper::countries();


@endphp

<x-core::card class="mt-5">
<x-core::table :striped="false" :hover="false">
    <x-core::table.body>
        <x-core::table.body.cell class="border-end" style="width: 5%">
            <x-core::icon name="ti ti-truck-delivery" />
        </x-core::table.body.cell>
        <x-core::table.body.cell style="width: 20%">
            <img
                class="filter-black"
                src="{{ url('vendor/core/plugins/aramex/images/logo-dark.svg') }}"
                alt="Aramex"
            >
        </x-core::table.body.cell>
        <x-core::table.body.cell>
            <a href="https://aramex.com/" target="_blank" class="fw-semibold">Aramex</a>
            <p class="mb-0">{{ trans('plugins/aramex::aramex.description') }}</p>
        </x-core::table.body.cell>
        <x-core::table.body.row class="bg-white">
            <x-core::table.body.cell colspan="3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div @class(['payment-name-label-group', 'd-none' => ! $status])>
                            <span class="payment-note v-a-t">{{ trans('plugins/payment::payment.use') }}:</span>
                            <label class="ws-nm inline-display method-name-label">Aramex</label>
                        </div>
                    </div>

                    <x-core::button
                        data-bs-toggle="collapse"
                        href="#collapse-shipping-method-aramex"
                        aria-expanded="false"
                        aria-controls="collapse-shipping-method-aramex"
                    >
                        @if ($status)
                            {{ trans('core/base::layouts.settings') }}
                        @else
                            {{ trans('core/base::forms.edit') }}
                        @endif
                    </x-core::button>
                </div>
            </x-core::table.body.cell>
        </x-core::table.body.row>
        <x-core::table.body.row class="collapse" id="collapse-shipping-method-aramex">
            <x-core::table.body.cell class="border-left" colspan="3">
                <x-core::form :url="route('ecommerce.shipments.aramex.settings.update')">
                    <div class="row">
                        <div class="col-sm-12">
                            <p class="text-muted">
                                {{ trans('plugins/aramex::aramex.please_provide_information') }}
                                <a href="https://goaramex.com/" target="_blank">Aramex</a>:
                            </p>
                        </div>
                       
                         
                        <div class="col-sm-6">
                            <x-core::form.text-input
                                name="shipping_aramex_account_country_code"
                                :label="trans('plugins/aramex::aramex.country_code')"
                                placeholder=""
                                :disabled="BaseHelper::hasDemoModeEnabled()"
                                :value="BaseHelper::hasDemoModeEnabled() ? Str::mask($countryCode, '*', 10) : $countryCode"
                            />
                        </div>
                        <div class="col-sm-6">
                             <x-core::form.text-input
                                name="shipping_aramex_account_entity"
                                :label="trans('plugins/aramex::aramex.account_entity')"
                                placeholder=""
                                :disabled="BaseHelper::hasDemoModeEnabled()"
                                :value="BaseHelper::hasDemoModeEnabled() ? Str::mask($accountEntity, '*', 2) : $accountEntity"
                            />
                        </div>
                        <div class="col-sm-6">
                            <x-core::form.text-input
                                name="shipping_aramex_account_number"
                                :label="trans('plugins/aramex::aramex.account_number')"
                                placeholder=""
                                :disabled="BaseHelper::hasDemoModeEnabled()"
                                :value="BaseHelper::hasDemoModeEnabled() ? Str::mask($accountNumber, '*', 2) : $accountNumber"
                            />
                        </div>
                        <div class="col-sm-6">
                            <x-core::form.text-input
                                name="shipping_aramex_account_pin"
                                :label="trans('plugins/aramex::aramex.account_pin')"
                                placeholder=""
                                :disabled="BaseHelper::hasDemoModeEnabled()"
                                :value="BaseHelper::hasDemoModeEnabled() ? Str::mask($accountPin, '*', 2) : $accountPin"
                            />
                        </div>
                        <div class="col-sm-6">
                            <x-core::form.text-input
                                name="shipping_aramex_username"
                                :label="trans('plugins/aramex::aramex.username')"
                                placeholder=""
                                :disabled="BaseHelper::hasDemoModeEnabled()"
                                :value="BaseHelper::hasDemoModeEnabled() ? Str::mask($username, '*', 2) : $username"
                            />
                        </div>
                        <div class="col-sm-6">
                            <x-core::form.text-input
                                name="shipping_aramex_password"
                                :label="trans('plugins/aramex::aramex.password')"
                                placeholder=""
                                type="password"
                                :disabled="BaseHelper::hasDemoModeEnabled()"
                                :value="BaseHelper::hasDemoModeEnabled() ? Str::mask($password, '*') : $password"
                            />
                        </div>

                        <div class="col-sm-6">

                            <div id="shipping_aramex_allowed_domestic_method">
                                <x-core::form.select
                                :label="trans('Allowed Domestic Methods')"
                                name="shipping_aramex_allowed_domestic_method[]"
                                :options="aramexAllowedDomesticMethods()"
                                :value="$allowedDomesticMethods"
                                :searchable="true"
                                :multiple="true"
                            />
                            </div>
                           
                        </div>
                        <div class="col-sm-6">

                            <div id="shipping_aramex_allowed_domestic_method">
                                <x-core::form.select
                                :label="trans('Allowed Domestic Additional Services')"
                                name="shipping_aramex_allowed_domestic_additional_services[]"
                                :options="aramexAllowedDomesticAdditionalServices()"
                                :value="$allowedDomesticAdditionalServices"
                                :searchable="true"
                                :multiple="true"
                                
                            />
                            </div>
                           
                        </div>

                        <div class="col-sm-6">
                            <div id="shipping_aramex_allowed_international_method" >
                                <x-core::form.select
                                :label="trans('Allowed International Methods')"
                                name="shipping_aramex_allowed_international_method[]"
                                :options="aramexAllowedInternationalMethods()"
                                :value="$allowedInternationalMethods"
                                :searchable="true"
                                :multiple="true"
                            />
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div id="shipping_aramex_allowed_international_method" >
                                <x-core::form.select
                                :label="trans('Allowed International Additional Services')"
                                name="shipping_aramex_allowed_international_additional_services[]"
                                :options="aramexAllowedInternationalAdditionalServices()"
                                :value="$allowedInternationalAdditionalServices"
                                :searchable="true"
                                :multiple="true"
                            />
                            </div>
                        </div>

                   
                        <div class="col-sm-12">
                            <fieldset class="form-fieldset">
                                <div class="form-group location-settings" style="" data-bb-value="0">
                                   <div class="mb-3 position-relative">
                                      <label class="form-label" for="location_settings_label">
                                      Available countries
                                      </label>
                                   </div>
                                   <div class="mb-3 position-relative">
                                      <input type="hidden" name="shipping_aramex_available_countries_all" value="0">
                                      <label class="check-all form-check" data-set=".available-countries">
                                      <input type="checkbox" id="available_countries_all" name="shipping_aramex_available_countries_all" class="form-check-input" value="1" {{ $availableCountriesAll ? 'checked' : '' }}>
                                      <span class="form-check-label">
                                      All
                                      </span>
                                      </label>
                                      {{-- <small class="form-hint">
                                      If you uncheck all countries, it will apply for all countries.
                                      </small> --}}
                                   </div>
                                   <div class="mb-3 position-relative">
                                      <fieldset class="form-fieldset fieldset-for-multi-check-list">
                                         
                                         <div class="multi-check-list-wrapper">
                                            @foreach ($countries as $code => $name)
                                                <label class="form-check">
                                                    <input type="checkbox" id="available-countries-item-{{ $code }}" name="shipping_aramex_available_countries[]" class="form-check-input available-countries" value="{{ $code }}" {{ in_array($code, $availableCountries) ? 'checked' : '' }}>
                                                    <span class="form-check-label">
                                                    {{ $name }}
                                                    </span>
                                                </label>
                                            @endforeach
                                            

                                         </div>
                                      </fieldset>
                                   </div>
                                </div>
                             </fieldset>
                            
                        </div>
                        <div class="col-sm-12">
                            <x-core::form.text-input
                            name="shipping_aramex_custom_amount"
                            :label="trans('Custom Amount')"
                            placeholder="20"
                            type="number"
                            :disabled="BaseHelper::hasDemoModeEnabled()"
                            :value="$customAmount"
                        />
                        </div>
                        <div class="col-sm-4">
                            <x-core::form-group>
                                <x-core::form.toggle
                                    name="shipping_aramex_test"
                                    :checked="$test"
                                    :label="trans('plugins/aramex::aramex.test_mode')"
                                />
                            </x-core::form-group>

                            <x-core::form-group>
                                <x-core::form.toggle
                                    name="shipping_aramex_status"
                                    :checked="$status"
                                    :label="trans('plugins/aramex::aramex.activate')"
                                />
                            </x-core::form-group> 
                        </div>

                        

                           {{-- <x-core::alert type="warning">
                                {{ trans('plugins/aramex::aramex.not_available_in_cod_payment_method') }}
                            </x-core::alert> --}}

                            

                                <div class="text-end">
                                    <x-core::button type="submit" color="primary">
                                        {{ trans('core/base::forms.update') }}
                                    </x-core::button>
                                </div>
                             
                        
                    </div>
                </x-core::form>
            </x-core::table.body.cell>
        </x-core::table.body.row>
    </x-core::table.body>
</x-core::table>
</x-core::card>

@push('footer')

 <script>

$(document).on('change', '.check-all', (event) => {
        let _self = $(event.currentTarget)
        let set = _self.attr('data-set')
        let checked = _self.find('input').prop('checked')
        $(set).each((index, el) => {
            if (checked) {
                $(el).prop('checked', true)
            } else {
                $(el).prop('checked', false)
            }
        })
    })

$(document).on('change', '.available-countries', () => {
    let allCountries = $('.available-countries');
    let allChecked = true;

    allCountries.each((index, el) => {
        if (!$(el).prop('checked')) {
            allChecked = false;
        }
    });

    $('#available_countries_all').prop('checked', allChecked);
});   

    // $(document).ready(function() {

    //     $('#shipping_aramex_product_group').on('change', function() {
    //         var selectedValue = $(this).val();
    //         console.log(selectedValue);
    //         if (selectedValue === 'DOM') {
    //             $('#shipping_aramex_allowed_domestic_method').show();
    //             $('#shipping_aramex_allowed_international_method').hide();
    //         } else if (selectedValue === 'EXP') {
    //             $('#shipping_aramex_allowed_domestic_method').hide();
    //             $('#shipping_aramex_allowed_international_method').show();
    //         }
    //     });

    //     // Trigger change event on page load to set initial state
    //     $('#shippingMethod').trigger('change');
    // });

</script>
@endpush
