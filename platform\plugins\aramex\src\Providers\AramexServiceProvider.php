<?php

namespace <PERSON><PERSON>qi\Aramex\Providers;

use Botble\Base\Traits\LoadAndPublishDataTrait;
use <PERSON><PERSON>qi\Aramex\Http\Middleware\WebhookMiddleware;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON><PERSON>\Aramex\Aramex as AramexClass;
use <PERSON>haqi\Aramex\Facades\Aramex;

class AramexServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        if (! is_plugin_active('ecommerce')) {
            return;
        }

        $this->app->singleton(Aramex::class, function () {
            return new AramexClass($this->app);
        });

        $this->setNamespace('plugins/aramex')->loadHelpers();
    }

    public function boot(): void
    {
        if (! is_plugin_active('ecommerce')) {
            return;
        }

        $this
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes()
            ->loadAndPublishConfigurations(['aramex'])
            ->publishAssets();

        // $this->app['events']->listen(RouteMatched::class, function () {
        //     $this->app['router']->aliasMiddleware('aramex.webhook', WebhookMiddleware::class);
        // });

        $config = $this->app['config'];
        if (! $config->has('logging.channels.aramex')) {
            $config->set([
                'logging.channels.aramex' => [
                    'driver' => 'daily',
                    'path' => storage_path('logs/aramex.log'),
                ],
            ]);
        }

        $this->app->register(HookServiceProvider::class);
        // $this->app->register(CommandServiceProvider::class);
    }
}
