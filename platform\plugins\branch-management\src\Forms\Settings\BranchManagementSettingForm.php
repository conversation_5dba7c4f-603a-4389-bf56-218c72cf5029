<?php

namespace Bo<PERSON>ble\BranchManagement\Forms\Settings;

use Botble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\OnOffField;
use Botble\BranchManagement\Http\Requests\Settings\BranchManagementSettingRequest;
use Bo<PERSON>ble\Setting\Forms\SettingForm;

class BranchManagementSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->setSectionTitle(trans('plugins/branch-management::settings.title'))
            ->setSectionDescription(trans('plugins/branch-management::settings.description'))
            ->setValidatorClass(BranchManagementSettingRequest::class)
            ->add(
                'branch_management_enable_pickup',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/branch-management::settings.enable_pickup'))
                    ->helperText(trans('plugins/branch-management::settings.enable_pickup_help'))
                    ->value(get_ecommerce_setting('branch_management_enable_pickup', true))
            )

            ->add(
                'branch_management_show_on_product_page',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/branch-management::settings.show_on_product_page'))
                    ->helperText(trans('plugins/branch-management::settings.show_on_product_page_help'))
                    ->value(get_ecommerce_setting('branch_management_show_on_product_page', true))
            );
    }
}
