<?php

namespace Shaqi\Aramex\Providers;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Botble\Ecommerce\Enums\ShippingMethodEnum;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Models\Shipment;
use Botble\Payment\Enums\PaymentMethodEnum;
use Illuminate\Support\Arr;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Shaqi\Aramex\AramexHelper;
use Shaqi\Aramex\Facades\Aramex;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter('handle_shipping_fee', [$this, 'handleShippingFee'], 11, 2);

        add_filter(SHIPPING_METHODS_SETTINGS_PAGE, [$this, 'addSettings'], 2);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == ShippingMethodEnum::class) {
                $values['ARAMEX'] = ARAMEX_SHIPPING_METHOD_NAME;
            }

            return $values;
        }, 2, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == ShippingMethodEnum::class && $value == ARAMEX_SHIPPING_METHOD_NAME) {
                return 'Aramex';
            }

            return $value;
        }, 2, 2);

        add_filter('shipment_buttons_detail_order', function (?string $content, Shipment $shipment) {
           
            Assets::addScriptsDirectly('vendor/core/plugins/aramex/js/aramex.js');

            return $content . view('plugins/aramex::buttons', compact('shipment'))->render();
        }, 1, 2);
    }

    public function handleShippingFee(array $result, array $data): array
    {
        //  if (! $this->app->runningInConsole() && setting('shipping_aramex_status') == 1 && AramexHelper::checkIsCountryAllowed($data)) {
        
        //     $rates = AramexHelper::getAramexRates($data);
        //         $result['aramex'] = $rates;
        // }

        return $result;
    }

    public function addSettings(?string $settings): string
    {
     
        $logFiles = [];

        if (setting('shipping_aramex_logging')) {
            foreach (BaseHelper::scanFolder(storage_path('logs')) as $file) {
                if (Str::startsWith($file, 'aramex-')) {
                    $logFiles[] = $file;
                }
            }
        }

        return $settings . view('plugins/aramex::settings', compact('logFiles'))->render();
    }
}
