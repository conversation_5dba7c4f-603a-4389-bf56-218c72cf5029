<?php

use Bo<PERSON><PERSON>\Base\Facades\AdminHelper;
use Botble\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Shaqi\Aramex\Http\Controllers'], function () {
    AdminHelper::registerRoutes(function () {
        Route::group([
            'prefix' => 'shipments/aramex',
            'as' => 'ecommerce.shipments.aramex.',
            'permission' => 'ecommerce.shipments.index',
        ], function () {
            Route::controller('AramexController')->group(function () {
                Route::get('show/{id}', [
                    'as' => 'show',
                    'uses' => 'show',
                ]);


                Route::get('shipment/show/{id}', [
                    'as' => 'shipment.show',
                    'uses' => 'showShipment',
                    'permission' => 'ecommerce.shipments.edit',
                ]);

                Route::post('shipment/create/{id}', [
                    'as' => 'shipment.create',
                    'uses' => 'createShipment',
                    'permission' => 'ecommerce.shipments.edit',
                ]);

               
                Route::get('shipment/pickup/show/{id}', [
                    'as' => 'shipment.pickup.show',
                    'uses' => 'showPickup',
                    'permission' => 'ecommerce.shipments.edit',
                ]);

                
                Route::post('shipment/pickup/create/{id}', [
                    'as' => 'shipment.pickup.create',
                    'uses' => 'createPickup',
                    'permission' => 'ecommerce.shipments.edit',
                ]);

                Route::get('rates/{id}', [
                    'as' => 'rates',
                    'uses' => 'getRates',
                ]);

                Route::post('update-rate/{id}', [
                    'as' => 'update-rate',
                    'uses' => 'updateRate',
                    'permission' => 'ecommerce.shipments.edit',
                ]);

                Route::get('view-logs/{file}', [
                    'as' => 'view-log',
                    'uses' => 'viewLog',
                ]);
            });

            Route::group(['prefix' => 'settings', 'as' => 'settings.'], function () {
                Route::post('update', [
                    'as' => 'update',
                    'uses' => 'AramexSettingController@update',
                    'middleware' => 'preventDemo',
                    'permission' => 'shipping_methods.index',
                ]);
            });
        });
    });

    if (is_plugin_active('marketplace')) {
        Theme::registerRoutes(function () {
            Route::group([
                'prefix' => 'vendor',
                'as' => 'marketplace.vendor.',
                'middleware' => ['vendor'],
            ], function () {
                Route::group(['prefix' => 'orders', 'as' => 'orders.'], function () {
                    Route::group(['prefix' => 'aramex', 'as' => 'aramex.'], function () {
                        Route::controller('AramexController')->group(function () {
                            Route::get('show/{id}', [
                                'as' => 'show',
                                'uses' => 'show',
                            ]);

                            Route::post('transactions/create/{id}', [
                                'as' => 'transactions.create',
                                'uses' => 'createTransaction',
                            ]);

                            Route::get('rates/{id}', [
                                'as' => 'rates',
                                'uses' => 'getRates',
                            ]);

                            Route::post('update-rate/{id}', [
                                'as' => 'update-rate',
                                'uses' => 'updateRate',
                            ]);
                        });
                    });
                });
            });
        });
    }

    if (defined('THEME_MODULE_SCREEN_NAME')) {
        Theme::registerRoutes(function () {
            Route::get('aramex/test', [
                'as' => 'aramex.test',
                'uses' => 'AramexWebhookController@test',
            ]);
        });
    }

});

Route::group([
    'namespace' => 'Shaqi\Aramex\Http\Controllers',
    'prefix' => 'aramex',
    'middleware' => ['api', 'aramex.webhook'],
    'as' => 'aramex.',
], function () {
    Route::controller('AramexWebhookController')->group(function () {
        Route::post('webhooks', [
            'uses' => 'index',
            'as' => 'webhooks',
        ]);
    });
});

