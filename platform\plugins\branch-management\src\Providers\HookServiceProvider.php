<?php

namespace Bo<PERSON><PERSON>\BranchManagement\Providers;

use <PERSON><PERSON><PERSON>\Ecommerce\Enums\ShippingMethodEnum;
use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>ble\Theme\Facades\Theme;
use Bo<PERSON><PERSON>\Base\Facades\Html;
use Bo<PERSON>ble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Bo<PERSON>ble\BranchManagement\Services\BranchPickupService;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // Register hook for product page branch display (exactly like quote plugin)
        add_filter(ECOMMERCE_PRODUCT_DETAIL_EXTRA_HTML, [$this, 'addBranchesToProduct'], 120, 2);

        // Load assets for frontend
        add_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, [$this, 'loadProductPageAssets'], 120, 2);

        // Load assets for checkout page
        add_filter('ecommerce_checkout_header', [$this, 'loadCheckoutAssets'], 120);

        // Register branch pickup as shipping method (like Shippo plugin)
        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == ShippingMethodEnum::class) {
                $values['BRANCH_PICKUP'] = BRANCH_PICKUP_SHIPPING_METHOD_NAME;
            }
            return $values;
        }, 20, 2);

        // Register shipping method label
        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == ShippingMethodEnum::class && $value == BRANCH_PICKUP_SHIPPING_METHOD_NAME) {
                return trans('plugins/branch-management::branch.pickup.title');
            }
            return $value;
        }, 20, 2);

        // Handle shipping fee calculation for branch pickup
        add_filter('handle_shipping_fee', [$this, 'handleShippingFee'], 20, 2);


        // Add branch selection to checkout form
        add_filter('ecommerce_checkout_form_after_shipping_address_form', [$this, 'addBranchSelectionToCheckout'], 20, 2);

        // Add branch pickup info to order displays
        add_filter('ecommerce_order_detail_top', [$this, 'addBranchPickupInfoToOrderDetail'], 20, 2);

        // Add branch pickup info to order emails
        add_filter('ecommerce_order_email_variables', [$this, 'addBranchPickupEmailVariables'], 20, 2);

        // Hook to add branch pickup data to address data before saving
        add_filter('ecommerce_checkout_address_data', [$this, 'addBranchPickupToAddressData'], 20, 2);

        // Add branch pickup info to shipment pages
        add_filter('ecommerce_shipment_detail_top', [$this, 'addBranchPickupInfoToShipment'], 20, 2);

        // Add server-side validation for branch pickup
        add_filter('ecommerce_checkout_rules', [$this, 'addBranchPickupValidationRules'], 20, 2);
    }

    public function addBranchPickupValidationRules(array $rules, $request): array
    {
        if (!get_ecommerce_setting('branch_management_enable_pickup', true)) {
            return $rules;
        }

        // Check if branch pickup is selected
        if ($this->isBranchPickupSelected($request)) {
            $rules['pickup_city_id'] = [
                'required',
                'integer',
                'exists:cities,id',
            ];

            $rules['pickup_branch_id'] = [
                'required',
                'integer',
                'exists:branches,id',
                function ($attribute, $value, $fail) use ($request) {
                    $branchRepository = app(BranchInterface::class);
                    $branch = $branchRepository->findById($value);

                    if (!$branch) {
                        $fail('The selected branch does not exist.');
                        return;
                    }

                    if (!$branch->is_pickup_available) {
                        $fail('The selected branch is not available for pickup.');
                        return;
                    }

                    // Validate that the branch belongs to the selected city
                    $cityId = $request->input('pickup_city_id');
                    if ($cityId && $branch->city_id != $cityId) {
                        $fail('The selected branch does not belong to the selected city.');
                    }
                },
            ];
        }

        return $rules;
    }

    protected function isBranchPickupSelected($request): bool
    {
        $shippingMethod = $request->input('shipping_method');
        $shippingOption = $request->input('shipping_option');

        // Check shipping method
        if ($shippingMethod) {
            $methodValue = strtolower($shippingMethod);
            if ($methodValue === 'branch_pickup' ||
                str_contains($methodValue, 'pickup') ||
                str_contains($methodValue, 'branch')) {
                return true;
            }
        }

        // Check shipping option
        if ($shippingOption) {
            $optionValue = strtolower($shippingOption);
            if ($optionValue === 'pickup_branch' ||
                str_contains($optionValue, 'pickup') ||
                str_contains($optionValue, 'branch')) {
                return true;
            }
        }

        return false;
    }

    public function addBranchesToProduct(?string $content, $product): string
    {
        if (!get_ecommerce_setting('branch_management_show_on_product_page', 1)) {
            return $content ?? '';
        }

        $branchView = view('plugins/branch-management::product.branches', compact('product'))->render();
        return ($content ?? '') . $branchView;
    }

    public function loadProductPageAssets($screen, $object): void
    {
        if ($screen !== PRODUCT_MODULE_SCREEN_NAME) {
            return;
        }

        if (!get_ecommerce_setting('branch_management_show_on_product_page', 1)) {
            return;
        }

        // Load CSS
        Theme::asset()
            ->usePath(false)
            ->add('branch-management-css', asset('vendor/core/plugins/branch-management/css/branch-management.css'), [], [], '1.0.0');

        // Load JS
        Theme::asset()
            ->container('footer')
            ->usePath(false)
            ->add(
                'branch-product-js',
                asset('vendor/core/plugins/branch-management/js/branch-product.js'),
                ['jquery'],
                [],
                '1.0.0'
            );
    }

    public function loadCheckoutAssets(?string $html): string
    {
        if (!get_ecommerce_setting('branch_management_enable_pickup', true)) {
            return $html ?? '';
        }

        // Add CSS
        $css = Html::style('vendor/core/plugins/branch-management/css/branch-management.css?v=1.0.0');

        // Add JS
        $js = Html::script('vendor/core/plugins/branch-management/js/branch-checkout.js?v=1.0.0');

        return ($html ?? '') . $css . $js;
    }

    public function handleShippingFee(array $result, array $data): array
    {
        if (!get_ecommerce_setting('branch_management_enable_pickup', true)) {
            return $result;
        }

        // Always show branch pickup as an option - city/branch selection will be handled in checkout form
        $result[BRANCH_PICKUP_SHIPPING_METHOD_NAME] = [
            'pickup_branch' => [
                'name' => trans('plugins/branch-management::branch.pickup.title'),
                'description' => trans('plugins/branch-management::branch.pickup.checkout_description'),
                'price' => 0,
                'disabled' => false,
                'option' => 'pickup_branch',
            ]
        ];

        return $result;
    }


    public function addBranchSelectionToCheckout(?string $content, $products = null): string
    {
        // $products parameter is required by the hook signature but not used in this implementation

        if (!get_ecommerce_setting('branch_management_enable_pickup', true)) {
            return $content ?? '';
        }

        // Pre-load cities with pickup branches for server-side rendering
        $branchRepository = app(BranchInterface::class);
        $citiesWithBranches = $branchRepository->getCitiesWithPickupBranches();

        $branchSelectionHtml = view('plugins/branch-management::checkout.branch-selection', [
            'citiesWithBranches' => $citiesWithBranches
        ])->render();

        return ($content ?? '') . $branchSelectionHtml;
    }

    public function addBranchPickupInfoToOrderDetail(?string $content, $order): string
    {
        // Check if this is a branch pickup order by checking the shipping address
        if (!$order->shippingAddress || !$order->shippingAddress->is_branch_pickup || !$order->shippingAddress->branch) {
            return $content ?? '';
        }

        $branchPickupHtml = view('plugins/branch-management::orders.branch-pickup-info', compact('order'))->render();

        return ($content ?? '') . $branchPickupHtml;
    }

    public function addBranchPickupInfoToOrderSidebar(?string $content, $order): string
    {
        // Use the simplified BranchPickupService to check if this is a branch pickup order
        $branchPickupService = app(\Botble\BranchManagement\Services\BranchPickupService::class);
        if (!$branchPickupService->isBranchPickupOrder($order)) {
            return $content ?? '';
        }

        $branchPickupHtml = view('plugins/branch-management::orders.branch-pickup-sidebar', compact('order'))->render();

        return ($content ?? '') . $branchPickupHtml;
    }

    public function addBranchPickupEmailVariables(array $variables, $order): array
    {
        // Check if this is a branch pickup order
        if (!$order->shippingAddress || !$order->shippingAddress->is_branch_pickup || !$order->shippingAddress->branch) {
            return $variables;
        }

        $branch = $order->shippingAddress->branch;

        // Add branch pickup variables to the email variables
        $variables['branch_pickup_info'] = view('plugins/branch-management::emails.branch-pickup-info', compact('order', 'branch'))->render();
        $variables['branch_name'] = $branch->name;
        $variables['branch_address'] = $branch->full_address;
        $variables['branch_phone'] = $branch->phone ?? '';
        $variables['branch_email'] = $branch->email ?? '';
        $variables['pickup_fee'] = $branch->pickup_fee > 0 ? format_price($branch->pickup_fee) : '';

        // Update shipping method name for email display
        $shippingMethodName = trans('plugins/branch-management::branch.pickup.title') . ': ' . $branch->name;
        if ($branch->city && $branch->city->name) {
            $shippingMethodName .= ' (' . $branch->city->name . ')';
        }
        $variables['shipping_method'] = $shippingMethodName;

        return $variables;
    }

    public function addBranchPickupToAddressData(array $addressData, $request): array
    {
        if (!get_ecommerce_setting('branch_management_enable_pickup', true)) {
            return $addressData;
        }

        // Check if this is a branch pickup order
        $shippingMethod = $request->input('shipping_method');
        $shippingOption = $request->input('shipping_option');
        $branchId = $request->input('pickup_branch_id');

        $isBranchPickup = false;

        // Check shipping method
        if (is_string($shippingMethod) && $shippingMethod === BRANCH_PICKUP_SHIPPING_METHOD_NAME) {
            $isBranchPickup = true;
        } elseif (is_array($shippingMethod) && in_array(BRANCH_PICKUP_SHIPPING_METHOD_NAME, $shippingMethod)) {
            $isBranchPickup = true;
        }

        // Check shipping option as fallback
        if (!$isBranchPickup && is_string($shippingOption) && $shippingOption === 'pickup_branch') {
            $isBranchPickup = true;
        } elseif (!$isBranchPickup && is_array($shippingOption) && in_array('pickup_branch', $shippingOption)) {
            $isBranchPickup = true;
        }

        // If this is a branch pickup order and we have a branch ID, add it to address data
        if ($isBranchPickup && $branchId) {
            $addressData['branch_id'] = (int) $branchId;
            $addressData['is_branch_pickup'] = true;
        }


        return $addressData;

    }

    public function addBranchPickupInfoToShipment(?string $content, $shipment): string
    {
        if (!get_ecommerce_setting('branch_management_enable_pickup', true)) {
            return $content ?? '';
        }

        // Check if this shipment is for a branch pickup order
        if (!$shipment->order->shippingAddress || !$shipment->order->shippingAddress->is_branch_pickup || !$shipment->order->shippingAddress->branch) {
            return $content ?? '';
        }

        $branchPickupHtml = view('plugins/branch-management::shipments.branch-pickup-info', compact('shipment'))->render();

        return ($content ?? '') . $branchPickupHtml;
    }
}
