{"__meta": {"id": "01K3V8WSEF5V052EJKV09RBHXN", "datetime": "2025-08-29 15:59:44", "utime": **********.080577, "method": "GET", "uri": "/admin/ecommerce/orders", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 393, "start": 1756483181.695322, "end": **********.080593, "duration": 2.3852710723876953, "duration_str": "2.39s", "measures": [{"label": "Booting", "start": 1756483181.695322, "relative_start": 0, "end": **********.725584, "relative_end": **********.725584, "duration": 1.****************, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.725603, "relative_start": 1.****************, "end": **********.080596, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.746402, "relative_start": 1.****************, "end": **********.759474, "relative_end": **********.759474, "duration": 0.013072013854980469, "duration_str": "13.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::bulk-changes", "start": **********.860479, "relative_start": 1.****************, "end": **********.860479, "relative_end": **********.860479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.865095, "relative_start": 1.****************, "end": **********.865095, "relative_end": **********.865095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53dbbce7846c20f5734d935076bf04ca", "start": **********.869787, "relative_start": 1.1744649410247803, "end": **********.869787, "relative_end": **********.869787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.872376, "relative_start": 1.1770539283752441, "end": **********.872376, "relative_end": **********.872376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.872959, "relative_start": 1.1776368618011475, "end": **********.872959, "relative_end": **********.872959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.create", "start": **********.875687, "relative_start": 1.1803648471832275, "end": **********.875687, "relative_end": **********.875687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::baaf02f6c56c328f3c307011a60b6b9f", "start": **********.877113, "relative_start": 1.181791067123413, "end": **********.877113, "relative_end": **********.877113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.88063, "relative_start": 1.1853079795837402, "end": **********.88063, "relative_end": **********.88063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.882176, "relative_start": 1.1868538856506348, "end": **********.882176, "relative_end": **********.882176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::badge", "start": **********.883239, "relative_start": 1.1879169940948486, "end": **********.883239, "relative_end": **********.883239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.899448, "relative_start": 1.2041258811950684, "end": **********.077537, "relative_end": **********.077537, "duration": 1.1780891418457031, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::table", "start": **********.900511, "relative_start": 1.2051889896392822, "end": **********.900511, "relative_end": **********.900511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::base-table", "start": **********.901354, "relative_start": 1.2060320377349854, "end": **********.901354, "relative_end": **********.901354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.934897, "relative_start": 1.239574909210205, "end": **********.934897, "relative_end": **********.934897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::14ad31fb3af14d3ba24d3c578af35e73", "start": **********.937378, "relative_start": 1.242055892944336, "end": **********.937378, "relative_end": **********.937378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::filter", "start": **********.975623, "relative_start": 1.2803008556365967, "end": **********.975623, "relative_end": **********.975623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.977607, "relative_start": 1.2822849750518799, "end": **********.977607, "relative_end": **********.977607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.979842, "relative_start": 1.284519910812378, "end": **********.979842, "relative_end": **********.979842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.980555, "relative_start": 1.2852330207824707, "end": **********.980555, "relative_end": **********.980555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.981212, "relative_start": 1.2858898639678955, "end": **********.981212, "relative_end": **********.981212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.981564, "relative_start": 1.2862420082092285, "end": **********.981564, "relative_end": **********.981564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b9d721822c50540453b6fa0f4bd081", "start": **********.982753, "relative_start": 1.287431001663208, "end": **********.982753, "relative_end": **********.982753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.983596, "relative_start": 1.2882740497589111, "end": **********.983596, "relative_end": **********.983596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.984745, "relative_start": 1.2894229888916016, "end": **********.984745, "relative_end": **********.984745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.985405, "relative_start": 1.2900829315185547, "end": **********.985405, "relative_end": **********.985405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.986758, "relative_start": 1.291435956954956, "end": **********.986758, "relative_end": **********.986758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.987224, "relative_start": 1.2919020652770996, "end": **********.987224, "relative_end": **********.987224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.987924, "relative_start": 1.2926020622253418, "end": **********.987924, "relative_end": **********.987924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.989075, "relative_start": 1.293752908706665, "end": **********.989075, "relative_end": **********.989075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c8728926b3975e33a051ebb6ef68e5d", "start": **********.991036, "relative_start": 1.2957139015197754, "end": **********.991036, "relative_end": **********.991036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.991947, "relative_start": 1.2966248989105225, "end": **********.991947, "relative_end": **********.991947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.body.index", "start": **********.992465, "relative_start": 1.2971429824829102, "end": **********.992465, "relative_end": **********.992465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.992945, "relative_start": 1.2976229190826416, "end": **********.992945, "relative_end": **********.992945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::bulk-action", "start": **********.032138, "relative_start": 1.3368160724639893, "end": **********.032138, "relative_end": **********.032138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.034035, "relative_start": 1.3387129306793213, "end": **********.034035, "relative_end": **********.034035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.035105, "relative_start": 1.339782953262329, "end": **********.035105, "relative_end": **********.035105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.036512, "relative_start": 1.3411898612976074, "end": **********.036512, "relative_end": **********.036512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.05579, "relative_start": 1.3604679107666016, "end": **********.05579, "relative_end": **********.05579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.058053, "relative_start": 1.3627309799194336, "end": **********.058053, "relative_end": **********.058053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42e1966f95bce065f65d4b22e53f3772", "start": **********.059621, "relative_start": 1.3642990589141846, "end": **********.059621, "relative_end": **********.059621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.create", "start": **********.060392, "relative_start": 1.36506986618042, "end": **********.060392, "relative_end": **********.060392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::baaf02f6c56c328f3c307011a60b6b9f", "start": **********.060845, "relative_start": 1.365522861480713, "end": **********.060845, "relative_end": **********.060845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b4335e9ff8c721e014fa31a65e4454d3", "start": **********.062911, "relative_start": 1.367588996887207, "end": **********.062911, "relative_end": **********.062911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::77e239c329a8f8a48840d651c6a8a9ee", "start": **********.064522, "relative_start": 1.3691999912261963, "end": **********.064522, "relative_end": **********.064522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7b2a6f88f6865c9dfe436f51388f0b67", "start": **********.065903, "relative_start": 1.3705809116363525, "end": **********.065903, "relative_end": **********.065903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.067188, "relative_start": 1.37186598777771, "end": **********.067188, "relative_end": **********.067188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c8728926b3975e33a051ebb6ef68e5d", "start": **********.06854, "relative_start": 1.***************, "end": **********.06854, "relative_end": **********.06854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.069009, "relative_start": 1.3736870288848877, "end": **********.069009, "relative_end": **********.069009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.089927, "relative_start": 1.3946049213409424, "end": **********.089927, "relative_end": **********.089927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::modal", "start": **********.091409, "relative_start": 1.3960869312286377, "end": **********.091409, "relative_end": **********.091409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.093288, "relative_start": 1.397965908050537, "end": **********.093288, "relative_end": **********.093288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.094813, "relative_start": 1.3994910717010498, "end": **********.094813, "relative_end": **********.094813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.096177, "relative_start": 1.4008550643920898, "end": **********.096177, "relative_end": **********.096177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.097326, "relative_start": 1.4020040035247803, "end": **********.097326, "relative_end": **********.097326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.098197, "relative_start": 1.4028749465942383, "end": **********.098197, "relative_end": **********.098197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.099729, "relative_start": 1.404407024383545, "end": **********.099729, "relative_end": **********.099729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.100633, "relative_start": 1.405310869216919, "end": **********.100633, "relative_end": **********.100633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.101401, "relative_start": 1.4060790538787842, "end": **********.101401, "relative_end": **********.101401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.101857, "relative_start": 1.4065349102020264, "end": **********.101857, "relative_end": **********.101857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.102197, "relative_start": 1.4068748950958252, "end": **********.102197, "relative_end": **********.102197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.103379, "relative_start": 1.4080569744110107, "end": **********.103379, "relative_end": **********.103379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.104019, "relative_start": 1.4086968898773193, "end": **********.104019, "relative_end": **********.104019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.104578, "relative_start": 1.4092559814453125, "end": **********.104578, "relative_end": **********.104578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.105327, "relative_start": 1.4100048542022705, "end": **********.105327, "relative_end": **********.105327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.105637, "relative_start": 1.4103150367736816, "end": **********.105637, "relative_end": **********.105637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.106245, "relative_start": 1.4109230041503906, "end": **********.106245, "relative_end": **********.106245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.106843, "relative_start": 1.4115209579467773, "end": **********.106843, "relative_end": **********.106843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.107211, "relative_start": 1.4118890762329102, "end": **********.107211, "relative_end": **********.107211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.107457, "relative_start": 1.4121348857879639, "end": **********.107457, "relative_end": **********.107457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.108211, "relative_start": 1.412889003753662, "end": **********.108211, "relative_end": **********.108211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.108846, "relative_start": 1.4135239124298096, "end": **********.108846, "relative_end": **********.108846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.109471, "relative_start": 1.4141490459442139, "end": **********.109471, "relative_end": **********.109471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.109808, "relative_start": 1.4144859313964844, "end": **********.109808, "relative_end": **********.109808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.110051, "relative_start": 1.4147288799285889, "end": **********.110051, "relative_end": **********.110051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::script", "start": **********.113173, "relative_start": 1.4178509712219238, "end": **********.113173, "relative_end": **********.113173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.11486, "relative_start": 1.4195380210876465, "end": **********.11486, "relative_end": **********.11486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.118252, "relative_start": 1.4229300022125244, "end": **********.118252, "relative_end": **********.118252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.119048, "relative_start": 1.4237260818481445, "end": **********.119048, "relative_end": **********.119048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.121495, "relative_start": 1.4261729717254639, "end": **********.121495, "relative_end": **********.121495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.122713, "relative_start": 1.4273910522460938, "end": **********.122713, "relative_end": **********.122713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.136755, "relative_start": 1.4414329528808594, "end": **********.136755, "relative_end": **********.136755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.137999, "relative_start": 1.4426770210266113, "end": **********.137999, "relative_end": **********.137999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.139678, "relative_start": 1.4443559646606445, "end": **********.139678, "relative_end": **********.139678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.140304, "relative_start": 1.4449820518493652, "end": **********.140304, "relative_end": **********.140304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.141059, "relative_start": 1.4457368850708008, "end": **********.141059, "relative_end": **********.141059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.142089, "relative_start": 1.4467668533325195, "end": **********.142089, "relative_end": **********.142089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d2cfde89f704c31422aff2fae16ddb81", "start": **********.143281, "relative_start": 1.4479589462280273, "end": **********.143281, "relative_end": **********.143281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.144321, "relative_start": 1.4489989280700684, "end": **********.144321, "relative_end": **********.144321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a5645d2a1f3c74251fc89224c575fed8", "start": **********.145886, "relative_start": 1.450563907623291, "end": **********.145886, "relative_end": **********.145886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.147756, "relative_start": 1.4524340629577637, "end": **********.147756, "relative_end": **********.147756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::639d159f54869d7a8362974885dec505", "start": **********.148951, "relative_start": 1.4536290168762207, "end": **********.148951, "relative_end": **********.148951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.153578, "relative_start": 1.4582560062408447, "end": **********.153578, "relative_end": **********.153578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.155849, "relative_start": 1.***************, "end": **********.155849, "relative_end": **********.155849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.157554, "relative_start": 1.****************, "end": **********.157554, "relative_end": **********.157554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.actions", "start": **********.158611, "relative_start": 1.****************, "end": **********.158611, "relative_end": **********.158611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.159072, "relative_start": 1.***************, "end": **********.159072, "relative_end": **********.159072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.179008, "relative_start": 1.****************, "end": **********.179008, "relative_end": **********.179008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.notification", "start": **********.198625, "relative_start": 1.503303050994873, "end": **********.198625, "relative_end": **********.198625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "start": **********.200483, "relative_start": 1.5051610469818115, "end": **********.200483, "relative_end": **********.200483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.201752, "relative_start": 1.50642991065979, "end": **********.201752, "relative_end": **********.201752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.219644, "relative_start": 1.****************, "end": **********.219644, "relative_end": **********.219644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.220687, "relative_start": 1.525364875793457, "end": **********.220687, "relative_end": **********.220687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.22852, "relative_start": 1.****************, "end": **********.22852, "relative_end": **********.22852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.230029, "relative_start": 1.5347070693969727, "end": **********.230029, "relative_end": **********.230029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.231052, "relative_start": 1.5357298851013184, "end": **********.231052, "relative_end": **********.231052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.232258, "relative_start": 1.5369360446929932, "end": **********.232258, "relative_end": **********.232258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.233034, "relative_start": 1.5377118587493896, "end": **********.233034, "relative_end": **********.233034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.23397, "relative_start": 1.5386478900909424, "end": **********.23397, "relative_end": **********.23397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.235579, "relative_start": 1.5402569770812988, "end": **********.235579, "relative_end": **********.235579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.235886, "relative_start": 1.5405640602111816, "end": **********.235886, "relative_end": **********.235886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.237711, "relative_start": 1.542388916015625, "end": **********.237711, "relative_end": **********.237711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.238577, "relative_start": 1.5432548522949219, "end": **********.238577, "relative_end": **********.238577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.239011, "relative_start": 1.5436890125274658, "end": **********.239011, "relative_end": **********.239011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.23967, "relative_start": 1.5443480014801025, "end": **********.23967, "relative_end": **********.23967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.239985, "relative_start": 1.5446629524230957, "end": **********.239985, "relative_end": **********.239985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.240663, "relative_start": 1.5453410148620605, "end": **********.240663, "relative_end": **********.240663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.241366, "relative_start": 1.546043872833252, "end": **********.241366, "relative_end": **********.241366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.263672, "relative_start": 1.568350076675415, "end": **********.263672, "relative_end": **********.263672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.265075, "relative_start": 1.5697529315948486, "end": **********.265075, "relative_end": **********.265075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3b17f7ce9738894b58a8b70b9624457", "start": **********.267033, "relative_start": 1.5717110633850098, "end": **********.267033, "relative_end": **********.267033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.268249, "relative_start": 1.5729269981384277, "end": **********.268249, "relative_end": **********.268249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.269565, "relative_start": 1.5742430686950684, "end": **********.269565, "relative_end": **********.269565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c6e6838aa476b78aace81114936689c", "start": **********.271965, "relative_start": 1.5766429901123047, "end": **********.271965, "relative_end": **********.271965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.274557, "relative_start": 1.5792350769042969, "end": **********.274557, "relative_end": **********.274557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.276033, "relative_start": 1.5807108879089355, "end": **********.276033, "relative_end": **********.276033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.27722, "relative_start": 1.5818979740142822, "end": **********.27722, "relative_end": **********.27722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::37fae22c8e215ea2e54e69a5e3a007cc", "start": **********.279939, "relative_start": 1.5846168994903564, "end": **********.279939, "relative_end": **********.279939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.281262, "relative_start": 1.585939884185791, "end": **********.281262, "relative_end": **********.281262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8916176d99d4ae2024cd36e11e35b821", "start": **********.282786, "relative_start": 1.5874638557434082, "end": **********.282786, "relative_end": **********.282786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.284101, "relative_start": 1.5887789726257324, "end": **********.284101, "relative_end": **********.284101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.284543, "relative_start": 1.5892210006713867, "end": **********.284543, "relative_end": **********.284543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.285108, "relative_start": 1.5897860527038574, "end": **********.285108, "relative_end": **********.285108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd27433a6607127acdaf6dc541ab2435", "start": **********.287152, "relative_start": 1.5918300151824951, "end": **********.287152, "relative_end": **********.287152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.288839, "relative_start": 1.5935170650482178, "end": **********.288839, "relative_end": **********.288839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "start": **********.29163, "relative_start": 1.5963079929351807, "end": **********.29163, "relative_end": **********.29163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.293154, "relative_start": 1.5978319644927979, "end": **********.293154, "relative_end": **********.293154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.293597, "relative_start": 1.5982749462127686, "end": **********.293597, "relative_end": **********.293597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.2942, "relative_start": 1.5988779067993164, "end": **********.2942, "relative_end": **********.2942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::080b92e00b37bcc97c1cd249894494a2", "start": **********.295959, "relative_start": 1.6006369590759277, "end": **********.295959, "relative_end": **********.295959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.29736, "relative_start": 1.6020379066467285, "end": **********.29736, "relative_end": **********.29736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6a26943e77184871de1629f41c534094", "start": **********.299306, "relative_start": 1.6039838790893555, "end": **********.299306, "relative_end": **********.299306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.300656, "relative_start": 1.6053340435028076, "end": **********.300656, "relative_end": **********.300656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8f29f8012139c7a3eb6593c906e1db38", "start": **********.302906, "relative_start": 1.607583999633789, "end": **********.302906, "relative_end": **********.302906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.304347, "relative_start": 1.609025001525879, "end": **********.304347, "relative_end": **********.304347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.304795, "relative_start": 1.6094729900360107, "end": **********.304795, "relative_end": **********.304795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.305408, "relative_start": 1.6100859642028809, "end": **********.305408, "relative_end": **********.305408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4553f5b37130b2effba490dbdf5419d2", "start": **********.308167, "relative_start": 1.6128449440002441, "end": **********.308167, "relative_end": **********.308167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.309176, "relative_start": 1.613853931427002, "end": **********.309176, "relative_end": **********.309176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59c947fc9b2121a5885d4f4e7b1242d8", "start": **********.311878, "relative_start": 1.61655592918396, "end": **********.311878, "relative_end": **********.311878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.313098, "relative_start": 1.6177759170532227, "end": **********.313098, "relative_end": **********.313098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "start": **********.314542, "relative_start": 1.6192200183868408, "end": **********.314542, "relative_end": **********.314542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.315826, "relative_start": 1.6205039024353027, "end": **********.315826, "relative_end": **********.315826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.317307, "relative_start": 1.6219849586486816, "end": **********.317307, "relative_end": **********.317307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.319068, "relative_start": 1.6237459182739258, "end": **********.319068, "relative_end": **********.319068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.322252, "relative_start": 1.6269299983978271, "end": **********.322252, "relative_end": **********.322252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.323956, "relative_start": 1.628633975982666, "end": **********.323956, "relative_end": **********.323956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::84f17fac377525e2e49f32058361220b", "start": **********.327125, "relative_start": 1.631803035736084, "end": **********.327125, "relative_end": **********.327125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.328591, "relative_start": 1.6332690715789795, "end": **********.328591, "relative_end": **********.328591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.33025, "relative_start": 1.6349279880523682, "end": **********.33025, "relative_end": **********.33025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.330902, "relative_start": 1.635580062866211, "end": **********.330902, "relative_end": **********.330902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c8617dee734f51544a3883923ddca6f", "start": **********.332947, "relative_start": 1.637624979019165, "end": **********.332947, "relative_end": **********.332947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.334635, "relative_start": 1.639312982559204, "end": **********.334635, "relative_end": **********.334635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3d3bfe5e8598abeb74083f6c26233cb5", "start": **********.336985, "relative_start": 1.6416630744934082, "end": **********.336985, "relative_end": **********.336985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.338298, "relative_start": 1.6429760456085205, "end": **********.338298, "relative_end": **********.338298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::35fe997a7b87ef55d749630606a50a1b", "start": **********.341284, "relative_start": 1.6459619998931885, "end": **********.341284, "relative_end": **********.341284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.345284, "relative_start": 1.6499619483947754, "end": **********.345284, "relative_end": **********.345284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a4f1583597dec7e67a8ae044f0915dbe", "start": **********.347409, "relative_start": 1.6520869731903076, "end": **********.347409, "relative_end": **********.347409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.348617, "relative_start": 1.6532950401306152, "end": **********.348617, "relative_end": **********.348617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c0cbd16b0cc2226ec5536610974ba3c3", "start": **********.351486, "relative_start": 1.6561639308929443, "end": **********.351486, "relative_end": **********.351486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.353093, "relative_start": 1.6577708721160889, "end": **********.353093, "relative_end": **********.353093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.356483, "relative_start": 1.661160945892334, "end": **********.356483, "relative_end": **********.356483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.358585, "relative_start": 1.6632628440856934, "end": **********.358585, "relative_end": **********.358585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.35955, "relative_start": 1.6642279624938965, "end": **********.35955, "relative_end": **********.35955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc78b90963e9d9963376e0e829411cea", "start": **********.362555, "relative_start": 1.6672329902648926, "end": **********.362555, "relative_end": **********.362555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.364184, "relative_start": 1.6688618659973145, "end": **********.364184, "relative_end": **********.364184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.365679, "relative_start": 1.6703569889068604, "end": **********.365679, "relative_end": **********.365679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.367117, "relative_start": 1.6717948913574219, "end": **********.367117, "relative_end": **********.367117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::481a833ebeb573258c941c925aa45f7b", "start": **********.369284, "relative_start": 1.673961877822876, "end": **********.369284, "relative_end": **********.369284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.370624, "relative_start": 1.6753020286560059, "end": **********.370624, "relative_end": **********.370624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b42bb0aa5fceca31ad61711414a614f0", "start": **********.372516, "relative_start": 1.6771938800811768, "end": **********.372516, "relative_end": **********.372516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.373819, "relative_start": 1.678497076034546, "end": **********.373819, "relative_end": **********.373819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.374819, "relative_start": 1.6794970035552979, "end": **********.374819, "relative_end": **********.374819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.377741, "relative_start": 1.6824190616607666, "end": **********.377741, "relative_end": **********.377741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.378931, "relative_start": 1.6836090087890625, "end": **********.378931, "relative_end": **********.378931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.379251, "relative_start": 1.6839289665222168, "end": **********.379251, "relative_end": **********.379251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.379721, "relative_start": 1.684398889541626, "end": **********.379721, "relative_end": **********.379721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::471e83668278198d730a7a3f4a475d45", "start": **********.38109, "relative_start": 1.6857678890228271, "end": **********.38109, "relative_end": **********.38109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.382214, "relative_start": 1.686892032623291, "end": **********.382214, "relative_end": **********.382214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.382957, "relative_start": 1.6876349449157715, "end": **********.382957, "relative_end": **********.382957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.383531, "relative_start": 1.688209056854248, "end": **********.383531, "relative_end": **********.383531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "start": **********.386441, "relative_start": 1.6911189556121826, "end": **********.386441, "relative_end": **********.386441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.387957, "relative_start": 1.6926350593566895, "end": **********.387957, "relative_end": **********.387957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.388336, "relative_start": 1.6930139064788818, "end": **********.388336, "relative_end": **********.388336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.388924, "relative_start": 1.6936018466949463, "end": **********.388924, "relative_end": **********.388924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.390534, "relative_start": 1.6952118873596191, "end": **********.390534, "relative_end": **********.390534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.391512, "relative_start": 1.6961898803710938, "end": **********.391512, "relative_end": **********.391512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acb69140835a74210411469faeab3034", "start": **********.394619, "relative_start": 1.6992969512939453, "end": **********.394619, "relative_end": **********.394619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.396112, "relative_start": 1.7007899284362793, "end": **********.396112, "relative_end": **********.396112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.396533, "relative_start": 1.7012109756469727, "end": **********.396533, "relative_end": **********.396533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.397095, "relative_start": 1.701772928237915, "end": **********.397095, "relative_end": **********.397095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19cd49cd69455e40dc223df6b4eaf954", "start": **********.399329, "relative_start": 1.7040069103240967, "end": **********.399329, "relative_end": **********.399329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.400638, "relative_start": 1.7053160667419434, "end": **********.400638, "relative_end": **********.400638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.401606, "relative_start": 1.7062840461730957, "end": **********.401606, "relative_end": **********.401606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2979b72aeeca0047ecdecc3ad66e7e16", "start": **********.403543, "relative_start": 1.7082209587097168, "end": **********.403543, "relative_end": **********.403543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.404902, "relative_start": 1.7095799446105957, "end": **********.404902, "relative_end": **********.404902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.40586, "relative_start": 1.7105379104614258, "end": **********.40586, "relative_end": **********.40586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3c40febd70fcdc245d99ae7cd02cface", "start": **********.407649, "relative_start": 1.712327003479004, "end": **********.407649, "relative_end": **********.407649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.409097, "relative_start": 1.7137749195098877, "end": **********.409097, "relative_end": **********.409097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.410847, "relative_start": 1.7155249118804932, "end": **********.410847, "relative_end": **********.410847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.412086, "relative_start": 1.716763973236084, "end": **********.412086, "relative_end": **********.412086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.413635, "relative_start": 1.7183129787445068, "end": **********.413635, "relative_end": **********.413635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.414239, "relative_start": 1.718916893005371, "end": **********.414239, "relative_end": **********.414239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.415508, "relative_start": 1.7201859951019287, "end": **********.415508, "relative_end": **********.415508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.41609, "relative_start": 1.7207679748535156, "end": **********.41609, "relative_end": **********.41609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.416803, "relative_start": 1.7214808464050293, "end": **********.416803, "relative_end": **********.416803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7ced212b797c29086a7922a858f3070", "start": **********.418624, "relative_start": 1.723301887512207, "end": **********.418624, "relative_end": **********.418624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.420482, "relative_start": 1.7251598834991455, "end": **********.420482, "relative_end": **********.420482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.420968, "relative_start": 1.7256460189819336, "end": **********.420968, "relative_end": **********.420968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.421542, "relative_start": 1.726219892501831, "end": **********.421542, "relative_end": **********.421542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.42407, "relative_start": 1.728747844696045, "end": **********.42407, "relative_end": **********.42407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.425748, "relative_start": 1.7304260730743408, "end": **********.425748, "relative_end": **********.425748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.426133, "relative_start": 1.7308108806610107, "end": **********.426133, "relative_end": **********.426133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.426733, "relative_start": 1.7314109802246094, "end": **********.426733, "relative_end": **********.426733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.427846, "relative_start": 1.7325239181518555, "end": **********.427846, "relative_end": **********.427846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.428605, "relative_start": 1.7332830429077148, "end": **********.428605, "relative_end": **********.428605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.430276, "relative_start": 1.7349538803100586, "end": **********.430276, "relative_end": **********.430276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.432071, "relative_start": 1.7367489337921143, "end": **********.432071, "relative_end": **********.432071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.432948, "relative_start": 1.737626075744629, "end": **********.432948, "relative_end": **********.432948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.434106, "relative_start": 1.7387840747833252, "end": **********.434106, "relative_end": **********.434106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.435429, "relative_start": 1.7401070594787598, "end": **********.435429, "relative_end": **********.435429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.436863, "relative_start": 1.7415409088134766, "end": **********.436863, "relative_end": **********.436863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8c52d9b1ef0685ec10fdc3e877751e02", "start": **********.4386, "relative_start": 1.7432780265808105, "end": **********.4386, "relative_end": **********.4386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.440351, "relative_start": 1.7450289726257324, "end": **********.440351, "relative_end": **********.440351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.441431, "relative_start": 1.7461090087890625, "end": **********.441431, "relative_end": **********.441431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42668be6e8e5266862c6994eaa88bb55", "start": **********.443972, "relative_start": 1.748650074005127, "end": **********.443972, "relative_end": **********.443972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.44512, "relative_start": 1.749798059463501, "end": **********.44512, "relative_end": **********.44512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.445486, "relative_start": 1.7501640319824219, "end": **********.445486, "relative_end": **********.445486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.445982, "relative_start": 1.7506599426269531, "end": **********.445982, "relative_end": **********.445982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.446745, "relative_start": 1.7514228820800781, "end": **********.446745, "relative_end": **********.446745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "start": **********.448164, "relative_start": 1.7528419494628906, "end": **********.448164, "relative_end": **********.448164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.44921, "relative_start": 1.7538878917694092, "end": **********.44921, "relative_end": **********.44921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.450444, "relative_start": 1.7551219463348389, "end": **********.450444, "relative_end": **********.450444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.451991, "relative_start": 1.756669044494629, "end": **********.451991, "relative_end": **********.451991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.454259, "relative_start": 1.758936882019043, "end": **********.454259, "relative_end": **********.454259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.456035, "relative_start": 1.7607128620147705, "end": **********.456035, "relative_end": **********.456035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.458009, "relative_start": 1.7626869678497314, "end": **********.458009, "relative_end": **********.458009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.460221, "relative_start": 1.7648990154266357, "end": **********.460221, "relative_end": **********.460221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.461206, "relative_start": 1.7658839225769043, "end": **********.461206, "relative_end": **********.461206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.461852, "relative_start": 1.7665300369262695, "end": **********.461852, "relative_end": **********.461852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.462844, "relative_start": 1.767521858215332, "end": **********.462844, "relative_end": **********.462844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::613233f0072612a02c74dd1699c0b74c", "start": **********.465072, "relative_start": 1.7697498798370361, "end": **********.465072, "relative_end": **********.465072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.466705, "relative_start": 1.7713830471038818, "end": **********.466705, "relative_end": **********.466705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16225ede2ef5cc17292fd2eb9026fc80", "start": **********.470368, "relative_start": 1.7750458717346191, "end": **********.470368, "relative_end": **********.470368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.473422, "relative_start": 1.7781000137329102, "end": **********.473422, "relative_end": **********.473422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.475369, "relative_start": 1.7800469398498535, "end": **********.475369, "relative_end": **********.475369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46010cb1cb88bb5ead5d94603a4a3d16", "start": **********.479155, "relative_start": 1.7838330268859863, "end": **********.479155, "relative_end": **********.479155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.481419, "relative_start": 1.7860970497131348, "end": **********.481419, "relative_end": **********.481419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.483026, "relative_start": 1.7877039909362793, "end": **********.483026, "relative_end": **********.483026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::34e3d89351b1208b7f313125eec52879", "start": **********.492419, "relative_start": 1.7970969676971436, "end": **********.492419, "relative_end": **********.492419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.496084, "relative_start": 1.8007619380950928, "end": **********.496084, "relative_end": **********.496084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::67034900569133b2c83b32da3dd4f5e5", "start": **********.499662, "relative_start": 1.80433988571167, "end": **********.499662, "relative_end": **********.499662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.502472, "relative_start": 1.807149887084961, "end": **********.502472, "relative_end": **********.502472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.505446, "relative_start": 1.8101239204406738, "end": **********.505446, "relative_end": **********.505446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.507097, "relative_start": 1.8117749691009521, "end": **********.507097, "relative_end": **********.507097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.508571, "relative_start": 1.813248872756958, "end": **********.508571, "relative_end": **********.508571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.510635, "relative_start": 1.8153128623962402, "end": **********.510635, "relative_end": **********.510635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.511342, "relative_start": 1.8160200119018555, "end": **********.511342, "relative_end": **********.511342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.512163, "relative_start": 1.8168408870697021, "end": **********.512163, "relative_end": **********.512163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.51412, "relative_start": 1.8187980651855469, "end": **********.51412, "relative_end": **********.51412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.51492, "relative_start": 1.8195979595184326, "end": **********.51492, "relative_end": **********.51492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d623715926c24f9fbc8a4b72c106d5d", "start": **********.518766, "relative_start": 1.82344388961792, "end": **********.518766, "relative_end": **********.518766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.52035, "relative_start": 1.8250279426574707, "end": **********.52035, "relative_end": **********.52035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6c50fc55276d93f8ed03f5c85273b6cc", "start": **********.522757, "relative_start": 1.82743501663208, "end": **********.522757, "relative_end": **********.522757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.524082, "relative_start": 1.8287599086761475, "end": **********.524082, "relative_end": **********.524082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::084d2b43c9ab4b881d9b34a15580aa2d", "start": **********.526514, "relative_start": 1.8311920166015625, "end": **********.526514, "relative_end": **********.526514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.528797, "relative_start": 1.83347487449646, "end": **********.528797, "relative_end": **********.528797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.533569, "relative_start": 1.8382470607757568, "end": **********.533569, "relative_end": **********.533569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.536256, "relative_start": 1.8409340381622314, "end": **********.536256, "relative_end": **********.536256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7db8cad89359963c1e9aa8fcc6c89817", "start": **********.540849, "relative_start": 1.845526933670044, "end": **********.540849, "relative_end": **********.540849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.542314, "relative_start": 1.846992015838623, "end": **********.542314, "relative_end": **********.542314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.543243, "relative_start": 1.8479208946228027, "end": **********.543243, "relative_end": **********.543243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.545944, "relative_start": 1.8506219387054443, "end": **********.545944, "relative_end": **********.545944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.5469, "relative_start": 1.8515779972076416, "end": **********.5469, "relative_end": **********.5469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.54789, "relative_start": 1.8525679111480713, "end": **********.54789, "relative_end": **********.54789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::325be2a8c3ca3843efa76c03adaee1dc", "start": **********.551483, "relative_start": 1.8561608791351318, "end": **********.551483, "relative_end": **********.551483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.554411, "relative_start": 1.8590888977050781, "end": **********.554411, "relative_end": **********.554411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e12a669ffa0346a27198bed32e63b7ba", "start": **********.557729, "relative_start": 1.8624069690704346, "end": **********.557729, "relative_end": **********.557729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.559846, "relative_start": 1.8645238876342773, "end": **********.559846, "relative_end": **********.559846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d0b20db301db9a47503a93a879bb206", "start": **********.562234, "relative_start": 1.8669118881225586, "end": **********.562234, "relative_end": **********.562234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.563808, "relative_start": 1.868485927581787, "end": **********.563808, "relative_end": **********.563808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b985af7bcdacbeac70eaf3979ad19f5a", "start": **********.566119, "relative_start": 1.8707969188690186, "end": **********.566119, "relative_end": **********.566119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.568168, "relative_start": 1.8728458881378174, "end": **********.568168, "relative_end": **********.568168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a0bb1d43b71cff86abe626fd376492e9", "start": **********.571546, "relative_start": 1.8762240409851074, "end": **********.571546, "relative_end": **********.571546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.57336, "relative_start": 1.878037929534912, "end": **********.57336, "relative_end": **********.57336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e25f2b305e6de46c04f91fa1ce50f68f", "start": **********.576031, "relative_start": 1.880708932876587, "end": **********.576031, "relative_end": **********.576031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.577905, "relative_start": 1.8825829029083252, "end": **********.577905, "relative_end": **********.577905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69152f707ea1358f8997b77a28e38a6f", "start": **********.579814, "relative_start": 1.8844919204711914, "end": **********.579814, "relative_end": **********.579814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.58109, "relative_start": 1.885767936706543, "end": **********.58109, "relative_end": **********.58109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b85eba35d3b7929c2988678b725baebf", "start": **********.582702, "relative_start": 1.8873798847198486, "end": **********.582702, "relative_end": **********.582702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.584054, "relative_start": 1.8887319564819336, "end": **********.584054, "relative_end": **********.584054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f3d3b83c612f68036b4d79d53ae851e", "start": **********.586535, "relative_start": 1.8912129402160645, "end": **********.586535, "relative_end": **********.586535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.588702, "relative_start": 1.8933799266815186, "end": **********.588702, "relative_end": **********.588702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.590821, "relative_start": 1.8954989910125732, "end": **********.590821, "relative_end": **********.590821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f143d1296cea16d82e2c87956e445593", "start": **********.595109, "relative_start": 1.8997869491577148, "end": **********.595109, "relative_end": **********.595109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.596514, "relative_start": 1.9011919498443604, "end": **********.596514, "relative_end": **********.596514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8cae4e5056b67c6778a54389a62ac7a0", "start": **********.598226, "relative_start": 1.9029040336608887, "end": **********.598226, "relative_end": **********.598226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.599406, "relative_start": 1.9040839672088623, "end": **********.599406, "relative_end": **********.599406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d5e509b6eb9084ec382ec05ccab41d1a", "start": **********.601162, "relative_start": 1.9058399200439453, "end": **********.601162, "relative_end": **********.601162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.604259, "relative_start": 1.9089369773864746, "end": **********.604259, "relative_end": **********.604259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.606181, "relative_start": 1.9108588695526123, "end": **********.606181, "relative_end": **********.606181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81a46563ed9378aa4d9a4fcb55e743e", "start": **********.60859, "relative_start": 1.9132678508758545, "end": **********.60859, "relative_end": **********.60859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.610177, "relative_start": 1.9148550033569336, "end": **********.610177, "relative_end": **********.610177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.612704, "relative_start": 1.917382001876831, "end": **********.612704, "relative_end": **********.612704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.613425, "relative_start": 1.9181029796600342, "end": **********.613425, "relative_end": **********.613425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.614272, "relative_start": 1.918950080871582, "end": **********.614272, "relative_end": **********.614272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.615142, "relative_start": 1.9198200702667236, "end": **********.615142, "relative_end": **********.615142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.615775, "relative_start": 1.9204530715942383, "end": **********.615775, "relative_end": **********.615775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.61654, "relative_start": 1.921217918395996, "end": **********.61654, "relative_end": **********.61654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da3c3de008e5793cbbdad005d78f49b1", "start": **********.618567, "relative_start": 1.9232449531555176, "end": **********.618567, "relative_end": **********.618567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.621204, "relative_start": 1.9258818626403809, "end": **********.621204, "relative_end": **********.621204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.6229, "relative_start": 1.9275779724121094, "end": **********.6229, "relative_end": **********.6229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.625549, "relative_start": 1.9302270412445068, "end": **********.625549, "relative_end": **********.625549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.626724, "relative_start": 1.9314019680023193, "end": **********.626724, "relative_end": **********.626724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.62837, "relative_start": 1.9330480098724365, "end": **********.62837, "relative_end": **********.62837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.license-invalid", "start": **********.62939, "relative_start": 1.934067964553833, "end": **********.62939, "relative_end": **********.62939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.630643, "relative_start": 1.9353208541870117, "end": **********.630643, "relative_end": **********.630643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.partials.license-activation-modal", "start": **********.631925, "relative_start": 1.93660306930542, "end": **********.631925, "relative_end": **********.631925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::license.form", "start": **********.633263, "relative_start": 1.937941074371338, "end": **********.633263, "relative_end": **********.633263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.635009, "relative_start": 1.9396870136260986, "end": **********.635009, "relative_end": **********.635009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27ec08f706fece52ef1cc0ec5563cef9", "start": **********.637698, "relative_start": 1.942375898361206, "end": **********.637698, "relative_end": **********.637698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.638946, "relative_start": 1.9436240196228027, "end": **********.638946, "relative_end": **********.638946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.640173, "relative_start": 1.9448509216308594, "end": **********.640173, "relative_end": **********.640173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.640891, "relative_start": 1.9455690383911133, "end": **********.640891, "relative_end": **********.640891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.641542, "relative_start": 1.9462199211120605, "end": **********.641542, "relative_end": **********.641542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.642025, "relative_start": 1.9467029571533203, "end": **********.642025, "relative_end": **********.642025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.642526, "relative_start": 1.9472038745880127, "end": **********.642526, "relative_end": **********.642526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.643507, "relative_start": 1.9481849670410156, "end": **********.643507, "relative_end": **********.643507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.643982, "relative_start": 1.948659896850586, "end": **********.643982, "relative_end": **********.643982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.64427, "relative_start": 1.9489479064941406, "end": **********.64427, "relative_end": **********.64427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.644622, "relative_start": 1.9493000507354736, "end": **********.644622, "relative_end": **********.644622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "start": **********.645203, "relative_start": 1.9498810768127441, "end": **********.645203, "relative_end": **********.645203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.646094, "relative_start": 1.9507720470428467, "end": **********.646094, "relative_end": **********.646094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.64689, "relative_start": 1.9515678882598877, "end": **********.64689, "relative_end": **********.64689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.64739, "relative_start": 1.9520678520202637, "end": **********.64739, "relative_end": **********.64739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.6484, "relative_start": 1.953078031539917, "end": **********.6484, "relative_end": **********.6484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.649283, "relative_start": 1.95396089553833, "end": **********.649283, "relative_end": **********.649283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.649967, "relative_start": 1.9546449184417725, "end": **********.649967, "relative_end": **********.649967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.650814, "relative_start": 1.9554920196533203, "end": **********.650814, "relative_end": **********.650814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.651577, "relative_start": 1.9562549591064453, "end": **********.651577, "relative_end": **********.651577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.654467, "relative_start": 1.9591450691223145, "end": **********.654467, "relative_end": **********.654467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.654867, "relative_start": 1.9595448970794678, "end": **********.654867, "relative_end": **********.654867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.656633, "relative_start": 1.961310863494873, "end": **********.656633, "relative_end": **********.656633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.657402, "relative_start": 1.9620800018310547, "end": **********.657402, "relative_end": **********.657402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.658096, "relative_start": 1.9627740383148193, "end": **********.658096, "relative_end": **********.658096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.658523, "relative_start": 1.9632010459899902, "end": **********.658523, "relative_end": **********.658523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53362b6227831afe8e4d7d3436ab607f", "start": **********.659959, "relative_start": 1.964637041091919, "end": **********.659959, "relative_end": **********.659959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e76aef074ac8ea84c711b8437720a22", "start": **********.661763, "relative_start": 1.9664409160614014, "end": **********.661763, "relative_end": **********.661763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::04edbddbda254d131a3439b11c880f12", "start": **********.66321, "relative_start": 1.9678878784179688, "end": **********.66321, "relative_end": **********.66321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.663954, "relative_start": 1.9686319828033447, "end": **********.663954, "relative_end": **********.663954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::custom-template", "start": **********.664945, "relative_start": 1.9696228504180908, "end": **********.664945, "relative_end": **********.664945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.665937, "relative_start": 1.9706149101257324, "end": **********.665937, "relative_end": **********.665937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.667127, "relative_start": 1.9718048572540283, "end": **********.667127, "relative_end": **********.667127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::loading", "start": **********.667666, "relative_start": 1.972343921661377, "end": **********.667666, "relative_end": **********.667666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.668523, "relative_start": 1.973201036453247, "end": **********.668523, "relative_end": **********.668523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.670298, "relative_start": 1.9749760627746582, "end": **********.670298, "relative_end": **********.670298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.670993, "relative_start": 1.9756710529327393, "end": **********.670993, "relative_end": **********.670993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.671437, "relative_start": 1.9761149883270264, "end": **********.671437, "relative_end": **********.671437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.checkbox", "start": **********.672048, "relative_start": 1.9767260551452637, "end": **********.672048, "relative_end": **********.672048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.673508, "relative_start": 1.9781858921051025, "end": **********.673508, "relative_end": **********.673508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.674401, "relative_start": 1.979079008102417, "end": **********.674401, "relative_end": **********.674401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.675209, "relative_start": 1.9798870086669922, "end": **********.675209, "relative_end": **********.675209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.676281, "relative_start": 1.9809589385986328, "end": **********.676281, "relative_end": **********.676281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.676945, "relative_start": 1.9816229343414307, "end": **********.676945, "relative_end": **********.676945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::debug-badge", "start": **********.049262, "relative_start": 2.353940010070801, "end": **********.049262, "relative_end": **********.049262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.050397, "relative_start": 2.355074882507324, "end": **********.050397, "relative_end": **********.050397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.051119, "relative_start": 2.355797052383423, "end": **********.051119, "relative_end": **********.051119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.052068, "relative_start": 2.356745958328247, "end": **********.052068, "relative_end": **********.052068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::93ebde3601860db875cfe9a96164bda6", "start": **********.053866, "relative_start": 2.358543872833252, "end": **********.053866, "relative_end": **********.053866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.055135, "relative_start": 2.3598129749298096, "end": **********.055135, "relative_end": **********.055135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.056448, "relative_start": 2.361125946044922, "end": **********.056448, "relative_end": **********.056448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.057359, "relative_start": 2.362036943435669, "end": **********.057359, "relative_end": **********.057359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.058144, "relative_start": 2.3628220558166504, "end": **********.058144, "relative_end": **********.058144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16c15d36d71c18d83a7e9e7e1b68a92b", "start": **********.059198, "relative_start": 2.3638758659362793, "end": **********.059198, "relative_end": **********.059198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.060139, "relative_start": 2.364816904067993, "end": **********.060139, "relative_end": **********.060139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::layouts.base", "start": **********.061272, "relative_start": 2.365949869155884, "end": **********.061272, "relative_end": **********.061272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.062887, "relative_start": 2.3675649166107178, "end": **********.062887, "relative_end": **********.062887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.066947, "relative_start": 2.3716249465942383, "end": **********.066947, "relative_end": **********.066947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.071654, "relative_start": 2.3763320446014404, "end": **********.071654, "relative_end": **********.071654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.074536, "relative_start": 2.37921404838562, "end": **********.074536, "relative_end": **********.074536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.076037, "relative_start": 2.3807148933410645, "end": **********.076037, "relative_end": **********.076037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 74723832, "peak_usage_str": "71MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 389, "nb_templates": 389, "templates": [{"name": "1x core/table::bulk-changes", "param_count": null, "params": [], "start": **********.860446, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-changes"}, {"name": "8x 8def1252668913628243c4d363bee1ef::dropdown.item", "param_count": null, "params": [], "start": **********.86507, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/item.blade.php8def1252668913628243c4d363bee1ef::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 8, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.item"}, {"name": "1x __components::53dbbce7846c20f5734d935076bf04ca", "param_count": null, "params": [], "start": **********.86975, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/53dbbce7846c20f5734d935076bf04ca.blade.php__components::53dbbce7846c20f5734d935076bf04ca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F53dbbce7846c20f5734d935076bf04ca.blade.php&line=1", "ajax": false, "filename": "53dbbce7846c20f5734d935076bf04ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53dbbce7846c20f5734d935076bf04ca"}, {"name": "2x core/table::partials.create", "param_count": null, "params": [], "start": **********.875663, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/partials/create.blade.phpcore/table::partials.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/table::partials.create"}, {"name": "2x __components::baaf02f6c56c328f3c307011a60b6b9f", "param_count": null, "params": [], "start": **********.877092, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/baaf02f6c56c328f3c307011a60b6b9f.blade.php__components::baaf02f6c56c328f3c307011a60b6b9f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbaaf02f6c56c328f3c307011a60b6b9f.blade.php&line=1", "ajax": false, "filename": "baaf02f6c56c328f3c307011a60b6b9f.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::baaf02f6c56c328f3c307011a60b6b9f"}, {"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.88061, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "2x __components::c33cfbd01dd1d76718fcd68287a40728", "param_count": null, "params": [], "start": **********.882155, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c33cfbd01dd1d76718fcd68287a40728.blade.php__components::c33cfbd01dd1d76718fcd68287a40728", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc33cfbd01dd1d76718fcd68287a40728.blade.php&line=1", "ajax": false, "filename": "c33cfbd01dd1d76718fcd68287a40728.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::c33cfbd01dd1d76718fcd68287a40728"}, {"name": "1x 8def1252668913628243c4d363bee1ef::badge", "param_count": null, "params": [], "start": **********.883188, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/badge.blade.php8def1252668913628243c4d363bee1ef::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::badge"}, {"name": "1x core/table::table", "param_count": null, "params": [], "start": **********.900474, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/table.blade.phpcore/table::table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table"}, {"name": "1x core/table::base-table", "param_count": null, "params": [], "start": **********.901321, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.phpcore/table::base-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbase-table.blade.php&line=1", "ajax": false, "filename": "base-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::base-table"}, {"name": "14x 8def1252668913628243c4d363bee1ef::button", "param_count": null, "params": [], "start": **********.934854, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/button.blade.php8def1252668913628243c4d363bee1ef::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 14, "name_original": "8def1252668913628243c4d363bee1ef::button"}, {"name": "1x __components::14ad31fb3af14d3ba24d3c578af35e73", "param_count": null, "params": [], "start": **********.937354, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/14ad31fb3af14d3ba24d3c578af35e73.blade.php__components::14ad31fb3af14d3ba24d3c578af35e73", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F14ad31fb3af14d3ba24d3c578af35e73.blade.php&line=1", "ajax": false, "filename": "14ad31fb3af14d3ba24d3c578af35e73.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::14ad31fb3af14d3ba24d3c578af35e73"}, {"name": "1x core/table::filter", "param_count": null, "params": [], "start": **********.975596, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/filter.blade.phpcore/table::filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ffilter.blade.php&line=1", "ajax": false, "filename": "filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::filter"}, {"name": "4x 8def1252668913628243c4d363bee1ef::form.select", "param_count": null, "params": [], "start": **********.977584, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/select.blade.php8def1252668913628243c4d363bee1ef::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "8def1252668913628243c4d363bee1ef::form.select"}, {"name": "11x 8def1252668913628243c4d363bee1ef::form-group", "param_count": null, "params": [], "start": **********.97982, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form-group.blade.php8def1252668913628243c4d363bee1ef::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::form-group"}, {"name": "1x __components::d7b9d721822c50540453b6fa0f4bd081", "param_count": null, "params": [], "start": **********.982732, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7b9d721822c50540453b6fa0f4bd081.blade.php__components::d7b9d721822c50540453b6fa0f4bd081", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7b9d721822c50540453b6fa0f4bd081.blade.php&line=1", "ajax": false, "filename": "d7b9d721822c50540453b6fa0f4bd081.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b9d721822c50540453b6fa0f4bd081"}, {"name": "2x __components::0c8728926b3975e33a051ebb6ef68e5d", "param_count": null, "params": [], "start": **********.99101, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0c8728926b3975e33a051ebb6ef68e5d.blade.php__components::0c8728926b3975e33a051ebb6ef68e5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0c8728926b3975e33a051ebb6ef68e5d.blade.php&line=1", "ajax": false, "filename": "0c8728926b3975e33a051ebb6ef68e5d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0c8728926b3975e33a051ebb6ef68e5d"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.index", "param_count": null, "params": [], "start": **********.991926, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/index.blade.php8def1252668913628243c4d363bee1ef::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.index"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.body.index", "param_count": null, "params": [], "start": **********.992444, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/body/index.blade.php8def1252668913628243c4d363bee1ef::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.body.index"}, {"name": "4x 8def1252668913628243c4d363bee1ef::card.index", "param_count": null, "params": [], "start": **********.992924, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/index.blade.php8def1252668913628243c4d363bee1ef::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "8def1252668913628243c4d363bee1ef::card.index"}, {"name": "1x core/table::bulk-action", "param_count": null, "params": [], "start": **********.032115, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/bulk-action.blade.phpcore/table::bulk-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-action.blade.php&line=1", "ajax": false, "filename": "bulk-action.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-action"}, {"name": "3x 8def1252668913628243c4d363bee1ef::dropdown.index", "param_count": null, "params": [], "start": **********.035078, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/index.blade.php8def1252668913628243c4d363bee1ef::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.index"}, {"name": "2x __components::6f3b10173cc6f5c541f27080145e1a40", "param_count": null, "params": [], "start": **********.05803, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6f3b10173cc6f5c541f27080145e1a40.blade.php__components::6f3b10173cc6f5c541f27080145e1a40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6f3b10173cc6f5c541f27080145e1a40.blade.php&line=1", "ajax": false, "filename": "6f3b10173cc6f5c541f27080145e1a40.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6f3b10173cc6f5c541f27080145e1a40"}, {"name": "1x __components::42e1966f95bce065f65d4b22e53f3772", "param_count": null, "params": [], "start": **********.059594, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/42e1966f95bce065f65d4b22e53f3772.blade.php__components::42e1966f95bce065f65d4b22e53f3772", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F42e1966f95bce065f65d4b22e53f3772.blade.php&line=1", "ajax": false, "filename": "42e1966f95bce065f65d4b22e53f3772.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42e1966f95bce065f65d4b22e53f3772"}, {"name": "1x __components::b4335e9ff8c721e014fa31a65e4454d3", "param_count": null, "params": [], "start": **********.062889, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b4335e9ff8c721e014fa31a65e4454d3.blade.php__components::b4335e9ff8c721e014fa31a65e4454d3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb4335e9ff8c721e014fa31a65e4454d3.blade.php&line=1", "ajax": false, "filename": "b4335e9ff8c721e014fa31a65e4454d3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b4335e9ff8c721e014fa31a65e4454d3"}, {"name": "1x __components::77e239c329a8f8a48840d651c6a8a9ee", "param_count": null, "params": [], "start": **********.064501, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/77e239c329a8f8a48840d651c6a8a9ee.blade.php__components::77e239c329a8f8a48840d651c6a8a9ee", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F77e239c329a8f8a48840d651c6a8a9ee.blade.php&line=1", "ajax": false, "filename": "77e239c329a8f8a48840d651c6a8a9ee.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::77e239c329a8f8a48840d651c6a8a9ee"}, {"name": "1x __components::7b2a6f88f6865c9dfe436f51388f0b67", "param_count": null, "params": [], "start": **********.065878, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7b2a6f88f6865c9dfe436f51388f0b67.blade.php__components::7b2a6f88f6865c9dfe436f51388f0b67", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7b2a6f88f6865c9dfe436f51388f0b67.blade.php&line=1", "ajax": false, "filename": "7b2a6f88f6865c9dfe436f51388f0b67.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7b2a6f88f6865c9dfe436f51388f0b67"}, {"name": "3x 8def1252668913628243c4d363bee1ef::card.header.index", "param_count": null, "params": [], "start": **********.068984, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/header/index.blade.php8def1252668913628243c4d363bee1ef::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::card.header.index"}, {"name": "1x core/table::modal", "param_count": null, "params": [], "start": **********.091386, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/modal.blade.phpcore/table::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::modal"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.action", "param_count": null, "params": [], "start": **********.093262, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/action.blade.php8def1252668913628243c4d363bee1ef::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.action"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.alert", "param_count": null, "params": [], "start": **********.094789, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/alert.blade.php8def1252668913628243c4d363bee1ef::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.alert"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal.close-button", "param_count": null, "params": [], "start": **********.096154, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/close-button.blade.php8def1252668913628243c4d363bee1ef::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal.close-button"}, {"name": "4x __components::d17cb0db54485d707113609802086895", "param_count": null, "params": [], "start": **********.097306, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d17cb0db54485d707113609802086895.blade.php__components::d17cb0db54485d707113609802086895", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd17cb0db54485d707113609802086895.blade.php&line=1", "ajax": false, "filename": "d17cb0db54485d707113609802086895.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::d17cb0db54485d707113609802086895"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal", "param_count": null, "params": [], "start": **********.098179, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal.blade.php8def1252668913628243c4d363bee1ef::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal"}, {"name": "1x core/table::script", "param_count": null, "params": [], "start": **********.113152, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/script.blade.phpcore/table::script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::script"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.114839, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.118226, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.119009, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::3a4eb377d01a3c4bb09865b43ffbd313", "param_count": null, "params": [], "start": **********.121469, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3a4eb377d01a3c4bb09865b43ffbd313.blade.php__components::3a4eb377d01a3c4bb09865b43ffbd313", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3a4eb377d01a3c4bb09865b43ffbd313.blade.php&line=1", "ajax": false, "filename": "3a4eb377d01a3c4bb09865b43ffbd313.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::3a4eb377d01a3c4bb09865b43ffbd313"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.122678, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.136726, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.text-input", "param_count": null, "params": [], "start": **********.137968, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/text-input.blade.php8def1252668913628243c4d363bee1ef::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.text-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.label", "param_count": null, "params": [], "start": **********.139655, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/label.blade.php8def1252668913628243c4d363bee1ef::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.label"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.error", "param_count": null, "params": [], "start": **********.140284, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/error.blade.php8def1252668913628243c4d363bee1ef::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.error"}, {"name": "1x __components::d2cfde89f704c31422aff2fae16ddb81", "param_count": null, "params": [], "start": **********.14326, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d2cfde89f704c31422aff2fae16ddb81.blade.php__components::d2cfde89f704c31422aff2fae16ddb81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd2cfde89f704c31422aff2fae16ddb81.blade.php&line=1", "ajax": false, "filename": "d2cfde89f704c31422aff2fae16ddb81.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d2cfde89f704c31422aff2fae16ddb81"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.1443, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::a5645d2a1f3c74251fc89224c575fed8", "param_count": null, "params": [], "start": **********.145866, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a5645d2a1f3c74251fc89224c575fed8.blade.php__components::a5645d2a1f3c74251fc89224c575fed8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa5645d2a1f3c74251fc89224c575fed8.blade.php&line=1", "ajax": false, "filename": "a5645d2a1f3c74251fc89224c575fed8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a5645d2a1f3c74251fc89224c575fed8"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.147736, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::639d159f54869d7a8362974885dec505", "param_count": null, "params": [], "start": **********.148931, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/639d159f54869d7a8362974885dec505.blade.php__components::639d159f54869d7a8362974885dec505", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F639d159f54869d7a8362974885dec505.blade.php&line=1", "ajax": false, "filename": "639d159f54869d7a8362974885dec505.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::639d159f54869d7a8362974885dec505"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.153537, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::cf41524c2db4e8ac4f30aba28550db55", "param_count": null, "params": [], "start": **********.155824, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/cf41524c2db4e8ac4f30aba28550db55.blade.php__components::cf41524c2db4e8ac4f30aba28550db55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fcf41524c2db4e8ac4f30aba28550db55.blade.php&line=1", "ajax": false, "filename": "cf41524c2db4e8ac4f30aba28550db55.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::cf41524c2db4e8ac4f30aba28550db55"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.title", "param_count": null, "params": [], "start": **********.157529, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/title.blade.php8def1252668913628243c4d363bee1ef::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.title"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.actions", "param_count": null, "params": [], "start": **********.158591, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/actions.blade.php8def1252668913628243c4d363bee1ef::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.actions"}, {"name": "1x plugins/ecommerce::orders.notification", "param_count": null, "params": [], "start": **********.198602, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/notification.blade.phpplugins/ecommerce::orders.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.notification"}, {"name": "1x __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "param_count": null, "params": [], "start": **********.200449, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php__components::8394ebb1c2841e3a1166cd3fb0a6e03f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php&line=1", "ajax": false, "filename": "8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8394ebb1c2841e3a1166cd3fb0a6e03f"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.220665, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "2x __components::d059faaba602d6895d68258ab3c890a6", "param_count": null, "params": [], "start": **********.230007, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d059faaba602d6895d68258ab3c890a6.blade.php__components::d059faaba602d6895d68258ab3c890a6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd059faaba602d6895d68258ab3c890a6.blade.php&line=1", "ajax": false, "filename": "d059faaba602d6895d68258ab3c890a6.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d059faaba602d6895d68258ab3c890a6"}, {"name": "2x __components::a3cb4601eb64a80dc01a3c268590a3c8", "param_count": null, "params": [], "start": **********.232238, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a3cb4601eb64a80dc01a3c268590a3c8.blade.php__components::a3cb4601eb64a80dc01a3c268590a3c8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa3cb4601eb64a80dc01a3c268590a3c8.blade.php&line=1", "ajax": false, "filename": "a3cb4601eb64a80dc01a3c268590a3c8.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::a3cb4601eb64a80dc01a3c268590a3c8"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.233937, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.24064, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.241344, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "22x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.263641, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 22, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "79x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.265053, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 79, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::e3b17f7ce9738894b58a8b70b9624457", "param_count": null, "params": [], "start": **********.26701, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e3b17f7ce9738894b58a8b70b9624457.blade.php__components::e3b17f7ce9738894b58a8b70b9624457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe3b17f7ce9738894b58a8b70b9624457.blade.php&line=1", "ajax": false, "filename": "e3b17f7ce9738894b58a8b70b9624457.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3b17f7ce9738894b58a8b70b9624457"}, {"name": "1x __components::0c6e6838aa476b78aace81114936689c", "param_count": null, "params": [], "start": **********.271934, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0c6e6838aa476b78aace81114936689c.blade.php__components::0c6e6838aa476b78aace81114936689c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0c6e6838aa476b78aace81114936689c.blade.php&line=1", "ajax": false, "filename": "0c6e6838aa476b78aace81114936689c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0c6e6838aa476b78aace81114936689c"}, {"name": "11x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.274528, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "11x 8def1252668913628243c4d363bee1ef::navbar.badge-count", "param_count": null, "params": [], "start": **********.276001, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/navbar/badge-count.blade.php8def1252668913628243c4d363bee1ef::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::navbar.badge-count"}, {"name": "1x __components::37fae22c8e215ea2e54e69a5e3a007cc", "param_count": null, "params": [], "start": **********.279916, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/37fae22c8e215ea2e54e69a5e3a007cc.blade.php__components::37fae22c8e215ea2e54e69a5e3a007cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F37fae22c8e215ea2e54e69a5e3a007cc.blade.php&line=1", "ajax": false, "filename": "37fae22c8e215ea2e54e69a5e3a007cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::37fae22c8e215ea2e54e69a5e3a007cc"}, {"name": "1x __components::8916176d99d4ae2024cd36e11e35b821", "param_count": null, "params": [], "start": **********.282764, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8916176d99d4ae2024cd36e11e35b821.blade.php__components::8916176d99d4ae2024cd36e11e35b821", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8916176d99d4ae2024cd36e11e35b821.blade.php&line=1", "ajax": false, "filename": "8916176d99d4ae2024cd36e11e35b821.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8916176d99d4ae2024cd36e11e35b821"}, {"name": "1x __components::bd27433a6607127acdaf6dc541ab2435", "param_count": null, "params": [], "start": **********.28713, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/bd27433a6607127acdaf6dc541ab2435.blade.php__components::bd27433a6607127acdaf6dc541ab2435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbd27433a6607127acdaf6dc541ab2435.blade.php&line=1", "ajax": false, "filename": "bd27433a6607127acdaf6dc541ab2435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd27433a6607127acdaf6dc541ab2435"}, {"name": "1x __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "param_count": null, "params": [], "start": **********.291594, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php__components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php&line=1", "ajax": false, "filename": "5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5f4f9ebbae249bdc8b0d599a1ac6ad06"}, {"name": "1x __components::080b92e00b37bcc97c1cd249894494a2", "param_count": null, "params": [], "start": **********.295935, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/080b92e00b37bcc97c1cd249894494a2.blade.php__components::080b92e00b37bcc97c1cd249894494a2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F080b92e00b37bcc97c1cd249894494a2.blade.php&line=1", "ajax": false, "filename": "080b92e00b37bcc97c1cd249894494a2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::080b92e00b37bcc97c1cd249894494a2"}, {"name": "1x __components::6a26943e77184871de1629f41c534094", "param_count": null, "params": [], "start": **********.299284, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6a26943e77184871de1629f41c534094.blade.php__components::6a26943e77184871de1629f41c534094", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6a26943e77184871de1629f41c534094.blade.php&line=1", "ajax": false, "filename": "6a26943e77184871de1629f41c534094.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6a26943e77184871de1629f41c534094"}, {"name": "1x __components::8f29f8012139c7a3eb6593c906e1db38", "param_count": null, "params": [], "start": **********.302876, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8f29f8012139c7a3eb6593c906e1db38.blade.php__components::8f29f8012139c7a3eb6593c906e1db38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8f29f8012139c7a3eb6593c906e1db38.blade.php&line=1", "ajax": false, "filename": "8f29f8012139c7a3eb6593c906e1db38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8f29f8012139c7a3eb6593c906e1db38"}, {"name": "1x __components::4553f5b37130b2effba490dbdf5419d2", "param_count": null, "params": [], "start": **********.30814, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/4553f5b37130b2effba490dbdf5419d2.blade.php__components::4553f5b37130b2effba490dbdf5419d2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F4553f5b37130b2effba490dbdf5419d2.blade.php&line=1", "ajax": false, "filename": "4553f5b37130b2effba490dbdf5419d2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4553f5b37130b2effba490dbdf5419d2"}, {"name": "1x __components::59c947fc9b2121a5885d4f4e7b1242d8", "param_count": null, "params": [], "start": **********.311856, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/59c947fc9b2121a5885d4f4e7b1242d8.blade.php__components::59c947fc9b2121a5885d4f4e7b1242d8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F59c947fc9b2121a5885d4f4e7b1242d8.blade.php&line=1", "ajax": false, "filename": "59c947fc9b2121a5885d4f4e7b1242d8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::59c947fc9b2121a5885d4f4e7b1242d8"}, {"name": "1x __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "param_count": null, "params": [], "start": **********.314521, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php__components::1e9698c460b468bfcaf0f7dbbebf9bf0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php&line=1", "ajax": false, "filename": "1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1e9698c460b468bfcaf0f7dbbebf9bf0"}, {"name": "2x __components::0d4eb4544a5328bea40b7b01743b8f82", "param_count": null, "params": [], "start": **********.317285, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d4eb4544a5328bea40b7b01743b8f82.blade.php__components::0d4eb4544a5328bea40b7b01743b8f82", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d4eb4544a5328bea40b7b01743b8f82.blade.php&line=1", "ajax": false, "filename": "0d4eb4544a5328bea40b7b01743b8f82.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0d4eb4544a5328bea40b7b01743b8f82"}, {"name": "2x __components::5270fef4db64e6c2fedf42ea8ac88f25", "param_count": null, "params": [], "start": **********.322215, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5270fef4db64e6c2fedf42ea8ac88f25.blade.php__components::5270fef4db64e6c2fedf42ea8ac88f25", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5270fef4db64e6c2fedf42ea8ac88f25.blade.php&line=1", "ajax": false, "filename": "5270fef4db64e6c2fedf42ea8ac88f25.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5270fef4db64e6c2fedf42ea8ac88f25"}, {"name": "1x __components::84f17fac377525e2e49f32058361220b", "param_count": null, "params": [], "start": **********.327099, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/84f17fac377525e2e49f32058361220b.blade.php__components::84f17fac377525e2e49f32058361220b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F84f17fac377525e2e49f32058361220b.blade.php&line=1", "ajax": false, "filename": "84f17fac377525e2e49f32058361220b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::84f17fac377525e2e49f32058361220b"}, {"name": "1x __components::1c8617dee734f51544a3883923ddca6f", "param_count": null, "params": [], "start": **********.332927, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c8617dee734f51544a3883923ddca6f.blade.php__components::1c8617dee734f51544a3883923ddca6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c8617dee734f51544a3883923ddca6f.blade.php&line=1", "ajax": false, "filename": "1c8617dee734f51544a3883923ddca6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c8617dee734f51544a3883923ddca6f"}, {"name": "1x __components::3d3bfe5e8598abeb74083f6c26233cb5", "param_count": null, "params": [], "start": **********.336961, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3d3bfe5e8598abeb74083f6c26233cb5.blade.php__components::3d3bfe5e8598abeb74083f6c26233cb5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3d3bfe5e8598abeb74083f6c26233cb5.blade.php&line=1", "ajax": false, "filename": "3d3bfe5e8598abeb74083f6c26233cb5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3d3bfe5e8598abeb74083f6c26233cb5"}, {"name": "1x __components::35fe997a7b87ef55d749630606a50a1b", "param_count": null, "params": [], "start": **********.341258, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/35fe997a7b87ef55d749630606a50a1b.blade.php__components::35fe997a7b87ef55d749630606a50a1b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F35fe997a7b87ef55d749630606a50a1b.blade.php&line=1", "ajax": false, "filename": "35fe997a7b87ef55d749630606a50a1b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::35fe997a7b87ef55d749630606a50a1b"}, {"name": "1x __components::a4f1583597dec7e67a8ae044f0915dbe", "param_count": null, "params": [], "start": **********.347388, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a4f1583597dec7e67a8ae044f0915dbe.blade.php__components::a4f1583597dec7e67a8ae044f0915dbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa4f1583597dec7e67a8ae044f0915dbe.blade.php&line=1", "ajax": false, "filename": "a4f1583597dec7e67a8ae044f0915dbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a4f1583597dec7e67a8ae044f0915dbe"}, {"name": "1x __components::c0cbd16b0cc2226ec5536610974ba3c3", "param_count": null, "params": [], "start": **********.351449, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c0cbd16b0cc2226ec5536610974ba3c3.blade.php__components::c0cbd16b0cc2226ec5536610974ba3c3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc0cbd16b0cc2226ec5536610974ba3c3.blade.php&line=1", "ajax": false, "filename": "c0cbd16b0cc2226ec5536610974ba3c3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c0cbd16b0cc2226ec5536610974ba3c3"}, {"name": "2x __components::70b8df706e60982a72f15e9e2d486203", "param_count": null, "params": [], "start": **********.356447, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/70b8df706e60982a72f15e9e2d486203.blade.php__components::70b8df706e60982a72f15e9e2d486203", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F70b8df706e60982a72f15e9e2d486203.blade.php&line=1", "ajax": false, "filename": "70b8df706e60982a72f15e9e2d486203.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::70b8df706e60982a72f15e9e2d486203"}, {"name": "1x __components::dc78b90963e9d9963376e0e829411cea", "param_count": null, "params": [], "start": **********.36253, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/dc78b90963e9d9963376e0e829411cea.blade.php__components::dc78b90963e9d9963376e0e829411cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fdc78b90963e9d9963376e0e829411cea.blade.php&line=1", "ajax": false, "filename": "dc78b90963e9d9963376e0e829411cea.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc78b90963e9d9963376e0e829411cea"}, {"name": "4x __components::7a6e3d0dfcd673b5659893aa4dd54e33", "param_count": null, "params": [], "start": **********.365658, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7a6e3d0dfcd673b5659893aa4dd54e33.blade.php__components::7a6e3d0dfcd673b5659893aa4dd54e33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7a6e3d0dfcd673b5659893aa4dd54e33.blade.php&line=1", "ajax": false, "filename": "7a6e3d0dfcd673b5659893aa4dd54e33.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::7a6e3d0dfcd673b5659893aa4dd54e33"}, {"name": "1x __components::481a833ebeb573258c941c925aa45f7b", "param_count": null, "params": [], "start": **********.369257, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/481a833ebeb573258c941c925aa45f7b.blade.php__components::481a833ebeb573258c941c925aa45f7b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F481a833ebeb573258c941c925aa45f7b.blade.php&line=1", "ajax": false, "filename": "481a833ebeb573258c941c925aa45f7b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::481a833ebeb573258c941c925aa45f7b"}, {"name": "1x __components::b42bb0aa5fceca31ad61711414a614f0", "param_count": null, "params": [], "start": **********.37249, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b42bb0aa5fceca31ad61711414a614f0.blade.php__components::b42bb0aa5fceca31ad61711414a614f0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb42bb0aa5fceca31ad61711414a614f0.blade.php&line=1", "ajax": false, "filename": "b42bb0aa5fceca31ad61711414a614f0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b42bb0aa5fceca31ad61711414a614f0"}, {"name": "3x __components::1c969038219bd5c599f1ca2d81401cea", "param_count": null, "params": [], "start": **********.377717, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c969038219bd5c599f1ca2d81401cea.blade.php__components::1c969038219bd5c599f1ca2d81401cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c969038219bd5c599f1ca2d81401cea.blade.php&line=1", "ajax": false, "filename": "1c969038219bd5c599f1ca2d81401cea.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1c969038219bd5c599f1ca2d81401cea"}, {"name": "1x __components::471e83668278198d730a7a3f4a475d45", "param_count": null, "params": [], "start": **********.381069, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/471e83668278198d730a7a3f4a475d45.blade.php__components::471e83668278198d730a7a3f4a475d45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F471e83668278198d730a7a3f4a475d45.blade.php&line=1", "ajax": false, "filename": "471e83668278198d730a7a3f4a475d45.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::471e83668278198d730a7a3f4a475d45"}, {"name": "1x __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "param_count": null, "params": [], "start": **********.386417, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php__components::d7b40194b2b4ba91a975fc9aafe2d3e8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7b40194b2b4ba91a975fc9aafe2d3e8.blade.php&line=1", "ajax": false, "filename": "d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b40194b2b4ba91a975fc9aafe2d3e8"}, {"name": "1x __components::acb69140835a74210411469faeab3034", "param_count": null, "params": [], "start": **********.394594, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/acb69140835a74210411469faeab3034.blade.php__components::acb69140835a74210411469faeab3034", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Facb69140835a74210411469faeab3034.blade.php&line=1", "ajax": false, "filename": "acb69140835a74210411469faeab3034.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acb69140835a74210411469faeab3034"}, {"name": "1x __components::19cd49cd69455e40dc223df6b4eaf954", "param_count": null, "params": [], "start": **********.399308, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/19cd49cd69455e40dc223df6b4eaf954.blade.php__components::19cd49cd69455e40dc223df6b4eaf954", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F19cd49cd69455e40dc223df6b4eaf954.blade.php&line=1", "ajax": false, "filename": "19cd49cd69455e40dc223df6b4eaf954.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19cd49cd69455e40dc223df6b4eaf954"}, {"name": "1x __components::2979b72aeeca0047ecdecc3ad66e7e16", "param_count": null, "params": [], "start": **********.403518, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/2979b72aeeca0047ecdecc3ad66e7e16.blade.php__components::2979b72aeeca0047ecdecc3ad66e7e16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F2979b72aeeca0047ecdecc3ad66e7e16.blade.php&line=1", "ajax": false, "filename": "2979b72aeeca0047ecdecc3ad66e7e16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2979b72aeeca0047ecdecc3ad66e7e16"}, {"name": "1x __components::3c40febd70fcdc245d99ae7cd02cface", "param_count": null, "params": [], "start": **********.407625, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3c40febd70fcdc245d99ae7cd02cface.blade.php__components::3c40febd70fcdc245d99ae7cd02cface", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3c40febd70fcdc245d99ae7cd02cface.blade.php&line=1", "ajax": false, "filename": "3c40febd70fcdc245d99ae7cd02cface.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3c40febd70fcdc245d99ae7cd02cface"}, {"name": "2x __components::90ccac5c8bbb25741ef262bfd81c7551", "param_count": null, "params": [], "start": **********.410825, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/90ccac5c8bbb25741ef262bfd81c7551.blade.php__components::90ccac5c8bbb25741ef262bfd81c7551", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F90ccac5c8bbb25741ef262bfd81c7551.blade.php&line=1", "ajax": false, "filename": "90ccac5c8bbb25741ef262bfd81c7551.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::90ccac5c8bbb25741ef262bfd81c7551"}, {"name": "1x __components::d7ced212b797c29086a7922a858f3070", "param_count": null, "params": [], "start": **********.4186, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7ced212b797c29086a7922a858f3070.blade.php__components::d7ced212b797c29086a7922a858f3070", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7ced212b797c29086a7922a858f3070.blade.php&line=1", "ajax": false, "filename": "d7ced212b797c29086a7922a858f3070.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7ced212b797c29086a7922a858f3070"}, {"name": "2x __components::9d41a7757b46012fb4a0d6634d04a1e0", "param_count": null, "params": [], "start": **********.424032, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d41a7757b46012fb4a0d6634d04a1e0.blade.php__components::9d41a7757b46012fb4a0d6634d04a1e0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d41a7757b46012fb4a0d6634d04a1e0.blade.php&line=1", "ajax": false, "filename": "9d41a7757b46012fb4a0d6634d04a1e0.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9d41a7757b46012fb4a0d6634d04a1e0"}, {"name": "3x __components::86b7e33bd2198279086ebb1f21c0e2cc", "param_count": null, "params": [], "start": **********.430225, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/86b7e33bd2198279086ebb1f21c0e2cc.blade.php__components::86b7e33bd2198279086ebb1f21c0e2cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F86b7e33bd2198279086ebb1f21c0e2cc.blade.php&line=1", "ajax": false, "filename": "86b7e33bd2198279086ebb1f21c0e2cc.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::86b7e33bd2198279086ebb1f21c0e2cc"}, {"name": "1x __components::8c52d9b1ef0685ec10fdc3e877751e02", "param_count": null, "params": [], "start": **********.438568, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8c52d9b1ef0685ec10fdc3e877751e02.blade.php__components::8c52d9b1ef0685ec10fdc3e877751e02", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8c52d9b1ef0685ec10fdc3e877751e02.blade.php&line=1", "ajax": false, "filename": "8c52d9b1ef0685ec10fdc3e877751e02.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8c52d9b1ef0685ec10fdc3e877751e02"}, {"name": "1x __components::42668be6e8e5266862c6994eaa88bb55", "param_count": null, "params": [], "start": **********.443947, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/42668be6e8e5266862c6994eaa88bb55.blade.php__components::42668be6e8e5266862c6994eaa88bb55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F42668be6e8e5266862c6994eaa88bb55.blade.php&line=1", "ajax": false, "filename": "42668be6e8e5266862c6994eaa88bb55.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42668be6e8e5266862c6994eaa88bb55"}, {"name": "1x __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "param_count": null, "params": [], "start": **********.448142, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php__components::311d8c591d63d3cbd12dceb2fb1ac1c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php&line=1", "ajax": false, "filename": "311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::311d8c591d63d3cbd12dceb2fb1ac1c1"}, {"name": "1x __components::613233f0072612a02c74dd1699c0b74c", "param_count": null, "params": [], "start": **********.465045, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/613233f0072612a02c74dd1699c0b74c.blade.php__components::613233f0072612a02c74dd1699c0b74c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F613233f0072612a02c74dd1699c0b74c.blade.php&line=1", "ajax": false, "filename": "613233f0072612a02c74dd1699c0b74c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::613233f0072612a02c74dd1699c0b74c"}, {"name": "1x __components::16225ede2ef5cc17292fd2eb9026fc80", "param_count": null, "params": [], "start": **********.470307, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16225ede2ef5cc17292fd2eb9026fc80.blade.php__components::16225ede2ef5cc17292fd2eb9026fc80", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16225ede2ef5cc17292fd2eb9026fc80.blade.php&line=1", "ajax": false, "filename": "16225ede2ef5cc17292fd2eb9026fc80.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16225ede2ef5cc17292fd2eb9026fc80"}, {"name": "1x __components::46010cb1cb88bb5ead5d94603a4a3d16", "param_count": null, "params": [], "start": **********.479126, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/46010cb1cb88bb5ead5d94603a4a3d16.blade.php__components::46010cb1cb88bb5ead5d94603a4a3d16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F46010cb1cb88bb5ead5d94603a4a3d16.blade.php&line=1", "ajax": false, "filename": "46010cb1cb88bb5ead5d94603a4a3d16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46010cb1cb88bb5ead5d94603a4a3d16"}, {"name": "1x __components::34e3d89351b1208b7f313125eec52879", "param_count": null, "params": [], "start": **********.492335, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/34e3d89351b1208b7f313125eec52879.blade.php__components::34e3d89351b1208b7f313125eec52879", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F34e3d89351b1208b7f313125eec52879.blade.php&line=1", "ajax": false, "filename": "34e3d89351b1208b7f313125eec52879.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::34e3d89351b1208b7f313125eec52879"}, {"name": "1x __components::67034900569133b2c83b32da3dd4f5e5", "param_count": null, "params": [], "start": **********.499632, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/67034900569133b2c83b32da3dd4f5e5.blade.php__components::67034900569133b2c83b32da3dd4f5e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F67034900569133b2c83b32da3dd4f5e5.blade.php&line=1", "ajax": false, "filename": "67034900569133b2c83b32da3dd4f5e5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::67034900569133b2c83b32da3dd4f5e5"}, {"name": "1x __components::0d623715926c24f9fbc8a4b72c106d5d", "param_count": null, "params": [], "start": **********.51874, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d623715926c24f9fbc8a4b72c106d5d.blade.php__components::0d623715926c24f9fbc8a4b72c106d5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d623715926c24f9fbc8a4b72c106d5d.blade.php&line=1", "ajax": false, "filename": "0d623715926c24f9fbc8a4b72c106d5d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d623715926c24f9fbc8a4b72c106d5d"}, {"name": "1x __components::6c50fc55276d93f8ed03f5c85273b6cc", "param_count": null, "params": [], "start": **********.522722, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6c50fc55276d93f8ed03f5c85273b6cc.blade.php__components::6c50fc55276d93f8ed03f5c85273b6cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6c50fc55276d93f8ed03f5c85273b6cc.blade.php&line=1", "ajax": false, "filename": "6c50fc55276d93f8ed03f5c85273b6cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6c50fc55276d93f8ed03f5c85273b6cc"}, {"name": "1x __components::084d2b43c9ab4b881d9b34a15580aa2d", "param_count": null, "params": [], "start": **********.526481, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/084d2b43c9ab4b881d9b34a15580aa2d.blade.php__components::084d2b43c9ab4b881d9b34a15580aa2d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F084d2b43c9ab4b881d9b34a15580aa2d.blade.php&line=1", "ajax": false, "filename": "084d2b43c9ab4b881d9b34a15580aa2d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::084d2b43c9ab4b881d9b34a15580aa2d"}, {"name": "2x __components::b33d20952e90e5c4a596ff58ad010448", "param_count": null, "params": [], "start": **********.533506, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b33d20952e90e5c4a596ff58ad010448.blade.php__components::b33d20952e90e5c4a596ff58ad010448", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb33d20952e90e5c4a596ff58ad010448.blade.php&line=1", "ajax": false, "filename": "b33d20952e90e5c4a596ff58ad010448.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b33d20952e90e5c4a596ff58ad010448"}, {"name": "1x __components::7db8cad89359963c1e9aa8fcc6c89817", "param_count": null, "params": [], "start": **********.540817, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7db8cad89359963c1e9aa8fcc6c89817.blade.php__components::7db8cad89359963c1e9aa8fcc6c89817", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7db8cad89359963c1e9aa8fcc6c89817.blade.php&line=1", "ajax": false, "filename": "7db8cad89359963c1e9aa8fcc6c89817.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7db8cad89359963c1e9aa8fcc6c89817"}, {"name": "1x __components::325be2a8c3ca3843efa76c03adaee1dc", "param_count": null, "params": [], "start": **********.551432, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/325be2a8c3ca3843efa76c03adaee1dc.blade.php__components::325be2a8c3ca3843efa76c03adaee1dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F325be2a8c3ca3843efa76c03adaee1dc.blade.php&line=1", "ajax": false, "filename": "325be2a8c3ca3843efa76c03adaee1dc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::325be2a8c3ca3843efa76c03adaee1dc"}, {"name": "1x __components::e12a669ffa0346a27198bed32e63b7ba", "param_count": null, "params": [], "start": **********.557699, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e12a669ffa0346a27198bed32e63b7ba.blade.php__components::e12a669ffa0346a27198bed32e63b7ba", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe12a669ffa0346a27198bed32e63b7ba.blade.php&line=1", "ajax": false, "filename": "e12a669ffa0346a27198bed32e63b7ba.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e12a669ffa0346a27198bed32e63b7ba"}, {"name": "1x __components::9d0b20db301db9a47503a93a879bb206", "param_count": null, "params": [], "start": **********.562207, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d0b20db301db9a47503a93a879bb206.blade.php__components::9d0b20db301db9a47503a93a879bb206", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d0b20db301db9a47503a93a879bb206.blade.php&line=1", "ajax": false, "filename": "9d0b20db301db9a47503a93a879bb206.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9d0b20db301db9a47503a93a879bb206"}, {"name": "1x __components::b985af7bcdacbeac70eaf3979ad19f5a", "param_count": null, "params": [], "start": **********.566078, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b985af7bcdacbeac70eaf3979ad19f5a.blade.php__components::b985af7bcdacbeac70eaf3979ad19f5a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb985af7bcdacbeac70eaf3979ad19f5a.blade.php&line=1", "ajax": false, "filename": "b985af7bcdacbeac70eaf3979ad19f5a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b985af7bcdacbeac70eaf3979ad19f5a"}, {"name": "1x __components::a0bb1d43b71cff86abe626fd376492e9", "param_count": null, "params": [], "start": **********.571516, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a0bb1d43b71cff86abe626fd376492e9.blade.php__components::a0bb1d43b71cff86abe626fd376492e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa0bb1d43b71cff86abe626fd376492e9.blade.php&line=1", "ajax": false, "filename": "a0bb1d43b71cff86abe626fd376492e9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a0bb1d43b71cff86abe626fd376492e9"}, {"name": "1x __components::e25f2b305e6de46c04f91fa1ce50f68f", "param_count": null, "params": [], "start": **********.575996, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e25f2b305e6de46c04f91fa1ce50f68f.blade.php__components::e25f2b305e6de46c04f91fa1ce50f68f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe25f2b305e6de46c04f91fa1ce50f68f.blade.php&line=1", "ajax": false, "filename": "e25f2b305e6de46c04f91fa1ce50f68f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e25f2b305e6de46c04f91fa1ce50f68f"}, {"name": "1x __components::69152f707ea1358f8997b77a28e38a6f", "param_count": null, "params": [], "start": **********.579771, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/69152f707ea1358f8997b77a28e38a6f.blade.php__components::69152f707ea1358f8997b77a28e38a6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F69152f707ea1358f8997b77a28e38a6f.blade.php&line=1", "ajax": false, "filename": "69152f707ea1358f8997b77a28e38a6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69152f707ea1358f8997b77a28e38a6f"}, {"name": "1x __components::b85eba35d3b7929c2988678b725baebf", "param_count": null, "params": [], "start": **********.582676, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b85eba35d3b7929c2988678b725baebf.blade.php__components::b85eba35d3b7929c2988678b725baebf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb85eba35d3b7929c2988678b725baebf.blade.php&line=1", "ajax": false, "filename": "b85eba35d3b7929c2988678b725baebf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b85eba35d3b7929c2988678b725baebf"}, {"name": "1x __components::1f3d3b83c612f68036b4d79d53ae851e", "param_count": null, "params": [], "start": **********.58648, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1f3d3b83c612f68036b4d79d53ae851e.blade.php__components::1f3d3b83c612f68036b4d79d53ae851e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1f3d3b83c612f68036b4d79d53ae851e.blade.php&line=1", "ajax": false, "filename": "1f3d3b83c612f68036b4d79d53ae851e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1f3d3b83c612f68036b4d79d53ae851e"}, {"name": "1x __components::f143d1296cea16d82e2c87956e445593", "param_count": null, "params": [], "start": **********.59508, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/f143d1296cea16d82e2c87956e445593.blade.php__components::f143d1296cea16d82e2c87956e445593", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Ff143d1296cea16d82e2c87956e445593.blade.php&line=1", "ajax": false, "filename": "f143d1296cea16d82e2c87956e445593.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f143d1296cea16d82e2c87956e445593"}, {"name": "1x __components::8cae4e5056b67c6778a54389a62ac7a0", "param_count": null, "params": [], "start": **********.598203, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8cae4e5056b67c6778a54389a62ac7a0.blade.php__components::8cae4e5056b67c6778a54389a62ac7a0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8cae4e5056b67c6778a54389a62ac7a0.blade.php&line=1", "ajax": false, "filename": "8cae4e5056b67c6778a54389a62ac7a0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8cae4e5056b67c6778a54389a62ac7a0"}, {"name": "1x __components::d5e509b6eb9084ec382ec05ccab41d1a", "param_count": null, "params": [], "start": **********.60112, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d5e509b6eb9084ec382ec05ccab41d1a.blade.php__components::d5e509b6eb9084ec382ec05ccab41d1a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd5e509b6eb9084ec382ec05ccab41d1a.blade.php&line=1", "ajax": false, "filename": "d5e509b6eb9084ec382ec05ccab41d1a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d5e509b6eb9084ec382ec05ccab41d1a"}, {"name": "1x __components::e81a46563ed9378aa4d9a4fcb55e743e", "param_count": null, "params": [], "start": **********.608551, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e81a46563ed9378aa4d9a4fcb55e743e.blade.php__components::e81a46563ed9378aa4d9a4fcb55e743e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe81a46563ed9378aa4d9a4fcb55e743e.blade.php&line=1", "ajax": false, "filename": "e81a46563ed9378aa4d9a4fcb55e743e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81a46563ed9378aa4d9a4fcb55e743e"}, {"name": "1x __components::da3c3de008e5793cbbdad005d78f49b1", "param_count": null, "params": [], "start": **********.618531, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/da3c3de008e5793cbbdad005d78f49b1.blade.php__components::da3c3de008e5793cbbdad005d78f49b1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fda3c3de008e5793cbbdad005d78f49b1.blade.php&line=1", "ajax": false, "filename": "da3c3de008e5793cbbdad005d78f49b1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::da3c3de008e5793cbbdad005d78f49b1"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.621174, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.622858, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.625515, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.626693, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.628345, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::system.license-invalid", "param_count": null, "params": [], "start": **********.629363, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/license-invalid.blade.phpcore/base::system.license-invalid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Flicense-invalid.blade.php&line=1", "ajax": false, "filename": "license-invalid.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.license-invalid"}, {"name": "2x 8def1252668913628243c4d363bee1ef::alert", "param_count": null, "params": [], "start": **********.630619, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/alert.blade.php8def1252668913628243c4d363bee1ef::alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::alert"}, {"name": "1x core/base::system.partials.license-activation-modal", "param_count": null, "params": [], "start": **********.631881, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/partials/license-activation-modal.blade.phpcore/base::system.partials.license-activation-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Fpartials%2Flicense-activation-modal.blade.php&line=1", "ajax": false, "filename": "license-activation-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.partials.license-activation-modal"}, {"name": "1x 8def1252668913628243c4d363bee1ef::license.form", "param_count": null, "params": [], "start": **********.63324, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/license/form.blade.php8def1252668913628243c4d363bee1ef::license.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flicense%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::license.form"}, {"name": "1x __components::27ec08f706fece52ef1cc0ec5563cef9", "param_count": null, "params": [], "start": **********.637657, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/27ec08f706fece52ef1cc0ec5563cef9.blade.php__components::27ec08f706fece52ef1cc0ec5563cef9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F27ec08f706fece52ef1cc0ec5563cef9.blade.php&line=1", "ajax": false, "filename": "27ec08f706fece52ef1cc0ec5563cef9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27ec08f706fece52ef1cc0ec5563cef9"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.helper-text", "param_count": null, "params": [], "start": **********.640864, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/helper-text.blade.php8def1252668913628243c4d363bee1ef::form.helper-text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fhelper-text.blade.php&line=1", "ajax": false, "filename": "helper-text.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.helper-text"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "param_count": null, "params": [], "start": **********.64518, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": **********.646071, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "1x ********************************::form-group", "param_count": null, "params": [], "start": **********.648375, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/setting/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form-group"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.651536, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::53362b6227831afe8e4d7d3436ab607f", "param_count": null, "params": [], "start": **********.659929, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/53362b6227831afe8e4d7d3436ab607f.blade.php__components::53362b6227831afe8e4d7d3436ab607f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F53362b6227831afe8e4d7d3436ab607f.blade.php&line=1", "ajax": false, "filename": "53362b6227831afe8e4d7d3436ab607f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53362b6227831afe8e4d7d3436ab607f"}, {"name": "1x __components::9e76aef074ac8ea84c711b8437720a22", "param_count": null, "params": [], "start": **********.661736, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9e76aef074ac8ea84c711b8437720a22.blade.php__components::9e76aef074ac8ea84c711b8437720a22", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9e76aef074ac8ea84c711b8437720a22.blade.php&line=1", "ajax": false, "filename": "9e76aef074ac8ea84c711b8437720a22.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9e76aef074ac8ea84c711b8437720a22"}, {"name": "1x __components::04edbddbda254d131a3439b11c880f12", "param_count": null, "params": [], "start": **********.663187, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/04edbddbda254d131a3439b11c880f12.blade.php__components::04edbddbda254d131a3439b11c880f12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F04edbddbda254d131a3439b11c880f12.blade.php&line=1", "ajax": false, "filename": "04edbddbda254d131a3439b11c880f12.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::04edbddbda254d131a3439b11c880f12"}, {"name": "1x 8def1252668913628243c4d363bee1ef::custom-template", "param_count": null, "params": [], "start": **********.664922, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/custom-template.blade.php8def1252668913628243c4d363bee1ef::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.665913, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x 8def1252668913628243c4d363bee1ef::loading", "param_count": null, "params": [], "start": **********.66763, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/loading.blade.php8def1252668913628243c4d363bee1ef::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::loading"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.checkbox", "param_count": null, "params": [], "start": **********.672022, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.676919, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x 8def1252668913628243c4d363bee1ef::debug-badge", "param_count": null, "params": [], "start": **********.049237, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/debug-badge.blade.php8def1252668913628243c4d363bee1ef::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::debug-badge"}, {"name": "1x __components::93ebde3601860db875cfe9a96164bda6", "param_count": null, "params": [], "start": **********.053827, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/93ebde3601860db875cfe9a96164bda6.blade.php__components::93ebde3601860db875cfe9a96164bda6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F93ebde3601860db875cfe9a96164bda6.blade.php&line=1", "ajax": false, "filename": "93ebde3601860db875cfe9a96164bda6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::93ebde3601860db875cfe9a96164bda6"}, {"name": "1x __components::16c15d36d71c18d83a7e9e7e1b68a92b", "param_count": null, "params": [], "start": **********.059177, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16c15d36d71c18d83a7e9e7e1b68a92b.blade.php__components::16c15d36d71c18d83a7e9e7e1b68a92b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16c15d36d71c18d83a7e9e7e1b68a92b.blade.php&line=1", "ajax": false, "filename": "16c15d36d71c18d83a7e9e7e1b68a92b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16c15d36d71c18d83a7e9e7e1b68a92b"}, {"name": "1x 8def1252668913628243c4d363bee1ef::layouts.base", "param_count": null, "params": [], "start": **********.061247, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/base.blade.php8def1252668913628243c4d363bee1ef::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.062862, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.066923, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.071626, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.074512, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.076011, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.011800000000000001, "accumulated_duration_str": "11.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.7808921, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 4.068}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.787668, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 4.068, "width_percent": 4.831}, {"sql": "select * from `user_meta` where `user_meta`.`user_id` = 1 and `user_meta`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 62}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 99}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 39}], "start": **********.796907, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 8.898, "width_percent": 3.644}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.804945, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 12.542, "width_percent": 4.407}, {"sql": "select exists(select `id`, `status`, `user_id`, `created_at`, `amount`, `tax_amount`, `shipping_amount`, `payment_id`, `ec_orders`.`store_id` from `ec_orders` where `is_finished` = 1) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 940}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 253}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/OrderController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\OrderController.php", "line": 80}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.847034, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "TableAbstract.php:940", "source": {"index": 11, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 940}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Ftable%2Fsrc%2FAbstracts%2FTableAbstract.php&line=940", "ajax": false, "filename": "TableAbstract.php", "line": "940"}, "connection": "martfury", "explain": null, "start_percent": 16.949, "width_percent": 7.627}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.9317682, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 24.576, "width_percent": 3.729}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 133}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 57}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.954671, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 28.305, "width_percent": 4.746}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 49}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 155}, {"index": 16, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.973661, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 33.051, "width_percent": 5}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.011252, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 38.051, "width_percent": 4.407}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 95}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.030025, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 42.458, "width_percent": 4.407}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 136}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.053871, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 46.864, "width_percent": 4.661}, {"sql": "select `name`, `id` from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/Concerns/HasFilters.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\table\\src\\Abstracts\\Concerns\\HasFilters.php", "line": 38}, {"index": 15, "namespace": "view", "name": "core/table::base-table", "file": "D:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.php", "line": 423}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.0877202, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OrderTable.php:244", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/OrderTable.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Tables\\OrderTable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTables%2FOrderTable.php&line=244", "ajax": false, "filename": "OrderTable.php", "line": "244"}, "connection": "martfury", "explain": null, "start_percent": 51.525, "width_percent": 5.508}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.180024, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1401", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1401", "ajax": false, "filename": "HookServiceProvider.php", "line": "1401"}, "connection": "martfury", "explain": null, "start_percent": 57.034, "width_percent": 7.712}, {"sql": "select * from `ec_orders` where (`status` = 'pending' and `is_finished` = 1) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.181856, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 64.746, "width_percent": 7.627}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (72, 73, 75, 76, 77, 78, 80, 81, 83)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.1896799, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 72.373, "width_percent": 15.339}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.195301, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.712, "width_percent": 8.39}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.343771, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1445", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1445", "ajax": false, "filename": "HookServiceProvider.php", "line": "1445"}, "connection": "martfury", "explain": null, "start_percent": 96.102, "width_percent": 3.898}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Order": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\ACL\\Models\\UserMeta": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUserMeta.php&line=1", "ajax": false, "filename": "UserMeta.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 22, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 22}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/admin/ecommerce/orders", "action_name": "orders.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index", "uri": "GET admin/ecommerce/orders", "controller": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FOrderController.php&line=76\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/orders", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FOrderController.php&line=76\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/OrderController.php:76-81</a>", "middleware": "web, core, auth", "duration": "2.39s", "peak_memory": "74MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-301032095 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-301032095\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1411840451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1411840451\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-848572535 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">https://martfury.gc/admin/branches/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3292 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; perf_dv6Tr4n=1; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ino1eThkNXUzQ1pJazVwN2JNU2RDK0E9PSIsInZhbHVlIjoiOHJ0VmZVWGN0UHhONUczdElBVXd3LzA0cEIzNU9GQlNBVFJIMHNlM1JNTGU4R1MzczAveXZydXd5V2luVXpjZ043YW96U2pwWE5DMEw0R2V3MTZyUm5QcEI1Y3huUW9qOU9YaXRVZ1AvKytmSll1akJITEFiZUJCMlE2S1h6QngxeUFyYkU3eHhNMGI0Uml4WGk1U2YxTGZGMFg3N1BYczZQNkcwelFaUmZlNHBOV3RuUGMxMlErRDhZVjB1NkVXbWZpQkI0TWRJQU94N1RabVlzQ2drcFppNDN3QktvRXg3RWRtMmRzT09pRT0iLCJtYWMiOiIxNThjNDhmZWFiMmNhNDg5MDUwNTk4YWNlMWJmMDJkMWM2ZTE1MDBhZjA4MWQ0NDlhYTg3ZmY4NmVjMGU0NmNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5LbWZjLzR3cUY3eU9MZXV1dXVhc1E9PSIsInZhbHVlIjoibHdyZnU1dGJnaFM5em56QkNLUmEzdklhZjRtS29YZnh4TDNaTlVibTF6bzBTUzFoNExHV0x5Y1RBVVlybEtvbTk0NzZrYnYrbFo1c0VBQWZzQ29BN1dVelZsVkY3Vzk1Qk9YQ1JKelhIdTRaeThFWVdyS0JYVTJNWWtoV1FpTVYiLCJtYWMiOiIwZDMzZDIwMjEwOTk0MTU3ZWE1NjRhMzdhYmZhNjYxODUxZTNkMTg2ZmFkYjZjZTU5ZmM5MGYxY2I1YmNjNzE0IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlpBUU1PQkdSZldWV3BFYzh0MVptc2c9PSIsInZhbHVlIjoidE84UjBqZSs5TW5lYWJuWjZVa2xVTWNuS2RNRDYxTmlldEJWUmFBMllHUUQ2M1IwM29xS3dwa3FKNzhxOUFRbFY4b1NnSEx5d3k5ZC9jNnVyejlZNy9KbU5XVS9kSEJacjBqRFJPbXdrYVgzUFpyS0d0cldtNmp1eTJHQ2poT1MiLCJtYWMiOiIwNjg5OTdjZWFkNjdiYWRiNzIyYTNhZDM1MGU0ZWUxNmE3YzIwYzI2Mjk2YTBiY2NjNGYwOGY4ZGEzYjI4OGNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-848572535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1685427616 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|Sn3NBIYxloIcTrlDVxihmhZmXItJIa758ohpdorvNUxb7S1o7MUYuQJ1LyIq|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b1OxVasdCwiSOi9WXdtWR3Fkrz0qo56rFJKPUT0x</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JEsXobfU6SUz9Lw1Nr1jP0CiYiLtAkzCp4vSdQSn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685427616\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-756294058 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 29 Aug 2025 15:59:42 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756294058\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-70209303 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b1OxVasdCwiSOi9WXdtWR3Fkrz0qo56rFJKPUT0x</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"69 characters\">https://martfury.gc/checkout/10ed7024f09405b8e39bbeef73d7bedb/success</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>2</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>7</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756483086\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756483086</span></span> {<a class=sf-dump-ref>#4417</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011410000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:01:37.205331 from now\nDST Off\">2025-08-29 15:58:06.883410 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>recently_viewed_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756482217\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756482217</span></span> {<a class=sf-dump-ref>#4418</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011420000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:16:06.473006 from now\nDST Off\">2025-08-29 15:43:37.615831 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>recently_viewed</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4419</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>a775bac9cff7dec2b984e023b95206aa</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4420</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">a775bac9cff7dec2b984e023b95206aa</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>3</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"14 characters\">Beat Headphone</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>20.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4421</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756482217\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756482217</span></span> {<a class=sf-dump-ref>#4422</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011460000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:16:06.473593 from now\nDST Off\">2025-08-29 15:43:37.615804 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756482217\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756482217</span></span> {<a class=sf-dump-ref>#4423</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011470000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 29, 2025\n- 00:16:06.473652 from now\nDST Off\">2025-08-29 15:43:37.615796 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>abandoned_cart_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>abandoned_cart_phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">03147552550</span>\"\n  \"<span class=sf-dump-key>abandoned_cart_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Branch 1</span>\"\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-num>1756482214</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70209303\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/admin/ecommerce/orders", "action_name": "orders.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\OrderController@index"}, "badge": null}}