@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ $branch->name }}</h4>
                </div>
                <div class="card-body">
                    @if($branch->description)
                    <div class="mb-3">
                        <h6>{{ trans('plugins/branch-management::branch.fields.description') }}</h6>
                        <p>{{ $branch->description }}</p>
                    </div>
                    @endif

                    @if($branch->image)
                    <div class="mb-3">
                        <h6>{{ trans('plugins/branch-management::branch.fields.image') }}</h6>
                        <img src="{{ RvMedia::getImageUrl($branch->image) }}" alt="{{ $branch->name }}" class="img-fluid" style="max-height: 300px;">
                    </div>
                    @endif



                    @if($branch->special_instructions)
                    <div class="mb-3">
                        <h6>{{ trans('plugins/branch-management::branch.special_instructions') }}</h6>
                        <div class="alert alert-info">{{ $branch->special_instructions }}</div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Contact Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>{{ trans('plugins/branch-management::branch.contact_information') }}</h5>
                </div>
                <div class="card-body">
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.address') }}:</strong><br>{{ $branch->full_address }}</p>
                    @if($branch->phone)
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.phone') }}:</strong><br>{{ $branch->phone }}</p>
                    @endif
                    @if($branch->email)
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.email') }}:</strong><br>{{ $branch->email }}</p>
                    @endif
                </div>
            </div>

            <!-- Manager Information -->
            @if($branch->manager_name || $branch->manager_phone || $branch->manager_email)
            <div class="card mb-3">
                <div class="card-header">
                    <h5>{{ trans('plugins/branch-management::branch.manager_information') }}</h5>
                </div>
                <div class="card-body">
                    @if($branch->manager_name)
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.manager_name') }}:</strong><br>{{ $branch->manager_name }}</p>
                    @endif
                    @if($branch->manager_phone)
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.manager_phone') }}:</strong><br>{{ $branch->manager_phone }}</p>
                    @endif
                    @if($branch->manager_email)
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.manager_email') }}:</strong><br>{{ $branch->manager_email }}</p>
                    @endif
                </div>
            </div>
            @endif

            <!-- Pickup Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>{{ trans('plugins/branch-management::branch.pickup_information') }}</h5>
                </div>
                <div class="card-body">
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.is_pickup_available') }}:</strong><br>
                        @if($branch->is_pickup_available)
                            <span class="badge bg-success">{{ __('Yes') }}</span>
                        @else
                            <span class="badge bg-danger">{{ __('No') }}</span>
                        @endif
                    </p>

                </div>
            </div>

            <!-- Operating Hours -->
            @if($branch->operating_hours)
            <div class="card mb-3">
                <div class="card-header">
                    <h5>{{ trans('plugins/branch-management::branch.operating_hours') }}</h5>
                </div>
                <div class="card-body">
                    <p>{!! nl2br(e($branch->operating_hours)) !!}</p>
                </div>
            </div>
            @endif

            <!-- Location -->
            @if($branch->latitude && $branch->longitude)
            <div class="card">
                <div class="card-header">
                    <h5>{{ trans('plugins/branch-management::branch.location_information') }}</h5>
                </div>
                <div class="card-body">
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.latitude') }}:</strong> {{ $branch->latitude }}</p>
                    <p><strong>{{ trans('plugins/branch-management::branch.fields.longitude') }}:</strong> {{ $branch->longitude }}</p>
                    <a href="https://maps.google.com/?q={{ $branch->latitude }},{{ $branch->longitude }}" target="_blank" class="btn btn-primary btn-sm">
                        <i class="fa fa-map"></i> {{ __('View on Map') }}
                    </a>
                </div>
            </div>
            @endif
        </div>
    </div>
@endsection
