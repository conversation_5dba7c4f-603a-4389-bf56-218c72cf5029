const mix = require('laravel-mix');

const path = require('path');
let directory = path.basename(path.resolve(__dirname));

const source = 'platform/plugins/' + directory;
const dist = 'public/vendor/core/plugins/' + directory;

mix
    .js(source + '/resources/assets/js/branch-management.js', dist + '/js')
    .js(source + '/resources/assets/js/branch-product.js', dist + '/js')
    .js(source + '/resources/assets/js/branch-checkout.js', dist + '/js')
    .sass(source + '/resources/assets/sass/branch-management.scss', dist + '/css')

if (mix.inProduction()) {
    mix
        .copy(dist + '/js/branch-management.js', source + '/public/js')
        .copy(dist + '/js/branch-product.js', source + '/public/js')
        .copy(dist + '/js/branch-checkout.js', source + '/public/js')
        .copy(dist + '/css/branch-management.css', source + '/public/css')
}
